.gk-graph {
	.form-control {
		border: 1px solid var(--section-border);
		background-color: var(--input__bg);
		color: var(--text-normal);

		&::placeholder {
			color: var(--text-disabled);
		}

		&:focus {
			outline: 0;
			border-color: var(--form-control-focus);
			box-shadow: 0 0 0 1px var(--form-control-focus);
		}
	}

	// don't allow bootstrap to make inputs green with .has-success, since .has-success is used
	// on some standard inputs where the border color should remain blue
	.has-success .form-control:focus {
		outline: 0;
		border-color: var(--form-control-focus);
		box-shadow: 0 0 0 1px var(--form-control-focus);
	}

	input[type='date'].input-xs,
	input[type='time'].input-xs,
	input[type='datetime-local'].input-xs,
	input[type='month'].input-xs {
		line-height: 22px;
	}

	input[type='checkbox'],
	input[type='radio'] {
		margin: 0px;
	}

	.input-xs,
	.form-horizontal .form-group-xs .form-control {
		height: 22px;
		padding: 5px 5px;
		font-size: var(--fs-2);
		line-height: 1.5;
		border-radius: 3px;
	}

	label {
		font-weight: var(--text-normal);
	}
	select.input-xs {
		height: 22px;
		line-height: 22px;
	}
	textarea.input-xs,
	select[multiple].input-xs {
		height: auto;
	}

	.input-group-xs > .form-control,
	.input-group-xs > .input-group-addon,
	.input-group-xs > .input-group-btn > .btn {
		height: 22px;
		padding: 5px 5px;
		font-size: var(--fs-2);
		line-height: 1.5;
		border-radius: 3px;
	}
	select.input-group-xs > .form-control,
	select.input-group-xs > .input-group-addon,
	select.input-group-xs > .input-group-btn > .btn {
		height: 22px;
		line-height: 22px;
	}
	textarea.input-group-xs > .form-control,
	textarea.input-group-xs > .input-group-addon,
	textarea.input-group-xs > .input-group-btn > .btn,
	select[multiple].input-group-xs > .form-control,
	select[multiple].input-group-xs > .input-group-addon,
	select[multiple].input-group-xs > .input-group-btn > .btn {
		height: auto;
	}

	.help-block {
		color: var(--text-secondary);
		font-size: var(--fs-2);
	}

	/* modal overrides */
	.modal-dialog {
		margin-top: calc(var(--toolbar-height) + var(--title-bar-height));
	}

	/* button overrides */
	button.btn:focus,
	button.btn:active,
	.btn:focus.btn:active,
	.btn:hover {
		outline: none !important;
		color: var(--btn-text-hover);
		&:not(.btn-link):not(.btn-default):not(.hover-icon):not(.noncommercial-link):not(.toolbar-btn):not(.disabled) {
			color: var(--btn-text-hover) !important;
		}
		&.btn-default {
			color: var(--text-selected);
		}
	}
	.btn[disabled]:not(.toolbar-btn) {
		color: var(--text-normal);
		&:hover {
			color: var(--text-normal) !important;
		}
	}
	.noncommercial-link:hover {
		color: var(--text-normal) !important;
	}

	/* tab overrides */
	.nav-tabs > li > a {
		margin-right: 0px;
	}
	.nav-tabs > li.active > a,
	.nav-tabs > li.active > a:hover,
	.nav-tabs > li.active > a:focus {
		color: inherit;
		border: 1px solid #fff;
		border-bottom-color: transparent;
	}
	.nav-tabs > li > a:hover {
		border-color: transparent;
	}

	/* alert overrides */
	.alert-info {
		color: #d9edf7;
		background-color: #31708f;
		border-color: #174f6b;
	}

	/* tooltip overrides */
	.tooltip-inner {
		word-wrap: break-word;
		max-width: 500px;
	}

	.tooltip.wide .tooltip-inner {
		max-width: 400px;
	}

	.tooltip {
		/* Make it so the tooltip can show over the toastr message */
		z-index: 1000000;
	}

	.tooltip.no-max-width .tooltip-inner {
		max-width: none;
		text-align: left;
	}

	/* popovers */
	.gk-popover {
		font-family: var(--font-default);
		.arrow {
			border-right-color: var(--section-border) !important; // override bootstrap
			&:after {
				border-right-color: var(--section-border) !important; // override bootstrap
			}
		}
		.popover-content {
			padding: 0px;
		}
	}

	/* markdown */

	/* overrides to keep bootstrap from rendering super large text in markdown */
	.markdown {
		h1,
		h2,
		h3,
		h4,
		h5,
		h6 {
			font-weight: var(--semi-bold);
			line-height: 1.2;
			margin-top: 1.5rem;
			margin-bottom: 1rem;
		}

		h1 {
			font-size: var(--fs-7);
		}
		h2 {
			font-size: var(--fs-6);
		}
		h3 {
			font-size: var(--fs-5);
		}
		h4 {
			font-size: var(--fs-4);
		}
		h5 {
			font-size: var(--fs-3);
		}
		h6 {
			font-size: var(--fs-2);
		}

		blockquote {
			font-size: var(--fs-3);
			border-left: 4px solid var(--text-disabled);
			padding: 1rem 1.5rem;
		}
	}
}
