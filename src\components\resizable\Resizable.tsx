import classnames from 'classnames';
import type { <PERSON>leStyles, ResizeCallback, ResizeStartCallback } from 're-resizable';
import { Resizable as ResizableCore } from 're-resizable';
import type { ReactElement, ReactNode } from 'react';
import React from 'react';
import type { Style } from '../../domain/generic/GenericTypes';
import type { TranslationFn } from '../../domain/language/LanguageTypes';
import type { OnResize, OnResizeEnd, OnResizeFromPropChange, OnResizeStart, OnWheel } from '../../domain/ui/UiTypes';
import { clamp } from '../../utils/MathUtils';

const resizableEdges = {
	bottom: 'bottom',
	left: 'left',
	right: 'right',
	top: 'top',
};

// re-reslect accepts an object for selectively enabling and disabling resizablity on
// certain edges. By default, each is enabled, so we construct a helper object which
// disables all of edges and turn on only the edge we want.
const resizeHandleEdges = {
	top: false,
	right: false,
	bottom: false,
	left: false,
	topRight: false,
	bottomRight: false,
	bottomLeft: false,
	topLeft: false,
};

const defaultConstraint = {
	min: 0,
	max: Infinity,
};

type Constraints = {
	min: number;
	max: number;
};

interface DimensionalConstraints {
	heightConstraints: Constraints;
	widthConstraints: Constraints;
}

interface Dimensions {
	height: number;
	width: number;
}

type ResizableEdges = keyof typeof resizableEdges;

type DefaultProps = {
	heightConstraints: Constraints;
	className: string;
	enable: boolean;
	resizeContentClassName: string;
	resizeHandleClassName: string;
	style: Style;
	wrapperStyle: Style;
	translate: TranslationFn;
	wrapChildrenInDivContainer?: boolean;
	widthConstraints: Constraints;
};

type Props = {
	children?: ReactNode;
	className: string;
	enable?: boolean;
	handleStyles?: HandleStyles;
	height: number;
	heightConstraints: Constraints;
	id?: string;
	onResize?: OnResize;
	onResizeEnd?: OnResizeEnd;
	onResizeFromPropChange?: OnResizeFromPropChange;
	onResizeStart?: OnResizeStart;
	resizeContentClassName: string;
	resizeEdge: ResizableEdges;
	resizeHandleClassName: string;
	style: Style;
	wrapperStyle: Style;
	translate: TranslationFn;
	width?: number;
	wrapChildrenInDivContainer?: boolean;
	widthConstraints: Constraints;
};

interface State extends Dimensions {
	enable: boolean;
	horizontal: boolean;
	initialHeight: number;
	initialWidth: number;
	resizing: boolean;
	vertical: boolean;
	isMouseWheeling: boolean;
}

interface ClampDimensionsProps extends DimensionalConstraints, Dimensions {}

type ClampDimensions = (props: ClampDimensionsProps) => Dimensions;
const clampDimensions: ClampDimensions = ({ height, heightConstraints, width, widthConstraints }) => ({
	height: clamp(height, heightConstraints.min, heightConstraints.max),
	width: clamp(width, widthConstraints.min, widthConstraints.max),
});

type CalculateDimensions = (
	deltas: {
		deltaWidth: number;
		deltaHeight: number;
	},
	props: Props,
	state: State,
) => Dimensions;

const calculateDimensions: CalculateDimensions = (
	{ deltaWidth, deltaHeight },
	{ heightConstraints, widthConstraints },
	{ initialHeight, initialWidth },
) =>
	clampDimensions({
		height: initialHeight + deltaHeight,
		heightConstraints: heightConstraints,
		width: initialWidth + deltaWidth,
		widthConstraints: widthConstraints,
	});

type GetStateFromProps = (props: Props) => State;
const getStateFromProps: GetStateFromProps = ({
	enable,
	height,
	heightConstraints,
	resizeEdge,
	width = 0,
	widthConstraints,
}) => ({
	...clampDimensions({
		height: height,
		widthConstraints: widthConstraints,
		heightConstraints: heightConstraints,
		width: width,
	}),
	enable: enable === undefined ? true : enable,
	horizontal: resizeEdge === resizableEdges.left || resizeEdge === resizableEdges.right,
	initialHeight: height,
	initialWidth: width,
	resizing: false,
	isMouseWheeling: false,
	vertical: resizeEdge === resizableEdges.top || resizeEdge === resizableEdges.bottom,
});

export default class Resizable extends React.PureComponent<Props, State> {
	static defaultProps: DefaultProps = {
		className: '',
		enable: true,
		heightConstraints: defaultConstraint,
		resizeContentClassName: '',
		resizeHandleClassName: '',
		style: {},
		wrapperStyle: {},
		translate: () => '',
		wrapChildrenInDivContainer: true,
		widthConstraints: defaultConstraint,
	};

	onWheelTimeOut?: NodeJS.Timeout;
	isMouseWheeling = false;

	constructor(props: Props) {
		super(props);

		this.state = getStateFromProps(props);
	}

	static getDerivedStateFromProps(props: Props, state: State) {
		if (state.resizing) {
			return state;
		}
		return getStateFromProps(props);
	}

	override componentDidUpdate(nextProps: Props) {
		const { onResizeFromPropChange: originalOnResizeFromPropChange } = this.props;
		const { height: originalHeight, resizing, width: originalWidth } = this.state;
		const { onResizeFromPropChange: newOnResizeFromPropChange } = nextProps;

		if (resizing) {
			return;
		}

		const { height, width } = this.state;
		const onResizeFromPropChange = newOnResizeFromPropChange || originalOnResizeFromPropChange;

		if (onResizeFromPropChange && (height !== originalHeight || width !== originalWidth)) {
			onResizeFromPropChange({
				height: height,
				width: width,
				originalHeight: originalHeight,
				originalWidth: originalWidth,
			});
		}
	}

	_onResize: ResizeCallback = (_event, _dir, _ref, { height: deltaHeight, width: deltaWidth }) => {
		const { onResize } = this.props;
		const { height: originalHeight, horizontal, vertical, width: originalWidth } = this.state;

		const { height, width } = calculateDimensions(
			{ deltaWidth: deltaWidth, deltaHeight: deltaHeight },
			this.props,
			this.state,
		);
		const widthChanged: boolean = horizontal && width !== originalWidth;
		const heightChanged: boolean = vertical && height !== originalHeight;

		if (!widthChanged && !heightChanged) {
			return;
		}

		this.setState({
			height: heightChanged ? height : originalHeight,
			width: widthChanged ? width : originalWidth,
			isMouseWheeling: false,
		});

		if (onResize) {
			onResize({
				height: heightChanged ? height : undefined,
				width: widthChanged ? width : undefined,
			});
		}
	};

	_onResizeStart: ResizeStartCallback = event => {
		event.preventDefault();
		event.stopPropagation();

		const { onResizeStart } = this.props;
		const { height, horizontal, vertical, width } = this.state;

		this.setState({
			resizing: true,
		});

		if (onResizeStart) {
			onResizeStart({
				height: vertical ? height : undefined,
				width: horizontal ? width : undefined,
			});
		}
	};

	_onResizeEnd: ResizeCallback = () => {
		const { onResizeEnd } = this.props;
		const { height, horizontal, vertical, width, initialWidth, initialHeight } = this.state;

		this.setState({
			initialWidth: width,
			initialHeight: height,
			resizing: false,
		});

		if (initialWidth !== width || initialHeight !== height) {
			if (onResizeEnd) {
				onResizeEnd({
					width: horizontal ? width : undefined,
					height: vertical ? height : undefined,
				});
			}
		}
	};

	_onWheel: OnWheel = () => {
		if (!this.isMouseWheeling) {
			this.isMouseWheeling = true;
			this.setState({ isMouseWheeling: true });
		}

		clearTimeout(this.onWheelTimeOut);
		this.onWheelTimeOut = setTimeout(() => {
			this.isMouseWheeling = false;
			this.setState({ isMouseWheeling: false });
		}, 50);
	};

	override render(): ReactElement<'div'> {
		const {
			children,
			className,
			handleStyles,
			id,
			resizeContentClassName,
			resizeEdge,
			resizeHandleClassName,
			style,
			translate,
			heightConstraints,
			wrapChildrenInDivContainer,
			widthConstraints,
		} = this.props;

		const { enable, height, horizontal, resizing, vertical, width, isMouseWheeling } = this.state;

		const { display, ...normalizedStyle } = style;

		const { min: minWidth, max: maxWidth } = widthConstraints;
		const { min: minHeight, max: maxHeight } = heightConstraints;

		const wrapperStyle = { ...this.props.wrapperStyle };

		if (horizontal) {
			wrapperStyle.width = width;
			wrapperStyle.minWidth = width;
		}

		if (vertical) {
			wrapperStyle.height = height;
			wrapperStyle.minHeight = height;
		}

		const resizeClasses: string = classnames('resizable', className, {
			resizing: resizing,
		});

		const resizeContentClasses: string = classnames(
			'contents',
			resizeContentClassName,
			`resize-edge-${resizeEdge}`,
			{
				horizontal: horizontal,
				vertical: vertical,
			},
		);

		const resizeHandleClasses: string = classnames(resizeEdge, resizeHandleClassName, 'resizable-handle', {
			horizontal: horizontal,
			vertical: vertical,
		});

		const Handle: ReactElement<'span'> = <span title={translate('ResizePanel')} />;

		return (
			<div
				data-testid={`resizable-${id || ''}`}
				onWheel={this._onWheel}
				style={{ ...wrapperStyle, display: display }}
			>
				<ResizableCore
					className={resizeClasses}
					data-testid={`resizable-core-${id || ''}`}
					enable={{ ...resizeHandleEdges, [resizeEdge]: !isMouseWheeling && enable }}
					handleClasses={{ [resizeEdge]: resizeHandleClasses }}
					handleComponent={{ [resizeEdge]: Handle }}
					handleStyles={handleStyles}
					maxHeight={vertical ? maxHeight : undefined}
					maxWidth={horizontal ? maxWidth : undefined}
					minHeight={vertical ? minHeight : undefined}
					minWidth={horizontal ? minWidth : undefined}
					onResize={this._onResize}
					onResizeStart={this._onResizeStart}
					onResizeStop={this._onResizeEnd}
					size={{
						height: vertical ? height : '100%',
						width: horizontal ? width : '100%',
					}}
					style={normalizedStyle}
				>
					{wrapChildrenInDivContainer ? (
						<div className={resizeContentClasses} style={{ userSelect: 'none' }}>
							{children}
						</div>
					) : (
						children
					)}
				</ResizableCore>
			</div>
		);
	}
}
