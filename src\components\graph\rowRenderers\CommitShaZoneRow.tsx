import type { <PERSON>ridCellProps, GridCell<PERSON>enderer } from 'react-virtualized';
import type { ReactElement } from 'react';
import React from 'react';
import { mergeNodeType, workDirType } from '../../../domain/commit/CommitConstants';
import * as CommitHelpers from '../../../domain/commit/CommitHelpers';
import type { ShortSha } from '../../../domain/commit/CommitTypes';
import type { GraphZoneType } from '../../../domain/graph/GraphConstants';
import { commitShaZone } from '../../../domain/graph/GraphConstants';
import { GraphRowHelper } from '../../../domain/graph/GraphHelpers';
import type {
	CommonGraphRowDispatchProps,
	CommonGraphRowProps,
	GraphItemContext,
	ProcessedGraphRow,
} from '../../../domain/graph/GraphTypes';
import type { GetRealKeyForCellFunc } from '../../../domain/reactvirtualized/ReactVirtualizedHelpers';
import GenericGraphZone from '../common/GenericGraphZone';

interface OwnProps {
	shaLength?: number | null;
}

interface Props extends CommonGraphRowProps, CommonGraphRowDispatchProps, OwnProps {}

const makeCommitShaZoneRowRenderer = (props: Props, getKeyForCell: GetRealKeyForCellFunc): GridCellRenderer => {
	const {
		processedRows,
		getExternalIcon,
		highlightRowsOnRefHover,
		isInUnsupportedRebase,
		numGraphColumns,
		clearCurrentlyHoveredGraphCommit,
		currentlyHoveredCommitSha,
		dimMergeCommits,
		dimRowsOfSelectedCommit,
		onCommitContextMenu,
		onClickCommit,
		onDoubleClickCommit,
		setAsCurrentlyHoveredGraphCommit,
		shaLength,
		suppressNonRefRowTooltips,
	} = props;

	const rowHelper: GraphRowHelper = new GraphRowHelper(props);

	const rowRenderer = ({ rowIndex: index, style }: GridCellProps): ReactElement<any> | undefined => {
		const key: string = getKeyForCell(index);

		const graphRow: ProcessedGraphRow = processedRows[index];
		const { sha, type, contexts } = graphRow;
		const shortSha: ShortSha = CommitHelpers.getShortSha(sha, shaLength);
		const context: GraphItemContext | undefined = contexts?.sha || undefined;
		const rowContext: GraphItemContext | undefined = contexts?.row || undefined;
		const zoneType: GraphZoneType = commitShaZone;
		const showColorStrip = rowHelper.isColumnFollowingGraphColumn(zoneType);
		const isLastColumn = rowHelper.isLastColumn(zoneType);
		const zoneWidth = rowHelper.getZoneWidth(zoneType);
		const verticalScrollWidth: number = rowHelper.getVerticalScrollWidth(zoneType);

		return (
			<GenericGraphZone
				clearCurrentlyHoveredGraphCommit={clearCurrentlyHoveredGraphCommit}
				column={graphRow.column}
				columnForColoring={graphRow.columnForColoring}
				context={context}
				currentlyHoveredCommitSha={currentlyHoveredCommitSha}
				dimRowsOfSelectedCommit={dimRowsOfSelectedCommit}
				getExternalIcon={getExternalIcon}
				graphZoneType={zoneType}
				highlightRowsOnRefHover={highlightRowsOnRefHover}
				isDimmedMergeCommitRow={type === mergeNodeType && dimMergeCommits}
				isHighlighted={rowHelper.isHighlighted(index)}
				isHovering={rowHelper.isHovering(index)}
				isInUnsupportedRebase={isInUnsupportedRebase}
				isLastColumn={isLastColumn}
				isMissingHoveredRefGroup={rowHelper.isMissingHoveredRefGroup(index)}
				isSelected={rowHelper.isSelected(index)}
				key={key}
				numGraphColumns={numGraphColumns}
				onClickCommit={onClickCommit}
				onContextMenu={onCommitContextMenu}
				onDoubleClickCommit={onDoubleClickCommit}
				rowContext={rowContext}
				setAsCurrentlyHoveredGraphCommit={setAsCurrentlyHoveredGraphCommit}
				sha={sha}
				showColorStrip={showColorStrip}
				showTimeline={rowHelper.hasTimeline(index)}
				style={style}
				type={type}
				verticalScrollWidth={verticalScrollWidth}
				zoneWidth={zoneWidth}
			>
				{type !== workDirType ? (
					<span className="font-monospace pointer" title={suppressNonRefRowTooltips ? undefined : shortSha}>
						{shortSha}
					</span>
				) : null}
			</GenericGraphZone>
		);
	};

	return rowRenderer;
};

export default makeCommitShaZoneRowRenderer;
