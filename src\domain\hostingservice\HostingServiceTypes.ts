export type GitHubDotComHostingServiceType = 'github';
export type GithubEnterpriseHostingServiceType = 'githubEnterprise';
export type GitLabHostingServiceType = 'gitlab';
export type GitLabSelfHostingServiceType = 'gitlabSelfHosted';
export type AzureDevopsHostingServiceType = 'azureDevops';
export type BitBucketHostingServiceType = 'bitbucket';
export type BitBucketServerHostingServiceType = 'bitbucketServer';

export type HostingServiceType =
	| GitHubDotComHostingServiceType
	| GithubEnterpriseHostingServiceType
	| BitBucketHostingServiceType
	| BitBucketServerHostingServiceType
	| GitLabHostingServiceType
	| GitLabSelfHostingServiceType
	| AzureDevopsHostingServiceType;

export type AzureDevopsIssueTrackerType = AzureDevopsHostingServiceType;
export type GitHubIssueTrackerType = GitHubDotComHostingServiceType;
export type GithubEnterpriseIssueTrackerType = GithubEnterpriseHostingServiceType;
export type GitLabIssueTrackerType = GitLabHostingServiceType;
export type GitLabSelfHostedIssueTrackerType = GitLabSelfHostingServiceType;
export type JiraCloudIssueTrackerType = 'jiraCloud';
export type JiraServerIssueTrackerType = 'jiraServer';
export type TrelloIssueTrackerType = 'trello';
export type BitbucketIssueTrackerType = 'bitbucket';

export type IssueTrackerType =
	| GitHubIssueTrackerType
	| GithubEnterpriseIssueTrackerType
	| GitLabIssueTrackerType
	| GitLabSelfHostedIssueTrackerType
	| JiraCloudIssueTrackerType
	| JiraServerIssueTrackerType
	| TrelloIssueTrackerType
	| AzureDevopsIssueTrackerType
	| BitbucketIssueTrackerType;

export function getHostingServiceName(hostingServiceType: HostingServiceType): string {
	switch (hostingServiceType) {
		case 'github':
			return 'GitHub';
		case 'githubEnterprise':
			return 'GitHub Enterprise';
		case 'gitlab':
			return 'GitLab';
		case 'gitlabSelfHosted':
			return 'GitLab Self-Hosted';
		case 'azureDevops':
			return 'Azure DevOps';
		case 'bitbucket':
			return 'Bitbucket';
		case 'bitbucketServer':
			return 'Bitbucket Server';
		default:
			return hostingServiceType;
	}
}
