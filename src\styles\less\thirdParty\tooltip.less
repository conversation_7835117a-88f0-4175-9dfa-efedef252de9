/*!
* Bootstrap v3.4.1 (https://getbootstrap.com/)
* Copyright 2011-2019 Twitter, Inc.
* Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
*/
.gk-graph .tooltip,
.gk-graph.tooltip {
  position: absolute;
  z-index: 1070;
  display: block;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-style: normal;
  font-weight: 400;
  line-height: 1.42857143;
  line-break: auto;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  word-spacing: normal;
  word-wrap: normal;
  white-space: normal;
  font-size: 12px;
  filter: alpha(opacity=0);
  opacity: 0;

  &.show {
    filter: alpha(opacity=90);
    opacity: 0.9;
  }

  &.bs-tooltip-top {
    padding: 5px 0;
    margin-top: -3px;
  }

  &.bs-tooltip-right {
    padding: 0 5px;
    margin-left: 3px;
  }

  &.bs-tooltip-bottom {
    padding: 5px 0;
    margin-top: 3px;
  }

  &.bs-tooltip-left {
    padding: 0 5px;
    margin-left: -3px;
  }

  .tooltip-arrow {
    position: absolute;
    z-index: 1071;
    pointer-events: none;
  }

  .tooltip-arrow:after {
    content: '';
    position: absolute;
    width: 5px;
    height: 5px;
    background-color: var(--vscode-editorHoverWidget-background);
    border-right: 1px solid var(--vscode-editorHoverWidget-border);
    border-bottom: 1px solid var(--vscode-editorHoverWidget-border);
  }

  &.bs-tooltip-right .tooltip-arrow {
    left: -7px;
  }

  &.bs-tooltip-left .tooltip-arrow {
    right: 7px;
  }

  &.bs-tooltip-bottom .tooltip-arrow {
    top: -7px;
  }

  &.bs-tooltip-top .tooltip-arrow {
    bottom: 7px;
  }

  &.bs-tooltip-right .tooltip-arrow:after {
    transform: rotate(135deg);
  }

  &.bs-tooltip-left .tooltip-arrow:after {
    transform: rotate(315deg);
  }

  &.bs-tooltip-bottom .tooltip-arrow:after {
    transform: rotate(225deg);
  }

  &.bs-tooltip-top .tooltip-arrow:after {
    transform: rotate(45deg);
  }
}

.gk-graph {
  .tooltip-inner {
    max-width: 200px;
    padding: 3px 8px;
    color: #fff;
    text-align: center;
    background-color: #000;
    border-radius: 4px;
  }
}
