import type { IGraphContainer } from '../../../components/graph/IGraphContainer';
import type { CommitType, Sha } from '../../commit/CommitTypes';
import { refTypes } from '../../ref/RefConstants';
import type { TimelineEntry } from '../GraphConstants';
import { isGraphRefActive } from '../GraphHelpers';
import type {
	ChildRefsByType,
	GraphRef,
	GraphRefGroup,
	GraphRefGroupsByName,
	GraphRefsData,
	GraphRow,
	Head,
	ProcessedGraphRow,
	Remote,
	RowContexts,
	RowEdges,
	Tag,
} from '../GraphTypes';

// TODO: move everything related with the processed graph row here.

const refImportanceByType = {
	[refTypes.WORKTREE]: 3,
	[refTypes.HEAD]: 2,
	[refTypes.REMOTE]: 1,
	[refTypes.TAG]: 0,
};

export class ProcessedGraphRowObj implements ProcessedGraphRow {
	private _graph: IGraphContainer;

	private _message: string;
	private _heads: Head[];
	private _remotes: Remote[];
	private _tags: Tag[];

	private _activeGraphRef?: GraphRef;
	private _activeGraphRefGroup?: GraphRefGroup;
	private _refGroupsByName: GraphRefGroupsByName;
	private _orderedRefGroups: GraphRefGroup[];

	sha: Sha;
	parents: Sha[];
	author: string;
	email: string;
	date: number;
	type: CommitType;
	contexts?: RowContexts;
	rowIndex?: number;
	column: number;
	columnForColoring?: number;
	edgeColumnMaxes: number;
	edges: RowEdges;
	childRefs: ChildRefsByType;
	timeLineEntry?: TimelineEntry;

	summary: string;
	body?: string;

	displayMessage: string;
	displaySummary: string;
	displayBody?: string;

	set message(message: string) {
		if (this._message !== message) {
			this._message = message;
			this.displayMessage = this._graph.formatCommitMessageCallback(message);
			this.updateSummaryAndBody(message);
		}
	}

	get message(): string {
		return this._message;
	}

	get heads(): Head[] {
		return this._heads;
	}

	set heads(heads: Head[]) {
		if (this._heads !== heads) {
			this._heads = heads;
			this.initializeRefGroups();
		}
	}

	set remotes(remotes: Remote[]) {
		if (this._remotes !== remotes) {
			this._remotes = remotes;
			this.initializeRefGroups();
		}
	}

	get remotes(): Remote[] {
		return this._remotes;
	}

	set tags(tags: Tag[]) {
		if (this._tags !== tags) {
			this._tags = tags;
			this.initializeRefGroups();
		}
	}

	get tags(): Tag[] {
		return this._tags;
	}

	get hasRefs(): boolean {
		return this.heads.length > 0 || this.remotes.length > 0 || this.tags.length > 0;
	}

	get hasChildRefs(): boolean {
		return this.childRefs.heads.length > 0 || this.childRefs.remotes.length > 0 || this.childRefs.tags.length > 0;
	}

	get refsData(): GraphRefsData | undefined {
		if (!this.hasRefs && !this.hasChildRefs) {
			return undefined;
		}

		if (this._orderedRefGroups.length === 0) {
			this.loadGraphRefGroupsData();
		}

		return {
			activeGraphRef: this._activeGraphRef,
			activeGraphRefGroup: this._activeGraphRefGroup,
			orderedRefGroups: this._orderedRefGroups,
			refGroupsByName: this._refGroupsByName,
		};
	}

	constructor(
		graphContainer: IGraphContainer,
		row: GraphRow,
		column?: number,
		edgeColumnMaxes?: number,
		edges?: RowEdges,
		childRefs?: ChildRefsByType,
		timeLineEntry?: TimelineEntry,
		columnForColoring?: number,
	) {
		this._graph = graphContainer;

		this._heads = row.heads || [];
		this._remotes = row.remotes || [];
		this._tags = row.tags || [];
		this._message = row.message;

		this.sha = row.sha;
		this.parents = row.parents;
		this.author = row.author;
		this.email = row.email;
		this.date = row.date;
		this.type = row.type;
		this.contexts = row.contexts;
		this.column = column ? column : 0;
		this.columnForColoring = columnForColoring;
		this.edgeColumnMaxes = edgeColumnMaxes ? edgeColumnMaxes : 0;
		this.edges = edges || {};
		this.childRefs = childRefs || { heads: [], tags: [], remotes: [] };
		this.timeLineEntry = timeLineEntry;

		this.initializeRefGroups();
		this.updateSummaryAndBody(row.message);
	}

	private updateSummaryAndBody(commitMessage?: string): void {
		let summary = commitMessage ? commitMessage.trim() : '';
		let body;

		const index: number = summary.indexOf('\n');
		if (index !== -1) {
			body = summary
				.substring(index + 1)
				.trim()
				.replace(/\n+(?:\s+\n+)?/g, ' | ');
			summary = summary.substring(0, index);
		}

		this.body = body;
		this.summary = summary;

		this.displaySummary = this._graph.formatCommitMessageCallback(summary);
		this.displayBody = body ? this._graph.formatCommitMessageCallback(body) : undefined;
	}

	private initializeRefGroups(): void {
		this._activeGraphRefGroup = undefined;
		this._activeGraphRef = undefined;
		this._refGroupsByName = {};
		this._orderedRefGroups = [];
	}

	private addRefToRefGroup(graphRef: GraphRef): void {
		const refName = graphRef.name;
		if (!this._refGroupsByName[refName]) {
			this._refGroupsByName[refName] = [];
		}
		this._refGroupsByName[refName].push(graphRef);
	}

	private loadGraphRefGroupsData(): void {
		this.initializeRefGroups();

		const refGroups: GraphRefGroup[] = [];
		const { heads, tags, remotes } = this.hasRefs ? this : this.childRefs;

		// Heads
		for (const head of heads) {
			const graphRef: GraphRef = { ...head, refType: refTypes.HEAD };
			this.addRefToRefGroup(graphRef);

			if (isGraphRefActive(graphRef)) {
				this._activeGraphRefGroup = this._refGroupsByName[graphRef.name];
				this._activeGraphRef = graphRef;

				refGroups.unshift(this._refGroupsByName[graphRef.name]);
			} else {
				refGroups.push(this._refGroupsByName[graphRef.name]);
			}
		}

		// Remotes
		for (const remote of remotes) {
			const graphRef: GraphRef = {
				...remote,
				fullName: remote.owner ? `${remote.owner}/${remote.name}` : remote.name,
				refType: refTypes.REMOTE,
			};

			const isAlreadyGrouped = this._refGroupsByName[graphRef.name];
			this.addRefToRefGroup(graphRef);

			if (!isAlreadyGrouped) {
				refGroups.push(this._refGroupsByName[graphRef.name]);
			}
		}

		// Tags
		for (const tag of tags) {
			const graphRef: GraphRef = { ...tag, refType: refTypes.TAG };
			this.addRefToRefGroup(graphRef);
			refGroups.push(this._refGroupsByName[graphRef.name]);
		}

		// Order is important when displaying grouped refs of the graph
		this._orderedRefGroups = refGroups.sort((refA: GraphRefGroup, refB: GraphRefGroup) => {
			// At first priority, groups are sorted by isCurrentHead
			if (refA === this._activeGraphRefGroup) {
				return -1;
			} else if (refB === this._activeGraphRefGroup) {
				return 1;
			}

			// At second priority, groups are sorted by importance (local > remote > tag)
			if (refImportanceByType[refA[0].refType] > refImportanceByType[refB[0].refType]) {
				return -1;
			} else if (refImportanceByType[refA[0].refType] < refImportanceByType[refB[0].refType]) {
				return 1;
			}

			// At third priority, groups are sorted by agrupation (local + remotes) size of the nodes
			if (refA.length > refB.length) {
				return -1;
			} else if (refA.length < refB.length) {
				return 1;
			}

			// At fourth priority, groups are sorted by name
			return refA[0].name.localeCompare(refB[0].name);
		});
	}
}
