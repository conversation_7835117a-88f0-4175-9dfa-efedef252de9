# This workflow will run tests using node and then publish a package to GitHub Packages when a release is created
# For more information see: https://help.github.com/actions/language-and-framework-guides/publishing-nodejs-packages

name: Test build

on:
    push:
        branches: ['main']
    pull_request:
        branches: ['main']

jobs:
    build:
        runs-on: ubuntu-latest
        steps:
            - name: Code Checkout
              uses: actions/checkout@v4
            - name: Node Setup
              uses: actions/setup-node@v4
              with:
                  node-version: '22'
            - name: Install with Lock Check
              run: yarn --frozen-lockfile
            - name: Run build
              run: yarn build
            - name: Check Lint
              run: yarn lint
            - name: Run tests
              run: yarn test
