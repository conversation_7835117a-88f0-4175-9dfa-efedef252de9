import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import { CHANGES_BAR_MIN_WIDTH, CHANGES_BAR_RIGHT_MARGIN } from '../../../domain/graph/GraphConstants';
import type { GetExternalIcon, RowStats, RowStatsConstraints } from '../../../domain/graph/GraphTypes';

interface Props {
	constraints: RowStatsConstraints;
	getExternalIcon: GetExternalIcon;
	isLastColumn: boolean;
	isRowSelected: boolean;
	zoneWidth: number;
	stats: RowStats;
	verticalScrollWidth: number;
}

export default function ChangesBar(props: Props): ReactElement<'div'> {
	const {
		constraints: { max, min },
		stats: { files, additions, deletions },
		getExternalIcon,
		isLastColumn,
		isRowSelected,
		verticalScrollWidth,
		zoneWidth,
	} = props;
	const total = additions + deletions;
	const adjustedTotal = Math.max(min, Math.min(max, total));
	let rightMargin = isLastColumn ? verticalScrollWidth + CHANGES_BAR_RIGHT_MARGIN : CHANGES_BAR_RIGHT_MARGIN;
	// Add some additional margin for non-outlier rows so the outliers stand out more
	if (total <= adjustedTotal) {
		rightMargin += CHANGES_BAR_RIGHT_MARGIN;
	}

	const filesWidth = 52;
	const barWidth = Math.max(CHANGES_BAR_MIN_WIDTH, (adjustedTotal / max) * (zoneWidth - filesWidth - rightMargin));

	const additionWidth = (additions / total) * barWidth;
	const deletionWidth = (deletions / total) * barWidth;

	return (
		<div className={classnames('changes-zone', isRowSelected ? 'selected' : null)}>
			<span className="changes-files" style={{ width: filesWidth }}>
				<span className="mr1">{getExternalIcon('files')}</span>
				<span className="mr1">{files > 999 ? '999+' : files}</span>
			</span>

			<span className="changes-bar" style={{ width: barWidth }}>
				{additions ? <div className="changes-sub-bar added" style={{ width: additionWidth }} /> : null}
				{deletions ? <div className="changes-sub-bar deleted" style={{ width: deletionWidth }} /> : null}
			</span>
		</div>
	);
}
