import type { CssVariables } from '../generic/GenericTypes';
import { DEFAULT_CSS_VARIABLES } from './CssVariableConstants';

export function columnToColorPropName(column: number): string {
	return `--column-${column}-color`;
}

function mergeCssVariablesWithDefaults(cssVariables: CssVariables = {}): CssVariables {
	return {
		...DEFAULT_CSS_VARIABLES,
		...cssVariables,
	};
}

export function validateAndMergeCssVariablesWithDefaults(cssVariables: CssVariables = {}): CssVariables {
	const validatedCssVariables: CssVariables = {};
	Object.keys(cssVariables).forEach(key => {
		// validate column color
		if (key.includes('--graph-color-') || key.match(/^--column-\d+-color$/)) {
			if (CSS.supports('color', cssVariables[key])) {
				validatedCssVariables[key] = cssVariables[key];
			}
		} else {
			validatedCssVariables[key] = cssVariables[key];
		}
	});
	return mergeCssVariablesWithDefaults(validatedCssVariables);
}

// Derived from getCurrentCompiledThemeString in ThemeSelectors in GitKraken
export function getStyleStringFromCssVariables(cssVariables: CssVariables): string {
	const cssObject: CssVariables = cssVariables || DEFAULT_CSS_VARIABLES;
	let styleString = '';
	const cssFields: string[] = Object.keys(cssObject);
	styleString += '.gk-graph {\n';
	for (const key of cssFields) {
		styleString += `${key}: ${cssObject[key]};\n`;
	}

	styleString += '}\n';

	return styleString;
}
