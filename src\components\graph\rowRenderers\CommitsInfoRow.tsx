import type { ReactElement } from 'react';
import React from 'react';
import type { Style } from '../../../domain/generic/GenericTypes';
import type { GraphZoneType } from '../../../domain/graph/GraphConstants';
import type { GetExternalIcon } from '../../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';
import EntireRowSpan from '../common/EntireRowSpan';
import LoadingSpinner from '../common/LoadingSpinner';

type Props = {
	currentCommits: number;
	customFooterRow?: ReactElement<any>;
	getExternalIcon: GetExternalIcon;
	graphZoneType: GraphZoneType;
	hasMoreCommits: boolean;
	height: number;
	horizontalScrollHeight: number;
	isLoadingRows: boolean;
	scrollTop: number;
	style: Style;
	translate: TranslationFn;
};

class CommitsInfoRow extends React.Component<Props> {
	override render(): ReactElement<typeof EntireRowSpan> {
		const {
			currentCommits,
			customFooterRow,
			graphZoneType,
			hasMoreCommits,
			height,
			horizontalScrollHeight,
			getExternalIcon,
			isLoadingRows,
			scrollTop,
			style,
			translate,
		} = this.props;

		const loadingSpinner = (
			<LoadingSpinner className="loading-spinner" getExternalIcon={getExternalIcon} size={20} useSimpleSpinner />
		);

		const loadingInfoContent = (
			<div className="flex items-center">
				<span style={{ paddingRight: '5px' }}>{loadingSpinner}</span>
			</div>
		);

		const numCommits: number = currentCommits > 0 ? currentCommits : 0;
		const commitsInfoContent =
			numCommits === 0 && !hasMoreCommits ? <>{translate('Graph-NoCommits')}</> : customFooterRow;

		const rowContent = (
			<div className="graph-adjust-commit-count p1">
				{isLoadingRows ? loadingInfoContent : commitsInfoContent}
			</div>
		);

		return (
			<EntireRowSpan
				graphZoneType={graphZoneType}
				height={height}
				heightAdjustment={horizontalScrollHeight}
				scrollTop={scrollTop}
				style={style}
			>
				{rowContent}
			</EntireRowSpan>
		);
	}
}

export default CommitsInfoRow;
