import { expect } from 'chai';

import * as MathUtils from '../../../src/utils/MathUtils';

describe('Math Utils', function () {
	describe('clamp', function () {
		const testCases = [
			{ num: 1, lower: 2, upper: 3, result: 2 },
			{ num: 3, lower: 2, upper: 3, result: 3 },
			{ num: 3, lower: 2, upper: 4, result: 3 },
			{ num: 5, lower: 2, upper: 4, result: 4 },
			{ num: 5, lower: 5, upper: 6, result: 5 },
			{ num: 5, lower: 7, upper: 6, result: 7 },
		];

		testCases.forEach(testCase => {
			const { num, lower, upper, result } = testCase;

			it(`For num ${num}, lower ${lower} and upper ${upper}: It should return ${result}`, function () {
				expect(MathUtils.clamp(num, lower, upper)).to.equal(result);
			});
		});
	});
});
