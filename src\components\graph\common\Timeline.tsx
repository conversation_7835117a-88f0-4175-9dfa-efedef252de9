import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';

type TimelineProps = {
	z2?: true;
};
const timelineClasses: string = classnames('absolute', 'top-0', 'right-0', 'bottom-0', 'left-0', 'time-line');
const z2Classes: string = classnames(timelineClasses, 'z2');

function Timeline({ z2 }: TimelineProps): ReactElement<'div'> {
	return <div className={z2 ? z2Classes : timelineClasses} style={{ height: 2 }} />;
}

export default Timeline;
