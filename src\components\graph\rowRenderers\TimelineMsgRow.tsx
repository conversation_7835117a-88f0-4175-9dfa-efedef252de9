import type { <PERSON>ridCellProps, GridCell<PERSON>enderer } from 'react-virtualized';
import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import { GraphRowHelper, parseContext } from '../../../domain/graph/GraphHelpers';
import type {
	CommonGraphRowDispatchProps,
	CommonGraphRowProps,
	GraphItemContext,
	ProcessedGraphRow,
} from '../../../domain/graph/GraphTypes';
import type { GetRealKeyForCellFunc } from '../../../domain/reactvirtualized/ReactVirtualizedHelpers';
import Timeline from '../common/Timeline';

interface Props extends CommonGraphRowProps, CommonGraphRowDispatchProps {}

const makeTimelineMsgRowRenderer = (props: Props, getKeyForCell: GetRealKeyForCellFunc): GridCellRenderer => {
	const { translate, processedRows } = props;

	const baseClasses = classnames(
		'graph-row-wrapper',
		'grow-3',
		'height-100',
		'graph-zone-column',
		'min-width-0',
		'pb3',
		'pointer',
		'pt3',
		'relative',
	);

	const rowHelper: GraphRowHelper = new GraphRowHelper(props);

	const rowRenderer = ({ rowIndex: index, style }: GridCellProps): ReactElement<any> | null => {
		const key: string = getKeyForCell(index);

		const innerClasses = classnames('graph-row', 'height-100-percent', 'flex');

		const showTimeline: boolean = rowHelper.hasTimeline(index);

		const graphRow: ProcessedGraphRow = processedRows[index];
		const rowContext: GraphItemContext | undefined = graphRow.contexts?.row || undefined;

		const timeLineLabel: string = graphRow.timeLineEntry?.label || '';
		const timeLineValue: number = graphRow.timeLineEntry?.value || 0;

		const maybeTimeline: ReactElement<typeof Timeline> | null = showTimeline ? <Timeline /> : null;
		const maybeTimelineMessage: ReactElement<'span'> | null = showTimeline ? (
			<span className="time-line-message-container">
				<span className="time-line-message">{translate(timeLineLabel, timeLineValue)}</span>
			</span>
		) : null;

		return (
			<div className={baseClasses} data-vscode-context={parseContext(rowContext)} key={key} style={style}>
				{maybeTimeline}
				<div className={innerClasses} data-vscode-context={parseContext(rowContext)}>
					{maybeTimelineMessage}
				</div>
			</div>
		);
	};

	return rowRenderer;
};

export default makeTimelineMsgRowRenderer;
