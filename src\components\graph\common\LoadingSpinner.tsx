import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import type { GetExternalIcon } from '../../../domain/graph/GraphTypes';

export type LoadingSpinnerProps = {
	className?: string;
	getExternalIcon: GetExternalIcon;
	inline?: boolean;
	message?: string | null;
	size?: number;
	useSimpleSpinner?: boolean;
};

export default function LoadingSpinner({
	inline,
	getExternalIcon,
	message,
	size = 100,
	useSimpleSpinner,
}: LoadingSpinnerProps): ReactElement<any> {
	const wrapperClassName = classnames('loading-spinner', {
		'inline-block': inline,
		ml2: inline,
		'shrink-0': inline,
	});

	const spinnerClassName = classnames('spinner', 'mr1', {
		'inline-block': inline,
	});

	const spinnerSize = {
		height: size,
		width: size,
	};

	const messageClassName = classnames('message', {
		'inline-block': inline,
		mx2: inline,
	});

	const body = useSimpleSpinner ? (
		<span className={spinnerClassName} style={spinnerSize}>
			{getExternalIcon('loading')}
		</span>
	) : (
		<div className={spinnerClassName} style={spinnerSize}>
			<img className="ring outer" src="../../../images/spinner-outer-ring.svg" style={spinnerSize} />
			<img className="ring inner" src="../../../images/spinner-inner-ring.svg" style={spinnerSize} />
			<img className="bg-img" src="../../../images/spinner-kraken.svg" style={spinnerSize} />
		</div>
	);

	return (
		<div className={wrapperClassName}>
			{body}
			{message && <div className={messageClassName}>{message}</div>}
		</div>
	);
}
