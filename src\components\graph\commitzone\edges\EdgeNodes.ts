import { buildEdgeHash } from '../../../../domain/edge/EdgeHelpers';
import type { ColumnColorByColumn, EdgeCache, RowEdges, SvgElement } from '../../../../domain/graph/GraphTypes';
import { getEndingEdge } from './EndingEdge';
import { getPassThroughEdge } from './PassThroughEdge';
import { getStartingEdge } from './StartingEdge';

let edgeNodesCache: EdgeCache = {};

export function clearEdgeNodesCache() {
	edgeNodesCache = {};
}

// So for performance it's actually faster to create the SVG as a data-uri element and set that directly on the
// style of the div instead of creating react elements that have to go through reconciliation. Since this is a
// bottleneck for scrolling we're doing this but please don't copy this and do it in other areas of the app unless
// you really really *REALLY REALLY REALLY* have to.
export function getEdgeNodes(
	edges: RowEdges,
	edgeColumnMax: number,
	nodeColumn: number,
	columnColorByColumn: ColumnColorByColumn,
	columnWidth: number,
	gutterWidth: number,
	strokeWidth: number,
	isCompact?: boolean,
): SvgElement {
	const hash: string = buildEdgeHash(edges, edgeColumnMax, nodeColumn, isCompact);

	let edgeNode: SvgElement | undefined = edgeNodesCache[hash];
	if (edgeNode) {
		return edgeNode;
	}

	let edgeSvgString: SvgElement = '';
	for (let edgeColumn = 0; edgeColumn <= edgeColumnMax; edgeColumn += 1) {
		const edge = edges[edgeColumn];
		if (!edge) {
			continue;
		}

		const { passThrough, starting, ending } = edge;

		edgeSvgString += starting
			? getStartingEdge(
					edgeColumn,
					nodeColumn,
					starting.type,
					columnColorByColumn,
					columnWidth,
					gutterWidth,
					strokeWidth,
					isCompact,
			  )
			: '';
		edgeSvgString += passThrough
			? getPassThroughEdge(
					edgeColumn,
					passThrough.type,
					columnColorByColumn,
					columnWidth,
					gutterWidth,
					strokeWidth,
					isCompact,
			  )
			: '';
		edgeSvgString += ending
			? getEndingEdge(
					edgeColumn,
					nodeColumn,
					ending.type,
					columnColorByColumn,
					columnWidth,
					gutterWidth,
					strokeWidth,
					isCompact,
			  )
			: '';
	}

	edgeNode = `url('data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%"><g>${edgeSvgString}</g></svg>')`;

	edgeNodesCache[hash] = edgeNode;

	return edgeNode;
}
