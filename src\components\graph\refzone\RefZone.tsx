import classnames from 'classnames';
import type { ReactElement } from 'react';
import React, { createRef } from 'react';
import type { CommitType, Sha } from '../../../domain/commit/CommitTypes';
import type { HashMap, Style } from '../../../domain/generic/GenericTypes';
import { REF_ZONE_MARGIN_LEFT, REF_ZONE_MARGIN_RIGHT } from '../../../domain/graph/GraphConstants';
import { debounceFrame } from '../../../domain/graph/GraphHelpers';
import type {
	GetExternalIcon,
	GetMissingRefMetadata,
	GraphItemContext,
	GraphRefGroup,
	GraphRefOptData,
	IncludeOnlyRefsById,
	IncludeOnlyRemotesByName,
	OnClickRef,
	OnDoubleClickRef,
	OnRefBeginDrag,
	OnRefCanDrag,
	OnRefCanDrop,
	OnRefDragEnter,
	OnRefDragLeave,
	OnRefDrop,
	OnRefEndDrag,
	OnRefNodeHovered,
	OnRefNodeUnhovered,
	OnRefZoneContextMenu,
	OnRefZoneHovered,
	OnRefZoneUnhovered,
	OnToggleRefNodesShown,
	RefDndData,
	RefGroupContexts,
	RefMetadataById,
	RefMetadataType,
} from '../../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';
import type { RefIconsPosition } from '../../../domain/ref/RefTypes';
import RefLine from '../common/RefLine';
import Timeline from '../common/Timeline';
import OverflowCount from './OverflowCount';
import RefNodes from './RefNodes';

type RefZoneDefaultProps = {
	style: Style;
};

type RefZoneProps = {
	column: number;
	columnForColoring?: number;
	context: GraphItemContext | null;
	enableShowHideRefsOptions: boolean;
	showGhostRefsOnRowHover: boolean;
	getExternalIcon: GetExternalIcon;
	hasActive: boolean;
	hasRefs: boolean;
	hasTimeline: boolean;
	hoveredRefGroup: GraphRefGroup | null;
	hoveredRefZoneSha?: Sha;
	includeOnlyRefsById: IncludeOnlyRefsById;
	includeOnlyRemotesByName: IncludeOnlyRemotesByName;
	isInUnsupportedRebase: boolean;
	isSingleSelected: boolean;
	numGraphColumns: number;
	onClickRef: OnClickRef;
	onDoubleClickRef: OnDoubleClickRef;
	onMissingRefMetadata: GetMissingRefMetadata;
	onToggleRefNodesShown: OnToggleRefNodesShown;
	onRefBeginDrag: OnRefBeginDrag;
	onRefCanDrag: OnRefCanDrag;
	onRefCanDrop: OnRefCanDrop;
	onRefDragEnter: OnRefDragEnter;
	onRefDragLeave: OnRefDragLeave;
	onRefDrop: OnRefDrop;
	onRefEndDrag: OnRefEndDrag;
	refGroupContexts: RefGroupContexts | null;
	refGroupsByName: GraphRefGroup[];
	refIconsPosition: RefIconsPosition;
	refMetadata: RefMetadataById | null;
	refNodeHovered: OnRefNodeHovered;
	refNodeUnhovered: OnRefNodeUnhovered;
	refZoneHovered: OnRefZoneHovered;
	refZoneUnhovered: OnRefZoneUnhovered;
	sha: Sha;
	shouldShowRefLine: boolean;
	showColorStrip?: boolean;
	showContextMenuForGroupedRef: OnRefZoneContextMenu;
	showRemoteNamesOnRefs: boolean;
	enabledRefMetadataTypes: RefMetadataType[];
	style: Style;
	translate: TranslationFn;
	type: CommitType;
	useColumnHeaderIcons: boolean;
	width: number;
};

const refZoneWrapperClassNameCache: HashMap = {};

function getRefZoneWrapperClassName(
	type: CommitType,
	column: number,
	isHovered: boolean,
	hasActive: boolean,
	numGraphColumns: number,
	dimRef: boolean,
): string {
	const hash = `${type}${column}${isHovered}${hasActive}${numGraphColumns}${dimRef}`;

	let refZoneWrapperClassName: string | undefined = refZoneWrapperClassNameCache[hash];
	if (refZoneWrapperClassName) {
		return refZoneWrapperClassName;
	}

	refZoneWrapperClassName = classnames(
		// lists in less are 1-indexed based instead of 0 so this is compensation.
		`column-${(column % numGraphColumns) + 1}`,
		'flex',
		'pb3',
		'pt3',
		'ref-zone',
		'relative',
		type,
		{
			'dim-ref': dimRef,
			'has-active': hasActive,
			z6: isHovered,
			z1: !isHovered,
		},
	);

	refZoneWrapperClassNameCache[hash] = refZoneWrapperClassName;

	return refZoneWrapperClassName;
}

export default class RefZone extends React.PureComponent<RefZoneProps> {
	popoverTargetRef = createRef<HTMLDivElement>();

	static defaultProps: RefZoneDefaultProps = {
		style: {},
	};

	// TODO: should be better tested
	override componentDidUpdate(nextProps: RefZoneProps) {
		if (
			!this.props.hasRefs &&
			this.props.isSingleSelected &&
			!nextProps.isSingleSelected &&
			this.props.hoveredRefZoneSha === this.props.sha
		) {
			this.props.refZoneUnhovered();
			if (this.props.hoveredRefGroup) {
				this.props.refNodeUnhovered(undefined, this.props.hoveredRefGroup, this.props.sha);
			}
		}
	}

	onCustomToggleRefNodesShown: OnToggleRefNodesShown = (
		event: React.MouseEvent<any>,
		refs: GraphRefOptData[],
		visible: boolean,
		sha?: Sha,
	): void => {
		this.props.onToggleRefNodesShown(event, refs, visible, sha);
		this.props.refZoneUnhovered();
	};

	override render(): ReactElement<any> {
		const {
			enabledRefMetadataTypes,
			column,
			columnForColoring,
			context,
			enableShowHideRefsOptions,
			showGhostRefsOnRowHover,
			hasActive,
			hasRefs,
			hasTimeline,
			hoveredRefGroup,
			hoveredRefZoneSha,
			getExternalIcon,
			includeOnlyRefsById,
			includeOnlyRemotesByName,
			isInUnsupportedRebase,
			numGraphColumns,
			onClickRef,
			onDoubleClickRef,
			onMissingRefMetadata,
			onRefBeginDrag,
			onRefCanDrag,
			onRefCanDrop,
			onRefDragEnter,
			onRefDragLeave,
			onRefDrop,
			onRefEndDrag,
			refGroupContexts,
			refGroupsByName,
			refIconsPosition,
			refMetadata,
			refNodeHovered,
			refNodeUnhovered,
			refZoneHovered,
			refZoneUnhovered,
			sha,
			shouldShowRefLine,
			showColorStrip,
			showContextMenuForGroupedRef,
			showRemoteNamesOnRefs,
			style,
			translate,
			type,
			useColumnHeaderIcons,
			width: refZoneWidth,
		} = this.props;

		const isHovered: boolean = hoveredRefZoneSha === sha;

		const widthStyles: Style = {
			minWidth: refZoneWidth,
			width: refZoneWidth,
		};

		const wrapperStyle: Style = {
			...style,
			...widthStyles,
		};

		const maybeTimeline: ReactElement<typeof Timeline> | null = hasTimeline ? <Timeline /> : null;

		const groupCount: number = refGroupsByName.length;
		const onRefZoneHovered = (event: React.MouseEvent<any>) => {
			refZoneHovered(sha);
			if (groupCount) {
				refNodeHovered(event, refGroupsByName[0], sha);
			} else if (hoveredRefGroup) {
				refNodeUnhovered(event, hoveredRefGroup, sha);
			}
		};

		const onRefZoneUnhovered = (event: React.MouseEvent<any>) => {
			refZoneUnhovered();
			if (hoveredRefGroup) {
				refNodeUnhovered(event, hoveredRefGroup, sha);
			}
		};

		// Copied from GKC "DragDropHelpers.js" (beginDrag) but used "debounceFrame" instead of "setTimeout".
		// When a ref is dragged we want to collapse all expanded ref groups since they might be eclipsing
		// drop targets. However, collapsing the ref group may remove the ref that has begun dragging.
		// For that reason we have to wait a tick before collapsing the ref group so "DndComponent" can attach
		// its drag events (which happens after beginDrag) to the ref node before it's removed.
		const onInternalRefBeginDrag = debounceFrame((event: React.DragEvent, sourceRefData?: RefDndData): void => {
			onRefZoneUnhovered(event);
			onRefBeginDrag(event, sourceRefData);
		});

		const onInternalRefDragEnter = (
			event: React.DragEvent,
			sourceRefData?: RefDndData,
			targetRefData?: RefDndData,
		): void => {
			if (!isHovered && targetRefData?.sha === sha) {
				onRefZoneHovered(event);
			}

			onRefDragEnter(event, sourceRefData, targetRefData);
		};

		const dimRef: boolean = groupCount > 0 && !hasRefs && showGhostRefsOnRowHover;
		const refZoneWrapperClassName = getRefZoneWrapperClassName(
			type,
			columnForColoring ?? column,
			isHovered,
			hasActive,
			numGraphColumns,
			dimRef,
		);

		const colorStripStyle: Style = { width: 2 };
		const colorStrip: ReactElement<'div'> = <div className="color-strip height-100" style={colorStripStyle} />;

		if (!groupCount) {
			return (
				<div
					className={refZoneWrapperClassName}
					onMouseEnter={onRefZoneHovered}
					onWheel={onRefZoneUnhovered}
					style={wrapperStyle}
				>
					{showColorStrip ? colorStrip : null}
					{maybeTimeline}
				</div>
			);
		}

		const refNodesWrapperStyle: Style = {
			minWidth: 0,
			overflow: 'hidden',
			width: refZoneWidth - REF_ZONE_MARGIN_RIGHT,
		};

		const overflowCount = groupCount - 1;
		const maybeOverflowCountComponent: ReactElement<typeof OverflowCount> | null =
			overflowCount > 0 && !isHovered ? (
				<OverflowCount hasActive={hasActive} overflowCount={overflowCount} />
			) : null;

		return (
			<div
				className={refZoneWrapperClassName}
				onMouseEnter={onRefZoneHovered}
				onMouseLeave={onRefZoneUnhovered}
				onWheel={onRefZoneUnhovered}
				style={wrapperStyle}
			>
				<div ref={this.popoverTargetRef} />
				{showColorStrip ? colorStrip : null}
				{maybeTimeline}
				<div className="flex" style={refNodesWrapperStyle}>
					<RefNodes
						column={column}
						columnForColoring={columnForColoring}
						context={context}
						enabledRefMetadataTypes={enabledRefMetadataTypes}
						enableShowHideRefsOptions={enableShowHideRefsOptions}
						getExternalIcon={getExternalIcon}
						hasActive={hasActive}
						hasRefs={hasRefs}
						includeOnlyRefsById={includeOnlyRefsById}
						includeOnlyRemotesByName={includeOnlyRemotesByName}
						isGhostRef={dimRef}
						isHovered={isHovered}
						isInUnsupportedRebase={isInUnsupportedRebase}
						numGraphColumns={numGraphColumns}
						onClickRef={onClickRef}
						onDoubleClickRef={onDoubleClickRef}
						onMissingRefMetadata={onMissingRefMetadata}
						onRefBeginDrag={onInternalRefBeginDrag}
						onRefCanDrag={onRefCanDrag}
						onRefCanDrop={onRefCanDrop}
						onRefDragEnter={onInternalRefDragEnter}
						onRefDragLeave={onRefDragLeave}
						onRefDrop={onRefDrop}
						onRefEndDrag={onRefEndDrag}
						onToggleRefNodesShown={this.onCustomToggleRefNodesShown}
						refGroupContexts={refGroupContexts}
						refGroupsByName={refGroupsByName}
						refIconsPosition={refIconsPosition}
						refMetadata={refMetadata}
						refNodeHovered={refNodeHovered}
						sha={sha}
						showContextMenuForGroupedRef={showContextMenuForGroupedRef}
						showRemoteNamesOnRefs={showRemoteNamesOnRefs}
						targetRef={this.popoverTargetRef}
						translate={translate}
						type={type}
						useColumnHeaderIcons={useColumnHeaderIcons}
					/>
					{maybeOverflowCountComponent}
				</div>
				{!hasRefs || !shouldShowRefLine ? null : (
					<div className="absolute left-0 flex z1" style={widthStyles}>
						<RefLine hasRefs isActiveSha={hasActive} left={REF_ZONE_MARGIN_LEFT} type={type} width="100%" />
					</div>
				)}
			</div>
		);
	}
}
