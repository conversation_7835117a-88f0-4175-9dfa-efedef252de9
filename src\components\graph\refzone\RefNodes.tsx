import classnames from 'classnames';
import type { CSSProperties, ReactElement, RefObject } from 'react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Overlay } from 'react-bootstrap';
import type { CommitType, Sha } from '../../../domain/commit/CommitTypes';
import {
	REF_NODE_ICON_SPACING,
	REF_NODE_OUTER_SPACING,
	REF_ZONE_MAX_REFS_TO_RENDER,
} from '../../../domain/graph/GraphConstants';
import { getRefIdByGraphRef, parseContext } from '../../../domain/graph/GraphHelpers';
import type {
	GetExternalIcon,
	GetMissingRefMetadata,
	GraphItemContext,
	GraphRef,
	GraphRefOptData,
	IncludeOnlyRefsById,
	IncludeOnlyRemotesByName,
	OnClickRef,
	OnDoubleClickRef,
	OnHideRefClick,
	OnRefBeginDrag,
	OnRefCanDrag,
	OnRefCanDrop,
	OnRefDragEnter,
	OnRefDragLeave,
	OnRefDrop,
	OnRefEndDrag,
	OnRefNodeHovered,
	OnRefZoneContextMenu,
	OnToggleRefNodesShown,
	GraphRefGroup as RefGroup,
	RefGroupContexts,
	RefMetadataById,
	RefMetadataType,
} from '../../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';
import { refTypes } from '../../../domain/ref/RefConstants';
import type { RefIconsPosition } from '../../../domain/ref/RefTypes';
import type { RefNodeProps } from './RefNode';
import RefNode from './RefNode';
import RefPopover from './RefPopover';

const getNewRefOverlayKey = (): string => `ref-overlay-container-${Math.random()}`;

type CommonRefNodeProps = Omit<
	RefNodeProps,
	'isActive' | 'isRefGroupIncluded' | 'key' | 'refGroup' | 'refGroupContext'
>;

type RefNodesProps = {
	onDoubleClickRef: OnDoubleClickRef;
	enabledRefMetadataTypes: RefMetadataType[];
	column: number;
	columnForColoring?: number;
	context: GraphItemContext | null;
	enableShowHideRefsOptions: boolean;
	getExternalIcon: GetExternalIcon;
	targetRef: RefObject<HTMLDivElement>;
	hasActive: boolean;
	hasRefs: boolean;
	includeOnlyRefsById: IncludeOnlyRefsById;
	includeOnlyRemotesByName: IncludeOnlyRemotesByName;
	isGhostRef: boolean;
	isHovered: boolean;
	isInUnsupportedRebase: boolean;
	numGraphColumns: number;
	onClickRef: OnClickRef;
	onMissingRefMetadata: GetMissingRefMetadata;
	refGroupContexts: RefGroupContexts | null;
	refGroupsByName: RefGroup[];
	refIconsPosition: RefIconsPosition;
	refMetadata: RefMetadataById | null;
	refNodeHovered: OnRefNodeHovered;
	onToggleRefNodesShown: OnToggleRefNodesShown;
	onRefBeginDrag: OnRefBeginDrag;
	onRefCanDrag: OnRefCanDrag;
	onRefCanDrop: OnRefCanDrop;
	onRefDragEnter: OnRefDragEnter;
	onRefDragLeave: OnRefDragLeave;
	onRefDrop: OnRefDrop;
	onRefEndDrag: OnRefEndDrag;
	sha: Sha;
	showRemoteNamesOnRefs: boolean;
	showContextMenuForGroupedRef: OnRefZoneContextMenu;
	translate: TranslationFn;
	type: CommitType;
	useColumnHeaderIcons: boolean;
};

function RefNodes({
	enabledRefMetadataTypes,
	column,
	columnForColoring,
	context,
	enableShowHideRefsOptions,
	targetRef,
	hasActive,
	hasRefs,
	getExternalIcon,
	includeOnlyRefsById,
	includeOnlyRemotesByName,
	isGhostRef,
	isHovered,
	isInUnsupportedRebase,
	numGraphColumns,
	onMissingRefMetadata,
	refGroupContexts,
	refGroupsByName,
	refIconsPosition,
	refMetadata,
	refNodeHovered,
	sha,
	showRemoteNamesOnRefs,
	showContextMenuForGroupedRef,
	onClickRef,
	onDoubleClickRef,
	onToggleRefNodesShown,
	onRefBeginDrag,
	onRefCanDrag,
	onRefCanDrop,
	onRefDragEnter,
	onRefDragLeave,
	onRefDrop,
	onRefEndDrag,
	translate,
	type,
	useColumnHeaderIcons,
}: RefNodesProps): ReactElement<'span' | 'div'> {
	const [overlayKey, setOverlayKey] = useState(getNewRefOverlayKey());

	useEffect(() => {
		// NOTES: This line force to re-render the overlay when `isHovered` property changes.
		// That is, this will force the overlay to readjust its positions.
		setOverlayKey(getNewRefOverlayKey());
	}, [isHovered]);

	const onHideRefHandler: OnHideRefClick = useCallback(
		(event, refGroup: RefGroup, currentSha: Sha) => {
			const refsOpts = refGroup.map<GraphRefOptData>((r: GraphRef) => ({
				id: getRefIdByGraphRef(r),
				name: r.name,
				type: r.refType,
				owner: r.owner,
				avatarUrl: r.avatarUrl,
			}));

			onToggleRefNodesShown(event, refsOpts, false, currentSha);
		},
		[onToggleRefNodesShown],
	);

	// Just render the first REF_ZONE_MAX_REFS_TO_RENDER refs
	const limitedRefGroups: RefGroup[] = useMemo(
		() =>
			refGroupsByName && refGroupsByName.length > REF_ZONE_MAX_REFS_TO_RENDER
				? refGroupsByName.slice(0, REF_ZONE_MAX_REFS_TO_RENDER)
				: refGroupsByName,
		[refGroupsByName],
	);

	// As the array should be in order, the active RefGroup should be the first element
	const activeRefGroup: RefGroup | null = limitedRefGroups.length > 0 ? limitedRefGroups[0] : null;

	const refGroups: RefGroup[] = useMemo(() => {
		if (isHovered) {
			return limitedRefGroups;
		}
		return activeRefGroup ? [activeRefGroup] : [];
	}, [activeRefGroup, isHovered, limitedRefGroups]);

	const refStyles: CSSProperties = useMemo(() => {
		const minIconSpacing =
			REF_NODE_OUTER_SPACING +
			(activeRefGroup?.length || 0) * REF_NODE_ICON_SPACING +
			(hasActive ? REF_NODE_ICON_SPACING : 0);
		return isHovered ? { height: 100, position: 'relative' } : { minWidth: minIconSpacing };
	}, [activeRefGroup, hasActive, isHovered]);

	const hasIncludesWithTags = useMemo(() => {
		const includeOnlyItems = Object.values(includeOnlyRefsById);
		const hasIncludes = includeOnlyItems.length > 0;
		return hasIncludes && includeOnlyItems.some((refOptData: any) => refOptData.type === refTypes.TAG);
	}, [includeOnlyRefsById]);

	// TODO: Those props should be memoized in the parent component side (and not here)
	const commonRefNodeProps: CommonRefNodeProps = useMemo(
		() => ({
			enabledRefMetadataTypes: enabledRefMetadataTypes,
			enableShowHideRefsOptions: enableShowHideRefsOptions,
			getExternalIcon: getExternalIcon,
			groupIsHovered: isHovered,
			hasActive: hasActive,
			hasRefs: hasRefs,
			isGhostRef: isGhostRef,
			isInUnsupportedRebase: isInUnsupportedRebase,
			onClick: onClickRef,
			onContextMenu: showContextMenuForGroupedRef,
			onDoubleClick: onDoubleClickRef,
			onHideRefClick: onHideRefHandler,
			onHovered: refNodeHovered,
			onMissingRefMetadata: onMissingRefMetadata,
			onRefBeginDrag: onRefBeginDrag,
			onRefCanDrag: onRefCanDrag,
			onRefCanDrop: onRefCanDrop,
			onRefDragEnter: onRefDragEnter,
			onRefDragLeave: onRefDragLeave,
			onRefDrop: onRefDrop,
			onRefEndDrag: onRefEndDrag,
			refIconsPosition: refIconsPosition,
			refMetadata: refMetadata,
			sha: sha,
			showRemoteNamesOnRefs: showRemoteNamesOnRefs,
			translate: translate,
			type: type,
			useColumnHeaderIcons: useColumnHeaderIcons,
		}),
		[
			enableShowHideRefsOptions,
			enabledRefMetadataTypes,
			getExternalIcon,
			hasActive,
			hasRefs,
			isGhostRef,
			isHovered,
			isInUnsupportedRebase,
			onClickRef,
			onDoubleClickRef,
			onHideRefHandler,
			onMissingRefMetadata,
			onRefBeginDrag,
			onRefCanDrag,
			onRefCanDrop,
			onRefDragEnter,
			onRefDragLeave,
			onRefDrop,
			onRefEndDrag,
			refIconsPosition,
			refMetadata,
			refNodeHovered,
			sha,
			showContextMenuForGroupedRef,
			showRemoteNamesOnRefs,
			translate,
			type,
			useColumnHeaderIcons,
		],
	);

	const refDataNodes: RefNodeProps[] = useMemo(
		() =>
			refGroups.map((refGroup: RefGroup) => {
				// As the array should be in order, the active GraphRef should be the first element
				const activeRef: GraphRef = refGroup[0];
				const refGroupContext: GraphItemContext | undefined = refGroupContexts?.[activeRef.name] || undefined;

				const isRefGroupIncluded: boolean = refGroup.some((ref: GraphRef) => {
					const refId: string = getRefIdByGraphRef(ref);
					switch (ref.refType) {
						case refTypes.TAG:
							return hasIncludesWithTags && Boolean(includeOnlyRefsById[refId]);
						case refTypes.REMOTE:
							return (
								Boolean(includeOnlyRefsById[refId]) ||
								(ref.owner && includeOnlyRemotesByName[ref.owner])
							);
						default:
							return Boolean(includeOnlyRefsById[refId]);
					}
				});

				return {
					...commonRefNodeProps,
					isActive: activeRef.isCurrentHead,
					isRefGroupIncluded: isRefGroupIncluded,
					key: `ref-node-${activeRef.fullName || activeRef.name}`,
					refGroup: refGroup,
					refGroupContext: refGroupContext,
				};
			}),
		[
			commonRefNodeProps,
			hasIncludesWithTags,
			includeOnlyRefsById,
			includeOnlyRemotesByName,
			refGroupContexts,
			refGroups,
		],
	);

	return (
		<span data-vscode-context={parseContext(context)} style={refStyles}>
			<div className={classnames({ 'display-none': isHovered })}>
				{refDataNodes.map((refDataNode: RefNodeProps) => (
					<RefNode {...refDataNode} key={refDataNode.key} />
				))}
			</div>
			<Overlay key={overlayKey} placement="right-start" show={isHovered} target={targetRef.current}>
				<RefPopover column={columnForColoring ?? column} numGraphColumns={numGraphColumns}>
					{refDataNodes.map((refDataNode: RefNodeProps) => (
						<li key={refDataNode.key}>
							<RefNode {...refDataNode} key={undefined} />
						</li>
					))}
				</RefPopover>
			</Overlay>
		</span>
	);
}

export default RefNodes;
