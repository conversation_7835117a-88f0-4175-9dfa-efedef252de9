import type { <PERSON>rid<PERSON>ell<PERSON><PERSON>, <PERSON>ridCell<PERSON><PERSON><PERSON> } from 'react-virtualized';
import type { ReactElement } from 'react';
import React from 'react';
import type { Sha } from '../../../domain/commit/CommitTypes';
import type { GraphZoneType } from '../../../domain/graph/GraphConstants';
import { refZone } from '../../../domain/graph/GraphConstants';
import { GraphRowHelper } from '../../../domain/graph/GraphHelpers';
import type {
	CommonGraphRowDispatchProps,
	CommonGraphRowProps,
	FormatRefShorthand,
	GetMissingRefMetadata,
	GraphItemContext,
	GraphRefGroup,
	IncludeOnlyRefsById,
	IncludeOnlyRemotesByName,
	IsRefShorthandValid,
	OnClickRef,
	OnDoubleClickRef,
	OnRefBeginDrag,
	OnRefCanDrag,
	OnRefCanDrop,
	OnRefCreate,
	OnRefCreateCancel,
	OnRefCreateContextMenu,
	OnRefDragEnter,
	OnRefDragLeave,
	OnRefDrop,
	OnRefEndDrag,
	OnRefNodeHovered,
	OnRefNodeUnhovered,
	OnRefShorthandChange,
	OnRefZoneContextMenu,
	OnRefZoneHovered,
	OnRefZoneUnhovered,
	OnToggleRefNodesShown,
	ProcessedGraphRow,
	RefGroupContexts,
	RefMetadataById,
} from '../../../domain/graph/GraphTypes';
import type { GetRealKeyForCellFunc } from '../../../domain/reactvirtualized/ReactVirtualizedHelpers';
import type { CreateRefFormData, RefIconsPosition } from '../../../domain/ref/RefTypes';
import CreateRefNode from '../refzone/CreateRefNode';
import RefZone from '../refzone/RefZone';

interface OwnProps {
	height: number;
	includeOnlyRefsById: IncludeOnlyRefsById;
	includeOnlyRemotesByName: IncludeOnlyRemotesByName;
}

interface StateProps {
	createRefFormData?: CreateRefFormData;
	formatRefShorthand: FormatRefShorthand;
	enableShowHideRefsOptions: boolean;
	isInUnsupportedRebase: boolean;
	isRefShorthandValid: IsRefShorthandValid;
	hoveredRefZoneSha?: Sha;
	refIconsPosition: RefIconsPosition;
	refMetadataById?: RefMetadataById | null;
	shouldShowRefLine: boolean;
}

interface DispatchProps {
	onClickRef: OnClickRef;
	onDoubleClickRef: OnDoubleClickRef;
	onMissingRefMetadata: GetMissingRefMetadata;
	onShowContextMenuForGroupedRef: OnRefZoneContextMenu;
	onRefBeginDrag: OnRefBeginDrag;
	onRefCanDrag: OnRefCanDrag;
	onRefCanDrop: OnRefCanDrop;
	onRefCreate: OnRefCreate;
	onRefCreateCancel: OnRefCreateCancel;
	onRefCreateContextMenu: OnRefCreateContextMenu;
	onRefDragEnter: OnRefDragEnter;
	onRefDragLeave: OnRefDragLeave;
	onRefDrop: OnRefDrop;
	onRefEndDrag: OnRefEndDrag;
	onRefNodeHovered: OnRefNodeHovered;
	onRefNodeUnhovered: OnRefNodeUnhovered;
	onRefShorthandChange: OnRefShorthandChange;
	onRefZoneHovered: OnRefZoneHovered;
	onRefZoneUnhovered: OnRefZoneUnhovered;
	onToggleRefNodesShown: OnToggleRefNodesShown;
}

interface Props extends CommonGraphRowProps, CommonGraphRowDispatchProps, OwnProps, StateProps, DispatchProps {}

const makeRefZoneRowRenderer = (props: Props, getKeyForCell: GetRealKeyForCellFunc): GridCellRenderer => {
	const {
		createRefFormData,
		formatRefShorthand,
		enableShowHideRefsOptions,
		enabledRefMetadataTypes,
		isRefShorthandValid,
		showGhostRefsOnRowHover,
		showRemoteNamesOnRefs,
		onDoubleClickRef,
		processedRows,
		includeOnlyRefsById,
		includeOnlyRemotesByName,
		isInUnsupportedRebase,
		getExternalIcon,
		hoveredRefGroup,
		hoveredRefZoneSha,
		numGraphColumns,
		onClickRef,
		onMissingRefMetadata,
		onShowContextMenuForGroupedRef,
		onRefBeginDrag,
		onRefCanDrag,
		onRefCanDrop,
		onRefCreate,
		onRefCreateCancel,
		onRefCreateContextMenu,
		onRefDragEnter,
		onRefDragLeave,
		onRefDrop,
		onRefEndDrag,
		onRefNodeHovered,
		onRefNodeUnhovered,
		onRefShorthandChange,
		onRefZoneHovered,
		onRefZoneUnhovered,
		onToggleRefNodesShown,
		refIconsPosition,
		refMetadataById,
		shouldShowRefLine,
		translate,
	} = props;

	const rowHelper: GraphRowHelper = new GraphRowHelper(props);

	const rowRenderer = ({ rowIndex: index, style }: GridCellProps): ReactElement<any> => {
		const key: string = getKeyForCell(index);

		const graphRow: ProcessedGraphRow = processedRows[index];
		const zoneType: GraphZoneType = refZone;
		const refZoneWidth: number = rowHelper.getZoneWidth(zoneType);
		const isHoveringRow = rowHelper.isHovering(index);
		const { sha, type, hasRefs, contexts } = graphRow;
		const context: GraphItemContext | undefined = contexts?.ref || undefined;
		const refGroupContexts: RefGroupContexts | undefined = contexts?.refGroups || undefined;
		const isSingleSelected: boolean = rowHelper.isSingleSelected(index);

		const refGroupsByName: GraphRefGroup[] =
			(showGhostRefsOnRowHover || hasRefs) && (hasRefs || isHoveringRow || isSingleSelected)
				? rowHelper.getGraphRefGroupsByNameForRow(index)
				: [];

		const showColorStrip = rowHelper.isColumnFollowingGraphColumn(zoneType);
		const useColumnHeaderIcons = rowHelper.shouldUseColumnHeaderIcons(zoneType);

		return createRefFormData?.sha === sha ? (
			<CreateRefNode
				createRefFormData={createRefFormData}
				formatRefShorthand={formatRefShorthand}
				isRefShorthandValid={isRefShorthandValid}
				key={key}
				onCancel={onRefCreateCancel}
				onContextMenu={onRefCreateContextMenu}
				onRefCreate={onRefCreate}
				onRefShorthandChange={onRefShorthandChange}
				refZoneWidth={refZoneWidth}
				style={style}
				translate={translate}
			/>
		) : (
			<RefZone
				column={graphRow.column}
				columnForColoring={graphRow.columnForColoring}
				context={context}
				enabledRefMetadataTypes={enabledRefMetadataTypes}
				enableShowHideRefsOptions={enableShowHideRefsOptions}
				getExternalIcon={getExternalIcon}
				hasActive={rowHelper.rowContainsCurrentHeadRef(graphRow)}
				hasRefs={hasRefs || false}
				hasTimeline={rowHelper.hasTimeline(index)}
				hoveredRefGroup={hoveredRefGroup}
				hoveredRefZoneSha={hoveredRefZoneSha}
				includeOnlyRefsById={includeOnlyRefsById}
				includeOnlyRemotesByName={includeOnlyRemotesByName}
				isInUnsupportedRebase={isInUnsupportedRebase}
				isSingleSelected={isSingleSelected}
				key={key}
				numGraphColumns={numGraphColumns}
				onClickRef={onClickRef}
				onDoubleClickRef={onDoubleClickRef}
				onMissingRefMetadata={onMissingRefMetadata}
				onRefBeginDrag={onRefBeginDrag}
				onRefCanDrag={onRefCanDrag}
				onRefCanDrop={onRefCanDrop}
				onRefDragEnter={onRefDragEnter}
				onRefDragLeave={onRefDragLeave}
				onRefDrop={onRefDrop}
				onRefEndDrag={onRefEndDrag}
				onToggleRefNodesShown={onToggleRefNodesShown}
				refGroupContexts={refGroupContexts}
				refGroupsByName={refGroupsByName}
				refIconsPosition={refIconsPosition}
				refMetadata={refMetadataById}
				refNodeHovered={onRefNodeHovered}
				refNodeUnhovered={onRefNodeUnhovered}
				refZoneHovered={onRefZoneHovered}
				refZoneUnhovered={onRefZoneUnhovered}
				sha={sha}
				shouldShowRefLine={shouldShowRefLine}
				showColorStrip={showColorStrip}
				showContextMenuForGroupedRef={onShowContextMenuForGroupedRef}
				showGhostRefsOnRowHover={showGhostRefsOnRowHover}
				showRemoteNamesOnRefs={showRemoteNamesOnRefs}
				style={style}
				translate={translate}
				type={type}
				useColumnHeaderIcons={useColumnHeaderIcons}
				width={refZoneWidth}
			/>
		);
	};

	return rowRenderer;
};

export default makeRefZoneRowRenderer;
