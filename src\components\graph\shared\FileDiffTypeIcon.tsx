import classNames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import * as DiffConstants from '../../../domain/diff/DiffConstants';
import type { Style } from '../../../domain/generic/GenericTypes';
import type { ExternalIconKeys, GetExternalIcon } from '../../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';

type Props = {
	style: Style;
	fileDiffType: string;
	getExternalIcon: GetExternalIcon;
	translate: TranslationFn;
};

const getFileDiffIcon = (fileDiffType: string): ExternalIconKeys | null => {
	switch (fileDiffType) {
		case DiffConstants.types.ADDED:
			return 'added';
		case DiffConstants.types.MODIFIED:
			return 'modified';
		case DiffConstants.types.DELETED:
			return 'deleted';
		case DiffConstants.types.RENAMED:
			return 'renamed';
		// TODO: Add the rest once supported
		// case DiffConstants.types.RESOLVED:
		// 	return 'resolved';
		// case DiffConstants.types.CONFLICT:
		// 	return 'warning';
		default:
			return null;
	}
};

const getIconColor = (fileDiffType: string): string | null => {
	switch (fileDiffType) {
		case DiffConstants.types.ADDED:
			return 'color-green';
		case DiffConstants.types.MODIFIED:
			return 'color-orange';
		case DiffConstants.types.DELETED:
			return 'color-red';
		case DiffConstants.types.RENAMED:
			return 'color-blue';
		// TODO: Add the rest once supported
		// case DiffConstants.types.RESOLVED:
		// 	return 'color-green';
		// case DiffConstants.types.CONFLICT:
		// 	return 'color-orange';
		default:
			return null;
	}
};

const getIconTitleKey = (fileDiffType: string): string | null => {
	switch (fileDiffType) {
		case DiffConstants.types.ADDED:
			return 'CommitDiffSection-FileAdded';
		case DiffConstants.types.MODIFIED:
			return 'CommitDiffSection-FileModified';
		case DiffConstants.types.DELETED:
			return 'CommitDiffSection-FileDeleted';
		case DiffConstants.types.RENAMED:
			return 'CommitDiffSection-FileRenamed';
		// TODO: Add the rest once supported
		// case DiffConstants.types.RESOLVED:
		// 	return 'CommitDiffSection-FileResolved';
		// case DiffConstants.types.CONFLICT:
		// 	return 'CommitDiffSection-FileConflict';
		default:
			return null;
	}
};

// NOTE this needs to be a class rather than a functional component so that FileNode can create a `ref` to it
class FileDiffTypeIcon extends React.Component<Props> {
	override render(): ReactElement<'span'> | null {
		const { fileDiffType, getExternalIcon, translate } = this.props;

		if (!fileDiffType) {
			return null;
		}

		const iconKey = getFileDiffIcon(fileDiffType);
		const iconColor = getIconColor(fileDiffType);
		const iconTitleKey = getIconTitleKey(fileDiffType);
		const iconClasses = classNames('fs-1', iconColor, 'mr1');

		return (
			<span className={iconClasses} title={translate(iconTitleKey)}>
				{iconKey && getExternalIcon(iconKey)}
			</span>
		);
	}
}

export default FileDiffTypeIcon;
