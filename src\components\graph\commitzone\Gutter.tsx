import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import Timeline from '../common/Timeline';
import { GutterBackgroundStreak } from './BackgroundStreak';

interface CommonGutterProps {
	boxShadowAlpha: number;
	width: number;
}

interface LeftGutterProps extends CommonGutterProps {
	hasTimeline: boolean;
	scrollLeft: number;
}

interface RightGutterProps extends LeftGutterProps {
	commitZoneWidth: number;
}

interface GutterProps extends CommonGutterProps {
	boxShadowLeft: number;
	children?: any;
	className?: string;
	left: number;
}

function Gutter({
	boxShadowAlpha,
	boxShadowLeft,
	children,
	className = '',
	left,
	width,
}: GutterProps): ReactElement<'div'> {
	return (
		<div
			className={classnames('absolute', 'top-0', 'bottom-0', 'z3', className)}
			style={{
				boxShadow: `${boxShadowLeft}px 5px 10px rgba(0, 0, 0, ${boxShadowAlpha})`,
				left: left,
				width: width,
			}}
		>
			{children}
		</div>
	);
}

export default Gutter;

export function LeftGutter({ boxShadowAlpha, hasTimeline, scrollLeft, width }: LeftGutterProps): ReactElement<any> {
	return (
		<Gutter
			boxShadowAlpha={boxShadowAlpha}
			boxShadowLeft={5}
			className="app-bg0 left-0"
			left={scrollLeft}
			width={width}
		>
			{hasTimeline ? <Timeline z2 /> : null}
		</Gutter>
	);
}

export function RightGutter({
	boxShadowAlpha,
	commitZoneWidth,
	hasTimeline,
	scrollLeft,
	width,
}: RightGutterProps): ReactElement<any> {
	return (
		<Gutter
			boxShadowAlpha={boxShadowAlpha}
			boxShadowLeft={-5}
			left={scrollLeft + commitZoneWidth - width}
			width={width}
		>
			{hasTimeline ? <Timeline z2 /> : null}
			<GutterBackgroundStreak />
		</Gutter>
	);
}
