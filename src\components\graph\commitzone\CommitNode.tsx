import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import { OverlayTrigger, Tooltip } from 'react-bootstrap';
import {
	commitNodeType,
	mergeConflictNodeType,
	mergeNodeType,
	stashNodeType,
	unsupportedRebaseWarningNodeType,
	workDirType,
} from '../../../domain/commit/CommitConstants';
import { getCommitMessageCoauthors, getCommitMessageDescription } from '../../../domain/commit/CommitHelpers';
import type { Author, CommitType } from '../../../domain/commit/CommitTypes';
import type { CssVariables } from '../../../domain/generic/GenericTypes';
import type { GetExternalIcon, GraphItemContext, GraphZoneModeConstants } from '../../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';
import { getNameWithEmail } from '../../../utils/TextFormatting';
import GraphAvatar from './GraphAvatar';

type Props = {
	authorEmail: string;
	authorInitials: string;
	authorName: string;
	avatarUrl?: string | null;
	column: number;
	context?: GraphItemContext | null;
	cssVariables: CssVariables;
	getExternalIcon: GetExternalIcon;
	graphZoneModeConstants: GraphZoneModeConstants;
	left: number;
	message: string;
	opacity: number;
	suppressNonRefRowTooltips?: boolean;
	translate: TranslationFn;
	type: CommitType;
	useAuthorInitialsForAvatars: boolean;
};

const getAvatarElement = ({
	authorEmail,
	authorInitials,
	authorName,
	avatarUrl,
	column,
	context,
	cssVariables,
	graphZoneModeConstants,
	left,
	message,
	opacity,
	suppressNonRefRowTooltips,
	type,
	useAuthorInitialsForAvatars,
}: Props): ReactElement<typeof GraphAvatar> => {
	const tooltipTextGetter = (): string => {
		const authorText: string =
			authorName && authorName.trim() !== '' && authorName !== 'Unknown'
				? getNameWithEmail(authorName, authorEmail)
				: authorEmail;
		const coauthors: Author[] = getCommitMessageCoauthors(getCommitMessageDescription(message || ''));
		let tooltipText = '';

		if (coauthors.length === 0) {
			tooltipText = authorText;
		} else {
			coauthors.unshift({ email: authorEmail, name: authorName });
			for (let i = 0; i < coauthors.length; i += 1) {
				const coauthor: Author = coauthors[i];
				tooltipText += getNameWithEmail(coauthor.name, coauthor.email);
				if (i < coauthors.length - 1) {
					tooltipText += ', ';
				}
			}
		}

		return tooltipText;
	};

	return (
		<GraphAvatar
			authorInitials={authorInitials}
			avatarUrl={avatarUrl}
			className={classnames('node', type, 'z6')}
			column={column}
			context={context}
			cssVariables={cssVariables}
			fontSize={10}
			height={graphZoneModeConstants.COMMIT_NODE_DIAMETER}
			size={graphZoneModeConstants.COMMIT_ZONE_AVATAR_DIAMETER}
			style={{ left: left, opacity: opacity }}
			tooltip={suppressNonRefRowTooltips ? undefined : tooltipTextGetter}
			top={graphZoneModeConstants.COMMIT_NODE_TOP_OFFSET}
			useAuthorInitialsForAvatars={useAuthorInitialsForAvatars}
			width={graphZoneModeConstants.COMMIT_NODE_DIAMETER}
		/>
	);
};

type CommitRowStyle = {
	borderWidth?: number;
	height: number;
	left: number;
	opacity: number;
	marginTop: number;
	width: number;
};

const getMergeNodeElement = (
	authorEmail: string,
	authorName: string,
	left: number,
	opacity: number,
	type: CommitType,
	graphZoneModeConstants: GraphZoneModeConstants,
	suppressNonRefRowTooltips: boolean = false,
): ReactElement<typeof OverlayTrigger> => {
	const mergeRowStyle: CommitRowStyle = {
		height: graphZoneModeConstants.COMMIT_MERGE_NODE_DIAMETER,
		left: left + graphZoneModeConstants.COMMIT_MERGE_NODE_LEFT_OFFSET,
		marginTop: graphZoneModeConstants.COMMIT_MERGE_NODE_TOP_OFFSET,
		opacity: opacity,
		width: graphZoneModeConstants.COMMIT_MERGE_NODE_DIAMETER,
	};

	const classNames: string = classnames('node', type, 'z6');
	const tooltipComponent = (
		<Tooltip className="gk-graph" id="graph-merge-node-tooltip">
			{getNameWithEmail(authorName, authorEmail)}
		</Tooltip>
	);

	const node = <div className={classNames} data-test-class="tree-row-commit-node" style={mergeRowStyle} />;

	return suppressNonRefRowTooltips ? (
		node
	) : (
		<OverlayTrigger delay={250} overlay={tooltipComponent} placement="top" trigger={['hover', 'focus']}>
			{node}
		</OverlayTrigger>
	);
};

const getOtherNode = (
	getExternalIcon: GetExternalIcon,
	authorEmail: string,
	authorName: string,
	left: number,
	opacity: number,
	translate: TranslationFn,
	type: CommitType,
	isCompact: boolean = false,
	graphZoneModeConstants: GraphZoneModeConstants,
	suppressNonRefRowTooltips: boolean = false,
): ReactElement<'div'> => {
	const otherRowStyle: CommitRowStyle = {
		height: graphZoneModeConstants.COMMIT_NODE_DIAMETER,
		left: left,
		marginTop: graphZoneModeConstants.COMMIT_NODE_TOP_OFFSET,
		opacity: opacity,
		width: graphZoneModeConstants.COMMIT_NODE_DIAMETER,
	};

	if (isCompact) {
		otherRowStyle.borderWidth = 1;
	}

	const classNames: string = classnames('gk-graph', 'node', type, 'z6', { compact: isCompact });
	let tooltip: string;
	if (type === stashNodeType) {
		tooltip = translate('Stash');
	} else if (type === workDirType) {
		tooltip = translate('Graph-WorkInProgress');
	} else {
		tooltip = getNameWithEmail(authorName, authorEmail);
	}

	const node = (
		<div className={classNames} data-test-class="tree-row-commit-node" style={otherRowStyle}>
			{type === stashNodeType && !isCompact ? <span>{getExternalIcon('stash')}</span> : null}
		</div>
	);

	const tooltipComponent = (
		<Tooltip className="gk-graph" id="graph-other-node-tooltip">
			{tooltip}
		</Tooltip>
	);
	return suppressNonRefRowTooltips ? (
		node
	) : (
		<OverlayTrigger delay={250} overlay={tooltipComponent} placement="top" trigger={['hover', 'focus']}>
			{node}
		</OverlayTrigger>
	);
};

export default class CommitNode extends React.PureComponent<Props> {
	override render(): ReactElement<any> {
		const {
			authorEmail,
			authorName,
			getExternalIcon,
			graphZoneModeConstants,
			left,
			opacity,
			suppressNonRefRowTooltips,
			translate,
			type,
		} = this.props;

		if ((authorName || authorEmail) && type === commitNodeType) {
			return graphZoneModeConstants.IS_COMPACT
				? getMergeNodeElement(
						authorEmail,
						authorName,
						left,
						opacity,
						mergeNodeType,
						graphZoneModeConstants,
						suppressNonRefRowTooltips,
				  )
				: getAvatarElement(this.props);
		}

		if (type === mergeNodeType || type === mergeConflictNodeType || type === unsupportedRebaseWarningNodeType) {
			return getMergeNodeElement(
				authorEmail,
				authorName,
				left,
				opacity,
				type,
				graphZoneModeConstants,
				suppressNonRefRowTooltips,
			);
		}

		return getOtherNode(
			getExternalIcon,
			authorEmail,
			authorName,
			left,
			opacity,
			translate,
			type,
			graphZoneModeConstants.IS_COMPACT,
			graphZoneModeConstants,
			suppressNonRefRowTooltips,
		);
	}
}
