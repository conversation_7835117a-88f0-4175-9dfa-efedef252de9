import type { ReactElement, ReactNode } from 'react';
import React from 'react';
import { debounce<PERSON>rame, hasScrolledToBottom, hasScrolledToRight } from '../../../domain/graph/GraphHelpers';

export type ScrollParams = {
	clientHeight: number;
	clientWidth: number;
	scrollHeight: number;
	scrollLeft: number;
	scrollTop: number;
	scrollWidth: number;
};

export type Props = {
	children: ReactNode;
	contentHeight: number;
	contentWidth: number;
	displayHorizontalScrollbar?: boolean;
	displayVerticalScrollbar?: boolean;
	forceHorizontalScrollbar?: boolean;
	forceOnHorizontalWheel?: boolean;
	forceOnVerticalWheel?: boolean;
	forceVerticalScrollbar?: boolean;
	height?: number;
	onScroll?: ((params: ScrollParams) => any) | undefined;
	scrollLeft?: number;
	scrollTop?: number;
	width?: number;
};

class ScrollbarContainer extends React.PureComponent<Props> {
	resizeObserver: ResizeObserver;

	scrollbarContainerRef: React.RefObject<any> = React.createRef();
	horizontalScrollRef: React.RefObject<any> = React.createRef();
	scrollabbleContentRef: React.RefObject<any> = React.createRef();
	verticalScrollRef: React.RefObject<any> = React.createRef();
	outerHorizontalScrollRef: React.RefObject<any> = React.createRef();

	constructor(props: Props) {
		super(props);

		this.resizeObserver = new ResizeObserver(entries =>
			entries.forEach((e: ResizeObserverEntry) =>
				this.onContainerResized(e.contentRect.width, e.contentRect.height),
			),
		);
	}

	updateScrollbarValues(scrollLeft?: number, scrollTop?: number): void {
		if (this.horizontalScrollRef?.current) {
			this.horizontalScrollRef.current.scrollLeft = scrollLeft || 0;
		}

		if (this.verticalScrollRef?.current) {
			this.verticalScrollRef.current.scrollTop = scrollTop || 0;
		}
	}

	override componentDidUpdate(prevProps: Props): void {
		const { scrollLeft, scrollTop } = this.props;

		if (prevProps.scrollLeft !== scrollLeft || prevProps.scrollTop !== scrollTop) {
			this.updateScrollbarValues(scrollLeft, scrollTop);
		}
	}

	onHorizontalScroll(event: any): void {
		if (this.props.onScroll) {
			this.props.onScroll({
				clientHeight: this.props.height || 0,
				clientWidth: event.target.clientWidth,
				scrollHeight: this.getAdjustedContentHeight(),
				scrollLeft: event.target.scrollLeft,
				scrollTop: this.props.scrollTop || 0,
				scrollWidth: event.target.scrollWidth,
			});
		}
	}

	onVerticalScroll(event: any): void {
		// this method handles manual changes event dispatched in the updateScrollbarValues method
		// for some reason, after update to react 19, handling scroll event immediately after dispatching breaks scroll position
		// initialization for column grids. If you remove this check, you can reproduce it by moving columns with not-0 scroll position
		const itselfHandled = Math.abs(event.target.scrollTop - (this.props.scrollTop ?? 0)) < 1;
		if (this.props.onScroll && !itselfHandled) {
			this.props.onScroll({
				clientHeight: event.target.clientHeight,
				clientWidth: this.props.width || 0,
				scrollHeight: event.target.scrollHeight,
				scrollLeft: this.props.scrollLeft || 0,
				scrollTop: event.target.scrollTop,
				scrollWidth: this.props.contentWidth || 0,
			});
		}
	}

	onWheel(event: React.WheelEvent): void {
		if (
			!this.props.onScroll ||
			(!this.verticalScrollRef.current &&
				!this.props.forceOnVerticalWheel &&
				!this.horizontalScrollRef.current &&
				!this.props.forceOnHorizontalWheel)
		) {
			return;
		}

		// Horizontal scroll stuff

		const prevScrollLeft = this.props.scrollLeft || 0;
		let newScrollLeft = prevScrollLeft;

		if (this.horizontalScrollRef.current || this.props.forceOnHorizontalWheel) {
			newScrollLeft += event.deltaX;
			newScrollLeft = newScrollLeft >= 0 ? newScrollLeft : 0;

			if (
				newScrollLeft > prevScrollLeft &&
				this.horizontalScrollRef?.current &&
				hasScrolledToRight(this.horizontalScrollRef.current)
			) {
				newScrollLeft = prevScrollLeft;
			}
		}

		// Vertical scroll stuff

		const prevScrollTop = this.props.scrollTop || 0;
		let newScrollTop = prevScrollTop;

		if (this.verticalScrollRef.current || this.props.forceOnVerticalWheel) {
			newScrollTop += event.deltaY;
			newScrollTop = newScrollTop >= 0 ? newScrollTop : 0;

			if (
				newScrollTop > prevScrollTop &&
				this.verticalScrollRef?.current &&
				hasScrolledToBottom(this.verticalScrollRef.current)
			) {
				newScrollTop = prevScrollTop;
			}
		}

		// Call the "onScroll" procedure if the scroll positions have changed
		if (newScrollTop !== prevScrollTop || newScrollLeft !== prevScrollLeft) {
			this.props.onScroll({
				clientHeight: this.props.height || 0,
				clientWidth: this.props.width || 0,
				scrollHeight: this.getAdjustedContentHeight(),
				scrollLeft: newScrollLeft >= 0 ? newScrollLeft : 0,
				scrollTop: newScrollTop >= 0 ? newScrollTop : 0,
				scrollWidth: this.props.contentWidth || 0,
			});
		}
	}

	override componentDidMount(): void {
		this.updateScrollbarValues(this.props.scrollLeft, this.props.scrollTop);
		this.resizeObserver.observe(this.scrollbarContainerRef.current);
	}

	override componentWillUnmount(): void {
		this.resizeObserver.disconnect();
	}

	// When resizing the container, it may be possible that it stops having scroll
	// so we must update its value. See issue GK-4638 for more details.
	onContainerResized = debounceFrame((rectWidth: number, rectHeight: number): void => {
		const currentContentHeight = this.props.contentHeight || 0;
		const currentContentWidth = this.props.contentWidth || 0;

		const oldScrollTop = this.props.scrollTop || 0;
		const oldScrollLeft = this.props.scrollLeft || 0;

		const newScrollTop = rectHeight < currentContentHeight ? oldScrollTop : 0;
		const newScrollLeft = rectWidth < currentContentWidth ? oldScrollLeft : 0;

		if (oldScrollTop !== newScrollTop || oldScrollLeft !== newScrollLeft) {
			this.updateScrollbarValues(newScrollLeft, newScrollTop);
			this.props.onScroll?.({
				clientHeight: rectHeight,
				clientWidth: rectWidth,
				scrollHeight: currentContentHeight,
				scrollLeft: newScrollLeft,
				scrollTop: newScrollTop,
				scrollWidth: currentContentWidth,
			});
		}
	});

	hasHorizontalScroll(): boolean {
		const { contentWidth, displayHorizontalScrollbar, forceHorizontalScrollbar, width } = this.props;

		return forceHorizontalScrollbar || (displayHorizontalScrollbar && width < contentWidth);
	}

	hasVerticalScroll(): boolean {
		const { displayVerticalScrollbar, forceVerticalScrollbar, height } = this.props;

		return forceVerticalScrollbar || (displayVerticalScrollbar && height < this.getAdjustedContentHeight());
	}

	getHorizontalScrollbarTickness(): number {
		return parseInt(this.outerHorizontalScrollRef.current?.clientHeight, 10) || 0;
	}

	getAdjustedContentHeight(): number {
		return this.props.contentHeight + this.getHorizontalScrollbarTickness();
	}

	adjustScrollWhileDragging(event: React.DragEvent<HTMLElement>): void {
		const adjustedContentHeight = this.getAdjustedContentHeight();

		if (!this.scrollabbleContentRef.current || this.props.height >= adjustedContentHeight || !this.props.onScroll) {
			return;
		}

		const offset = 20;
		const targetRect: DOMRect = this.scrollabbleContentRef.current.getBoundingClientRect();

		const currentY: number = event.clientY - targetRect.top;
		const scrollHeight = Math.abs(adjustedContentHeight - this.props.height);

		let newScrollParams: ScrollParams | void;

		// Automatically scroll when dragging element up
		if (this.props.scrollTop > 0 && currentY <= offset) {
			const scrollTop: number = this.props.scrollTop - offset;
			newScrollParams = {
				clientHeight: targetRect.height,
				clientWidth: targetRect.width,
				scrollHeight: scrollHeight,
				scrollLeft: this.props.scrollLeft || 0,
				scrollTop: scrollTop > 0 ? scrollTop : 0,
				scrollWidth: this.props.contentWidth || 0,
			};
		}

		// Automatically scroll when dragging element down
		if (this.props.scrollTop < scrollHeight && currentY >= this.props.height - offset) {
			const scrollTop = this.props.scrollTop + offset;
			newScrollParams = {
				clientHeight: targetRect.height,
				clientWidth: targetRect.width,
				scrollHeight: scrollHeight,
				scrollLeft: this.props.scrollLeft || 0,
				scrollTop: scrollTop < scrollHeight ? scrollTop : scrollHeight,
				scrollWidth: this.props.contentWidth || 0,
			};
		}

		if (newScrollParams) {
			this.props.onScroll(newScrollParams);
		}
	}

	override render(): ReactElement<any> {
		const {
			children,
			contentHeight,
			contentWidth,
			forceHorizontalScrollbar,
			forceVerticalScrollbar,
			height,
			width,
		} = this.props;

		const hasVerticalScroll = this.hasVerticalScroll();
		const hasHorizontalScroll = this.hasHorizontalScroll();

		const maybeHorizontalScrollbar = hasHorizontalScroll && (
			<div
				className="scrollbar-outer horizontal"
				ref={this.outerHorizontalScrollRef}
				style={{
					top: `calc(${height}px - var(--scrollable-scrollbar-thickness))`,
					width: width,
				}}
			>
				<div
					className="scrollbar-inner horizontal_scrollbar"
					onScroll={e => this.onHorizontalScroll(e)}
					ref={this.horizontalScrollRef}
					style={{
						overflowX: forceHorizontalScrollbar ? 'scroll' : 'auto',
						width: hasVerticalScroll ? `calc(${width}px - var(--scrollable-scrollbar-thickness))` : width,
					}}
				>
					<div style={{ height: '1px', width: contentWidth }} />
				</div>
			</div>
		);

		const maybeVerticalScrollbar = hasVerticalScroll && (
			<div className="scrollbar-outer vertical" style={{ height: height }}>
				<div
					className="scrollbar-inner vertical_scrollbar"
					onScroll={e => this.onVerticalScroll(e)}
					ref={this.verticalScrollRef}
					style={{
						overflowY: forceVerticalScrollbar ? 'scroll' : 'auto',
						height: hasHorizontalScroll
							? `calc(${height}px - var(--scrollable-scrollbar-thickness))`
							: height,
					}}
				>
					<div
						style={{
							height: `calc(${contentHeight}px + var(--scrollable-scrollbar-thickness))`,
							width: '1px',
						}}
					/>
				</div>
			</div>
		);

		return (
			<div
				className="scrollbar-container"
				onWheelCapture={e => this.onWheel(e)}
				ref={this.scrollbarContainerRef}
				style={{ height: height, width: width }}
			>
				<div
					className="scrollable-content"
					onDragOverCapture={e => this.adjustScrollWhileDragging(e)}
					ref={this.scrollabbleContentRef}
					style={{ height: height, width: width }}
				>
					{children}
				</div>
				{maybeHorizontalScrollbar}
				{maybeVerticalScrollbar}
			</div>
		);
	}
}

export default ScrollbarContainer;
