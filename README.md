# GitKraken Components

This is a shared module between the GitKraken app and GitLens to share React components.

## Requires resolution to React 17
> [!WARNING]
> In order for this package to work properly, you must resolve React to version 17 in your own project.
> The reason for this is because this library's react-bootstrap dependency lists React 16 as a dependency, and npm/yarn 1 do not provide a way for dependencies to lock sub-dependencies. Therefore it is up to your project to force this library's react-bootstrap to use react 17.
> The following is an example of what to put into your package.json:
```
  "resolutions": {
    "react": "17.0.2",
    "react-dom": "17.0.2"
  }
```

[CHANGELOG](./CHANGELOG.md)
