.gk-graph {
	.scrollbar-container {
		.scrollable-content > div {
			overflow: hidden !important;
		}

		.scrollbar-outer {
			position: absolute;
			background-color: var(--app__bg0);

			&.vertical {
				right: 0;
				top: 0;
				width: var(--scrollable-scrollbar-thickness);
			}

			&.horizontal {
				height: var(--scrollable-scrollbar-thickness);
				right: 0;
			}
		}

		.scrollbar-inner {
			> div {
				background-color: inherit;
			}

			&::-webkit-scrollbar {
				background-color: var(--app__bg0);
				height: var(--scrollable-scrollbar-thickness);
			}

			&::-webkit-scrollbar-track {
				-webkit-border-radius: calc(5 * var(--scrollable-scrollbar-thickness) / 6);
				border-radius: calc(5 * var(--scrollable-scrollbar-thickness) / 6);
				background-color: transparent;
			}

			&::-webkit-scrollbar-thumb {
				-webkit-border-radius: calc(5 * var(--scrollable-scrollbar-thickness) / 6);
				border-radius: calc(5 * var(--scrollable-scrollbar-thickness) / 6);
				background-color: var(--scroll-thumb-bg, #3d424d);
			}

			&::-webkit-scrollbar-corner {
				background-color: transparent;
			}

			&::-webkit-scrollbar,
			&::-webkit-scrollbar-track,
			&::-webkit-scrollbar-thumb,
			&::-webkit-scrollbar-corner {
				width: var(--scrollable-scrollbar-thickness);
				display: none;
			}

			&.vertical_scrollbar {
				&::-webkit-scrollbar:vertical,
				&::-webkit-scrollbar-track:vertical,
				&::-webkit-scrollbar-thumb:vertical,
				&::-webkit-scrollbar-corner:vertical {
					display: inherit;
				}
			}

			&.horizontal_scrollbar {
				&::-webkit-scrollbar:horizontal,
				&::-webkit-scrollbar-track:horizontal,
				&::-webkit-scrollbar-thumb:horizontal,
				&::-webkit-scrollbar-corner:horizontal {
					display: inherit;
				}
			}
		}
	}
}
