import chai, { expect } from 'chai';
import spies from 'chai-spies';
import { DEFAULT_CSS_VARIABLES } from '../../../../src/domain/cssvariable/CssVariableConstants';
import * as CommitConstants from '../../../../src/domain/commit/CommitConstants';
import * as GraphHelpers from '../../../../src/domain/graph/GraphHelpers';
import * as GraphConstants from '../../../../src/domain/graph/GraphConstants';
import * as GraphTypes from '../../../../src/domain/graph/GraphTypes';
import * as RefConstants from '../../../../src/domain/ref/RefConstants';
import * as RefTypes from '../../../../src/domain/ref/RefTypes';
import * as GenericTypes from '../../../../src/domain/generic/GenericTypes';
import { ProcessedGraphRowObj } from '../../../../src/domain/graph/row/ProcessedGraphRowObj';
import { IGraphContainer } from '../../../../src/components/graph/IGraphContainer';

chai.use(spies);

describe('Graph Helpers', function () {
	// Mock refs
	const mockContext = '{"test":"test"}';
	const mockContextGroup = '{"testGroup":"testGroup"}';
	const mockRemoteRefType: RefTypes.GraphRefType = RefConstants.refTypes.REMOTE;
	const mockRemoteRefId: string = 'test-id';
	const mockBaseRemoteRef: GraphTypes.Remote = {
		id: mockRemoteRefId,
		name: 'test',
		owner: 'origin',
		avatarUrl: 'fakeurl.fake/avatar',
		context: mockContext,
		contextGroup: mockContextGroup,
	};
	const mockBaseRemoteRefWithoutOwner: GraphTypes.Remote = {
		id: mockRemoteRefId,
		name: 'test',
		avatarUrl: 'fakeurl.fake/avatar',
		context: mockContext,
		contextGroup: mockContextGroup,
	};
	const mockGraphRemoteRef: GraphTypes.GraphRef = {
		...mockBaseRemoteRef,
		refType: mockRemoteRefType,
	};
	const mockGraphRemoteRefWithoutOwner: GraphTypes.GraphRef = {
		...mockBaseRemoteRefWithoutOwner,
		refType: mockRemoteRefType,
	};
	const mockHeadRefType: RefTypes.GraphRefType = RefConstants.refTypes.HEAD;
	const mockHeadRefId: string = 'test-head-id';
	const mockBaseHeadRef: GraphTypes.Head = {
		id: mockHeadRefId,
		name: 'test-head',
		isCurrentHead: true,
	};
	const mockBaseTagRef: GraphTypes.Tag = {
		id: 'test-tag-id',
		name: 'test-tag',
		annotated: false,
	};
	const mockGraphHeadRef: GraphTypes.GraphRef = {
		...mockBaseHeadRef,
		refType: mockHeadRefType,
	};
	const mockHeadRefGroup: GraphTypes.GraphRefGroup = [mockGraphHeadRef];

	// Mock metadata
	const mockRemoteRefMetadata: GraphTypes.RefMetadata = {
		pullRequest: [
			{
				hostingServiceType: 'github',
				id: 1,
				title: 'Test PR',
			},
		],
	};
	const mockRefMetadataById = { [mockRemoteRefId]: mockRemoteRefMetadata };
	const mockRefMetadataType: GraphTypes.RefMetadataType = 'pullRequest';
	const mockOnMissingRefMetadata: GraphTypes.GetMissingRefMetadata =
		/* eslint-disable-next-line no-unused-vars */
		function (_id: string, _types: Array<GraphTypes.RefMetadataType>) {};

	// Mock zones
	const mockHiddenCommitAuthorZone = {
		...GraphConstants.graphZoneMetaData[GraphConstants.commitAuthorZone],
		type: GraphConstants.commitAuthorZone,
		currentWidth: 100,
		preferredWidth: 100,
		isHidden: true,
		order: 3,
	};

	const mockGraphZones: Array<GraphConstants.GraphZone> = [
		{
			...GraphConstants.graphZoneMetaData[GraphConstants.refZone],
			type: GraphConstants.refZone,
			currentWidth: 105,
			preferredWidth: 105,
			isHidden: false,
			order: 0,
		},
		{
			...GraphConstants.graphZoneMetaData[GraphConstants.commitZone],
			type: GraphConstants.commitZone,
			currentWidth: 300,
			preferredWidth: 300,
			isHidden: false,
			order: 1,
		},
		{
			...GraphConstants.graphZoneMetaData[GraphConstants.commitMessageZone],
			type: GraphConstants.commitMessageZone,
			currentWidth: 220,
			preferredWidth: 220,
			isHidden: false,
			order: 2,
		},
		{
			...mockHiddenCommitAuthorZone,
		},
		{
			...GraphConstants.graphZoneMetaData[GraphConstants.commitDateTimeZone],
			type: GraphConstants.commitDateTimeZone,
			currentWidth: 160,
			preferredWidth: 160,
			isHidden: true,
			order: 4,
		},
		{
			...GraphConstants.graphZoneMetaData[GraphConstants.commitShaZone],
			type: GraphConstants.commitShaZone,
			currentWidth: 100,
			preferredWidth: 100,
			isHidden: false,
			order: 5,
		},
	];
	const mockHiddenGraphZone: GraphConstants.GraphZone = mockHiddenCommitAuthorZone;
	const mockActiveGraphZones: Array<GraphConstants.GraphZone> = mockGraphZones.filter(
		(zone: GraphConstants.GraphZone) => !zone.isHidden,
	);
	const mockVisibleGraphZone: GraphConstants.GraphZone = mockActiveGraphZones[0];
	const mockGraphWidth = mockGraphZones.reduce((acc, zone) => acc + zone.currentWidth, 0);

	// Mock rows
	const mockHeadRefSha = 'a';
	const mockHeadRefParentSha = 'b';
	const mockGraphRows: Array<GraphTypes.GraphRow> = [
		{
			sha: 'work-dir-changes',
			parents: [mockHeadRefSha],
			author: 'Test Author',
			email: '<EMAIL>',
			date: 100,
			message: 'Work dir changes',
			type: CommitConstants.workDirType,
			heads: [],
			remotes: [],
			tags: [],
		},
		{
			sha: mockHeadRefSha,
			parents: [mockHeadRefParentSha],
			author: 'Test Author',
			email: '<EMAIL>',
			date: 99,
			message: 'Test commit 2',
			type: CommitConstants.commitNodeType,
			heads: [mockGraphHeadRef],
			remotes: [],
			tags: [],
		},
		{
			sha: mockHeadRefParentSha,
			parents: [],
			author: 'Test Author',
			email: '<EMAIL>',
			date: 98,
			message: 'Test commit 1',
			type: CommitConstants.commitNodeType,
			heads: [],
			remotes: [mockBaseRemoteRef],
			tags: [],
		},
		{
			sha: 'c',
			parents: [],
			author: 'Test Author',
			email: '<EMAIL>',
			date: 97,
			message: 'Test commit 3',
			type: CommitConstants.commitNodeType,
			heads: [mockGraphHeadRef],
			remotes: [mockBaseRemoteRef],
			tags: [],
		},
		{
			sha: 'without-refs',
			parents: [mockHeadRefParentSha],
			author: 'Test Author',
			email: '<EMAIL>',
			date: 96,
			message: 'Test commit 4',
			type: CommitConstants.commitNodeType,
			heads: [],
			remotes: [],
			tags: [],
		},
		{
			sha: 'with-refs-only-tag',
			parents: [mockHeadRefParentSha],
			author: 'Test Author',
			email: '<EMAIL>',
			date: 95,
			message: 'Test commit 5',
			type: CommitConstants.commitNodeType,
			heads: [],
			remotes: [],
			tags: [mockBaseTagRef],
		},
		{
			sha: 'with-remote-without-owner',
			parents: [],
			author: 'Test Author',
			email: '<EMAIL>',
			date: 98,
			message: 'Test commit 6',
			type: CommitConstants.commitNodeType,
			heads: [mockGraphHeadRef],
			remotes: [mockGraphRemoteRefWithoutOwner],
			tags: [],
		},
	];

	const mockSelectedShas = new Set<string>([mockHeadRefParentSha]);

	const mockGraphContainer: IGraphContainer = {
		formatCommitMessageCallback: commitMessage => commitMessage,
		translateCallback: key => key,
	};

	const mockProcessedGraphRows: Array<GraphTypes.ProcessedGraphRow> = [
		new ProcessedGraphRowObj(
			mockGraphContainer,
			{ ...mockGraphRows[0] }, // row
			0, // column
			1, // edgeColumnMaxes
			{}, // edges
		),
		new ProcessedGraphRowObj(
			mockGraphContainer,
			{ ...mockGraphRows[1] }, // row
			0, // column
			1, // edgeColumnMaxes
			{}, // edges
		),
		new ProcessedGraphRowObj(
			mockGraphContainer,
			{ ...mockGraphRows[2] }, // row
			0, // column
			1, // edgeColumnMaxes
			{}, // edges
			undefined, // childRefs
			{ date: 98, label: 'Timeline-NHoursAgo', timeUnit: 'hour', value: 2 }, // timeLineEntry
		),
		new ProcessedGraphRowObj(
			mockGraphContainer,
			{ ...mockGraphRows[3] }, // row
			0, // column
			1, // edgeColumnMaxes
			{}, // edges
		),
		new ProcessedGraphRowObj(
			mockGraphContainer,
			{ ...mockGraphRows[4] }, // row
			0, // column
			1, // edgeColumnMaxes
			{}, // edges
		),
		new ProcessedGraphRowObj(
			mockGraphContainer,
			{ ...mockGraphRows[5] }, // row
			0, // column
			1, // edgeColumnMaxes
			{}, // edges
		),
		new ProcessedGraphRowObj(
			mockGraphContainer,
			{ ...mockGraphRows[6] }, // row
			0, // column
			1, // edgeColumnMaxes
			{}, // edges
		),
	];

	const mockProcessedGraphRowBySha: GraphTypes.ProcessedGraphRowBySha = {};
	for (const row of mockProcessedGraphRows) {
		mockProcessedGraphRowBySha[row.sha] = row;
	}

	const mockCssVariables: GenericTypes.CssVariables = { ...DEFAULT_CSS_VARIABLES };
	const mockEnabledScrollMarkerTypes: any[] = ['localBranches'];
	const mockHeight = 580;
	const mockHasMoreCommits = false;
	const mockIsLoadingRows = false;
	const mockDimRowsOfSelectedCommit = false;

	// Mock events
	let mockOnMissingRefMetadataSpy: any;
	beforeEach(function () {
		mockOnMissingRefMetadataSpy = chai.spy(mockOnMissingRefMetadata);
	});
	afterEach(function () {
		chai.spy.restore();
	});

	describe('parseContext', function () {
		it('should return the context if it is a string', function () {
			expect(GraphHelpers.parseContext(mockContext)).to.equal(mockContext);
		});

		it('should return the stringified context if it is a JSON object', function () {
			expect(GraphHelpers.parseContext(JSON.parse(mockContext))).to.equal(mockContext);
		});

		it('should return null if no context is provided', function () {
			expect(GraphHelpers.parseContext(null)).to.equal(null);
		});
	});

	describe('getRowStatsConstraints', function () {
		const mockRowStatsAndExpectedConstraints: {
			rowStats: GraphTypes.RowStats[];
			expectedConstraints: GraphTypes.RowStatsConstraints;
		} = {
			rowStats: [
				{
					additions: 0,
					deletions: 0,
					files: 0,
				},
				{
					additions: 100,
					deletions: 100,
					files: 100,
				},
				{
					additions: 200,
					deletions: 120,
					files: 30,
				},
				{
					additions: 150,
					deletions: 2,
					files: 10,
				},
				{
					additions: 1,
					deletions: 300,
					files: 2,
				},
				{
					additions: 300,
					deletions: 0,
					files: 3,
				},
				{
					additions: 20000,
					deletions: 30000,
					files: 100,
				},
			],
			expectedConstraints: {
				min: 48,
				max: 552,
			},
		};

		it('should return the correct min and max values with an unsorted array of row stats', function () {
			const { rowStats, expectedConstraints } = mockRowStatsAndExpectedConstraints;
			const { min, max } = GraphHelpers.getRowStatsConstraints(rowStats.map(s => s.additions + s.deletions));

			expect(min).to.equal(expectedConstraints.min);
			expect(max).to.equal(expectedConstraints.max);
		});

		it('should return the correct min and max values with a sorted array of row stats', function () {
			const { rowStats, expectedConstraints } = mockRowStatsAndExpectedConstraints;
			const rowStatsSorted = rowStats.map(s => s.additions + s.deletions).sort((a, b) => a - b);
			const { min, max } = GraphHelpers.getRowStatsConstraints(rowStatsSorted);

			expect(min).to.equal(expectedConstraints.min);
			expect(max).to.equal(expectedConstraints.max);
		});
	});

	describe('getOrAskForRefMetadata', function () {
		it('should do nothing if id is missing or entry is null', function () {
			expect(
				GraphHelpers.getOrAskForRefMetadata(
					{ ...mockGraphRemoteRef, id: '' },
					mockRefMetadataById,
					mockOnMissingRefMetadataSpy,
					mockRefMetadataType,
				),
			).to.equal(null);
			expect(mockOnMissingRefMetadataSpy).not.to.have.been.called();

			expect(
				GraphHelpers.getOrAskForRefMetadata(
					mockGraphRemoteRef,
					null,
					mockOnMissingRefMetadataSpy,
					mockRefMetadataType,
				),
			).to.equal(null);
			expect(mockOnMissingRefMetadataSpy).not.to.have.been.called();

			expect(
				GraphHelpers.getOrAskForRefMetadata(
					mockGraphRemoteRef,
					{ ...mockRefMetadataById, [mockRemoteRefId]: null },
					mockOnMissingRefMetadataSpy,
					mockRefMetadataType,
				),
			).to.equal(null);
			expect(mockOnMissingRefMetadataSpy).not.to.have.been.called();

			expect(
				GraphHelpers.getOrAskForRefMetadata(
					mockGraphRemoteRef,
					{ ...mockRefMetadataById, [mockRemoteRefId]: { [mockRefMetadataType]: null } },
					mockOnMissingRefMetadataSpy,
					mockRefMetadataType,
				),
			).to.equal(null);
			expect(mockOnMissingRefMetadataSpy).not.to.have.been.called();
		});

		it('should call onMissingRefMetadata when metadata is missing', function () {
			expect(
				GraphHelpers.getOrAskForRefMetadata(
					mockGraphRemoteRef,
					undefined,
					mockOnMissingRefMetadataSpy,
					mockRefMetadataType,
				),
			).to.equal(null);
			expect(mockOnMissingRefMetadataSpy).to.have.been.called.exactly(1);
			expect(mockOnMissingRefMetadataSpy)
				.on.nth(1)
				.to.have.been.called.with(mockRemoteRefId, GraphTypes.allMetadataTypes);

			expect(
				GraphHelpers.getOrAskForRefMetadata(
					mockGraphRemoteRef,
					{},
					mockOnMissingRefMetadataSpy,
					mockRefMetadataType,
				),
			).to.equal(null);
			expect(mockOnMissingRefMetadataSpy).to.have.been.called.exactly(2);
			expect(mockOnMissingRefMetadataSpy)
				.on.nth(2)
				.to.have.been.called.with(mockRemoteRefId, GraphTypes.allMetadataTypes);

			expect(
				GraphHelpers.getOrAskForRefMetadata(
					mockGraphRemoteRef,
					{ ...mockRefMetadataById, [mockRemoteRefId]: {} },
					mockOnMissingRefMetadataSpy,
					mockRefMetadataType,
				),
			).to.equal(null);
			expect(mockOnMissingRefMetadataSpy).to.have.been.called.exactly(3);
			expect(mockOnMissingRefMetadataSpy)
				.on.nth(3)
				.to.have.been.called.with(mockRemoteRefId, [mockRefMetadataType]);
		});

		it('should return requested metadata when it is available', function () {
			expect(
				GraphHelpers.getOrAskForRefMetadata(
					mockGraphRemoteRef,
					mockRefMetadataById,
					mockOnMissingRefMetadataSpy,
				),
			).to.equal(mockRefMetadataById[mockRemoteRefId]);
			expect(mockOnMissingRefMetadataSpy).not.to.have.been.called();

			expect(
				GraphHelpers.getOrAskForRefMetadata(
					mockGraphRemoteRef,
					mockRefMetadataById,
					mockOnMissingRefMetadataSpy,
					mockRefMetadataType,
				),
			).to.equal(mockRefMetadataById[mockRemoteRefId][mockRefMetadataType]);
			expect(mockOnMissingRefMetadataSpy).not.to.have.been.called();
		});
	});

	describe('getTimelineEntriesByPeriod', function () {
		it('should return the appropriate number of entries by period', function () {
			const timelineEntries: GraphConstants.TimelineEntriesByPeriod = GraphHelpers.getTimelineEntriesByPeriod();
			expect(timelineEntries.hour.length).to.equal(GraphConstants.lookbackLimitByPeriod.hour);
			expect(timelineEntries.day.length).to.equal(GraphConstants.lookbackLimitByPeriod.day);
			expect(timelineEntries.week.length).to.equal(GraphConstants.lookbackLimitByPeriod.week);
			expect(timelineEntries.month.length).to.equal(GraphConstants.lookbackLimitByPeriod.month);
			expect(timelineEntries.year.length).to.equal(GraphConstants.lookbackLimitByPeriod.year);
		});
	});

	describe('getRefIdByBaseRef', function () {
		it('should return the ref id if available', function () {
			expect(GraphHelpers.getRefIdByBaseRef(mockRemoteRefType, mockBaseRemoteRef)).to.equal(mockRemoteRefId);
		});

		const mockBaseRefWithoutId: any = { ...mockBaseRemoteRef, id: undefined };
		it('should include type and owner on remote ref without id', function () {
			expect(GraphHelpers.getRefIdByBaseRef(mockRemoteRefType, mockBaseRefWithoutId)).to.equal(
				`${mockRemoteRefType}/${mockBaseRefWithoutId.owner as string}/${mockBaseRefWithoutId.name}`,
			);
		});

		it('should return type and name on non-remote ref without id', function () {
			expect(GraphHelpers.getRefIdByBaseRef(RefConstants.refTypes.HEAD, mockBaseRefWithoutId)).to.equal(
				`${RefConstants.refTypes.HEAD}/${mockBaseRefWithoutId.name}`,
			);
		});
	});

	describe('getRefIdByGraphRef', function () {
		const mockGraphRefWithoutId: any = { ...mockGraphRemoteRef, id: undefined };
		const mockHeadGraphRefWithoutId: any = { ...mockGraphRefWithoutId, refType: RefConstants.refTypes.HEAD };
		it('should return the ref id if available', function () {
			expect(GraphHelpers.getRefIdByGraphRef(mockGraphRemoteRef)).to.equal(mockRemoteRefId);
		});

		it('should include type and owner on remote ref without id', function () {
			expect(GraphHelpers.getRefIdByGraphRef(mockGraphRefWithoutId)).to.equal(
				`${mockRemoteRefType}/${mockGraphRefWithoutId.owner as string}/${mockGraphRefWithoutId.name}`,
			);
		});

		it('should return type and name on non-remote ref without id', function () {
			expect(GraphHelpers.getRefIdByGraphRef(mockHeadGraphRefWithoutId)).to.equal(
				`${RefConstants.refTypes.HEAD}/${mockGraphRefWithoutId.name}`,
			);
		});
	});

	describe('getLastShrinkableGraphZone', function () {
		const mockZonesWithShrinkable: any = JSON.parse(JSON.stringify(mockGraphZones));
		const mockZonesWithoutShrinkable: any = JSON.parse(JSON.stringify(mockGraphZones));
		const shrinkableZoneIndices = [0, 2];
		for (let i = 0; i < mockGraphZones.length; i += 1) {
			mockZonesWithoutShrinkable[i].currentWidth = mockZonesWithoutShrinkable[i].minimumWidth;
			if (shrinkableZoneIndices.includes(i)) {
				mockZonesWithShrinkable[i].currentWidth = mockZonesWithShrinkable[i].minimumWidth + 1;
			} else {
				mockZonesWithShrinkable[i].currentWidth = mockZonesWithShrinkable[i].minimumWidth;
			}
		}

		it('should get the last zone larger than its minimum width', function () {
			expect(GraphHelpers.getLastShrinkableGraphZone(mockZonesWithShrinkable)).to.equal(
				mockZonesWithShrinkable[2],
			);
		});

		it('should respect stopping index when provided', function () {
			expect(GraphHelpers.getLastShrinkableGraphZone(mockZonesWithShrinkable, 3)).to.equal(undefined);
		});

		it('should return undefined if no zone is expandable', function () {
			expect(GraphHelpers.getLastShrinkableGraphZone(mockZonesWithoutShrinkable)).to.equal(undefined);
		});
	});

	describe('getFirstExpandableGraphZone', function () {
		const mockZonesWithExpandable: any = JSON.parse(JSON.stringify(mockGraphZones));
		const mockZonesWithExpandableMaximum: any = JSON.parse(JSON.stringify(mockGraphZones));
		const mockZonesWithoutExpandable: any = JSON.parse(JSON.stringify(mockGraphZones));
		const expandableZoneIndices = [0, 2];
		for (let i = 0; i < mockGraphZones.length; i += 1) {
			mockZonesWithExpandable[i].maximumWidth = mockZonesWithExpandable[i].preferredWidth;
			mockZonesWithoutExpandable[i].maximumWidth = mockZonesWithoutExpandable[i].preferredWidth;

			mockZonesWithoutExpandable[i].currentWidth = mockZonesWithoutExpandable[i].preferredWidth;
			if (expandableZoneIndices.includes(i)) {
				mockZonesWithExpandableMaximum[i].currentWidth = mockZonesWithExpandableMaximum[i].preferredWidth - 2;
				mockZonesWithExpandable[i].currentWidth = mockZonesWithExpandable[i].preferredWidth - 2;
			} else {
				mockZonesWithExpandableMaximum[i].currentWidth = mockZonesWithExpandableMaximum[i].preferredWidth;
				mockZonesWithExpandable[i].currentWidth = mockZonesWithExpandable[i].preferredWidth;
			}

			if (i === expandableZoneIndices[0]) {
				mockZonesWithExpandableMaximum[i].maximumWidth = mockZonesWithExpandableMaximum[i].currentWidth;
			} else {
				mockZonesWithExpandableMaximum[i].maximumWidth = mockZonesWithExpandableMaximum[i].preferredWidth;
			}
		}

		it('should get the first zone smaller than its preferred width', function () {
			expect(GraphHelpers.getFirstExpandableGraphZone(mockZonesWithExpandable)).to.equal(
				mockZonesWithExpandable[0],
			);
		});

		it('should respect starting index when provided', function () {
			expect(GraphHelpers.getFirstExpandableGraphZone(mockZonesWithExpandable, 1)).to.equal(
				mockZonesWithExpandable[2],
			);
		});

		it('should respect maximum width', function () {
			expect(GraphHelpers.getFirstExpandableGraphZone(mockZonesWithExpandableMaximum)).to.equal(
				mockZonesWithExpandableMaximum[2],
			);
		});

		it('should return the last zone if no expandable zone found', function () {
			expect(GraphHelpers.getFirstExpandableGraphZone(mockZonesWithoutExpandable)).to.equal(
				mockZonesWithoutExpandable[mockZonesWithoutExpandable.length - 1],
			);
		});
	});

	describe('getZoneWidthsTotal', function () {
		const mockGraphZoneWidthsTotal = mockGraphZones.reduce((total, zone) => total + zone.currentWidth, 0);
		it('should return the sum of all zone widths', function () {
			expect(GraphHelpers.getZoneWidthsTotal(mockGraphZones)).to.equal(mockGraphZoneWidthsTotal);
		});

		it('should exclude a zone width if provided', function () {
			expect(GraphHelpers.getZoneWidthsTotal(mockGraphZones, mockGraphZones[0].type)).to.equal(
				mockGraphZoneWidthsTotal - mockGraphZones[0].currentWidth,
			);
		});
	});

	describe('getFollowingZoneMinWidthsTotal', function () {
		const mockFollowingZoneMinWidthsTotal = mockGraphZones
			.slice(1)
			.reduce((total, zone) => total + zone.minimumWidth, 0);
		it('should return the sum of all following zone minimum widths', function () {
			expect(GraphHelpers.getFollowingZoneMinWidthsTotal(mockGraphZones, 1)).to.equal(
				mockFollowingZoneMinWidthsTotal,
			);
		});
	});

	describe('getPrecedingZoneCurrentWidthsTotal', function () {
		const mockPrecedingZoneCurrentWidthsTotal = mockGraphZones
			.slice(0, -1)
			.reduce((total, zone) => total + zone.currentWidth, 0);
		it('should return the sum of all preceding zone minimum widths', function () {
			expect(GraphHelpers.getPrecedingZoneCurrentWidthsTotal(mockGraphZones, mockGraphZones.length - 2)).to.equal(
				mockPrecedingZoneCurrentWidthsTotal,
			);
		});
	});

	describe('getClampedZoneWidth', function () {
		const mockMaxWidth = 500;
		const mockZonesWithMaxWidths: any = JSON.parse(JSON.stringify(mockGraphZones));
		for (let i = 0; i < mockGraphZones.length; i += 1) {
			mockZonesWithMaxWidths[i].maximumWidth = mockMaxWidth;
		}

		it('should clamp zone width to minimum width', function () {
			expect(
				GraphHelpers.getClampedZoneWidth(mockGraphZones[0], mockGraphZones, mockGraphZones[0].minimumWidth - 1),
			).to.equal(mockGraphZones[0].minimumWidth);
		});

		it('should clamp zone width to maximum width if not the last zone', function () {
			expect(
				GraphHelpers.getClampedZoneWidth(
					mockZonesWithMaxWidths[0],
					mockZonesWithMaxWidths,
					mockZonesWithMaxWidths[0].maximumWidth + 1,
				),
			).to.equal(mockZonesWithMaxWidths[0].maximumWidth);
		});

		it('should not clamp zone width if within bounds', function () {
			expect(
				GraphHelpers.getClampedZoneWidth(mockGraphZones[0], mockGraphZones, mockGraphZones[0].minimumWidth + 1),
			).to.equal(mockGraphZones[0].minimumWidth + 1);

			expect(
				GraphHelpers.getClampedZoneWidth(
					mockZonesWithMaxWidths[0],
					mockZonesWithMaxWidths,
					mockZonesWithMaxWidths[0].maximumWidth - 1,
				),
			).to.equal(mockZonesWithMaxWidths[0].maximumWidth - 1);
		});

		it('should not clamp zone width to maximum width for the last zone', function () {
			expect(
				GraphHelpers.getClampedZoneWidth(
					mockZonesWithMaxWidths[mockZonesWithMaxWidths.length - 1],
					mockZonesWithMaxWidths,
					mockZonesWithMaxWidths[mockZonesWithMaxWidths.length - 1].maximumWidth + 1,
				),
			).to.equal(mockZonesWithMaxWidths[mockZonesWithMaxWidths.length - 1].maximumWidth + 1);
		});
	});

	describe('getGraphZoneFromGraphZones', function () {
		it('should return the correct zone', function () {
			expect(GraphHelpers.getGraphZoneFromGraphZones(mockGraphZones[0].type, mockGraphZones)).to.deep.equal(
				mockGraphZones[0],
			);
		});

		it('should return undefined if zone not found', function () {
			expect(GraphHelpers.getGraphZoneFromGraphZones('not a zone' as any, mockGraphZones)).to.be.undefined;
		});
	});

	describe('getGraphZoneIndexFromGraphZones', function () {
		it('should return the correct zone index', function () {
			expect(GraphHelpers.getGraphZoneIndexFromGraphZones(mockGraphZones[0].type, mockGraphZones)).to.equal(0);
		});

		it('should return -1 if zone not found', function () {
			expect(GraphHelpers.getGraphZoneIndexFromGraphZones('not a zone' as any, mockGraphZones)).to.equal(-1);
		});
	});

	describe('getGraphZoneWidthConstraintsFromGraphZones', function () {
		it('should return 0 values if the zone isnt found', function () {
			// eslint-disable-next-line max-len
			expect(
				GraphHelpers.getGraphZoneWidthConstraintsFromGraphZones(
					'not a zone' as any,
					mockGraphZones,
					mockGraphWidth,
				),
			).to.deep.equal({ min: 0, max: 0 });
		});

		it('should return the minimum width of the zone as the min constraint', function () {
			const zoneWidthConstraints: GraphTypes.GraphColumnWidthConstraints =
				GraphHelpers.getGraphZoneWidthConstraintsFromGraphZones(
					mockGraphZones[0].type,
					mockGraphZones,
					mockGraphWidth,
				);
			expect(zoneWidthConstraints.min).to.equal(mockGraphZones[0].minimumWidth);
		});

		const mockGraphCentralZoneIndex = 2;
		const mockGraphExpandabilityConstraint =
			mockGraphWidth -
			mockGraphZones.reduce((total, zone, index) => {
				if (index < mockGraphCentralZoneIndex) {
					return total + zone.currentWidth;
				}

				if (index > mockGraphCentralZoneIndex) {
					return total + zone.minimumWidth;
				}

				return total;
			}, 0);
		const mockZonesWithLowerMaximumWidth = JSON.parse(JSON.stringify(mockGraphZones));
		const mockZonesWithHigherMaximumWidth = JSON.parse(JSON.stringify(mockGraphZones));
		mockZonesWithLowerMaximumWidth[mockGraphCentralZoneIndex].maximumWidth = mockGraphExpandabilityConstraint - 1;
		mockZonesWithHigherMaximumWidth[mockGraphCentralZoneIndex].maximumWidth = mockGraphExpandabilityConstraint + 1;
		it('should return the maximum width of the zone if it is smaller than the expandability constraint', function () {
			const zoneWidthConstraints: GraphTypes.GraphColumnWidthConstraints =
				GraphHelpers.getGraphZoneWidthConstraintsFromGraphZones(
					mockGraphZones[mockGraphCentralZoneIndex].type,
					mockZonesWithLowerMaximumWidth,
					mockGraphWidth,
				);
			expect(zoneWidthConstraints.max).to.equal(
				mockZonesWithLowerMaximumWidth[mockGraphCentralZoneIndex].maximumWidth,
			);
		});

		it('should return the expandability constraint if the maximum width of the zone is larger', function () {
			const zoneWidthConstraints: GraphTypes.GraphColumnWidthConstraints =
				GraphHelpers.getGraphZoneWidthConstraintsFromGraphZones(
					mockGraphZones[mockGraphCentralZoneIndex].type,
					mockZonesWithHigherMaximumWidth,
					mockGraphWidth,
				);
			expect(zoneWidthConstraints.max).to.equal(mockGraphExpandabilityConstraint);
		});
	});

	describe('getGraphZoneModeConstants', function () {
		const defaultGraphZoneModeConstants: GraphTypes.GraphZoneModeConstants = {
			COMMIT_ZONE_LINE_WIDTH: 2,
			COMMIT_ZONE_GUTTER_WIDTH: 28,
			COMMIT_ZONE_PADDING_LEFT: 3,
			COMMIT_ZONE_PADDING_RIGHT: 3,
			COMMIT_COLUMN_WIDTH: 22,
			COMMIT_NODE_DIAMETER: 22,
			COMMIT_MERGE_NODE_DIAMETER: 12,
			COMMIT_NODE_TOP_OFFSET: 0,
			COMMIT_MERGE_NODE_LEFT_OFFSET: 5,
			COMMIT_MERGE_NODE_TOP_OFFSET: 5,
			COMMIT_ZONE_AVATAR_DIAMETER: 18,
			COMMIT_ZONE_VIEWPORT_WIDTH_MIN: 56,
			COMMIT_ZONE_SHOW_ICON_WIDTH: 56,
			IS_COMPACT: false,
			RADIUS_DIFF_MERGE_NODE_COMMIT_NODE: 5,
		};

		const compactGraphZoneModeConstants: GraphTypes.GraphZoneModeConstants = {
			COMMIT_ZONE_LINE_WIDTH: 1,
			COMMIT_ZONE_GUTTER_WIDTH: 10,
			COMMIT_ZONE_PADDING_LEFT: 1,
			COMMIT_ZONE_PADDING_RIGHT: 1,
			COMMIT_COLUMN_WIDTH: 10,
			COMMIT_NODE_DIAMETER: 10,
			COMMIT_MERGE_NODE_DIAMETER: 10,
			COMMIT_NODE_TOP_OFFSET: 6,
			COMMIT_MERGE_NODE_LEFT_OFFSET: 0,
			COMMIT_MERGE_NODE_TOP_OFFSET: 6,
			COMMIT_ZONE_AVATAR_DIAMETER: 8,
			COMMIT_ZONE_VIEWPORT_WIDTH_MIN: 22,
			COMMIT_ZONE_SHOW_ICON_WIDTH: 45,
			IS_COMPACT: true,
			RADIUS_DIFF_MERGE_NODE_COMMIT_NODE: 0,
		};

		it('should return default values if no mode is provided', function () {
			expect(GraphHelpers.getGraphZoneModeConstants()).to.deep.equal(defaultGraphZoneModeConstants);
		});

		it('should return default values if non-compact is provided', function () {
			expect(GraphHelpers.getGraphZoneModeConstants('test' as GraphConstants.GraphColumnMode)).to.deep.equal(
				defaultGraphZoneModeConstants,
			);
		});

		it('should return compact values if compact mode is provided', function () {
			expect(GraphHelpers.getGraphZoneModeConstants(GraphConstants.GraphColumnMode.Compact)).to.deep.equal(
				compactGraphZoneModeConstants,
			);
		});
	});

	describe('makeGetKeyForCell', function () {
		const mockGetKeyForCell = GraphHelpers.makeGetKeyForCell(mockProcessedGraphRows);
		it('should return a row sha if not the last row', function () {
			expect(mockGetKeyForCell(1)).to.equal(mockProcessedGraphRows[1].sha);
		});

		it('should return SHOW_MORE_COMMITS for the last row', function () {
			expect(mockGetKeyForCell(mockProcessedGraphRows.length)).to.equal('SHOW_MORE_COMMITS');
		});
	});

	describe('getZoneWidthWithVerticalScrollbar', function () {
		it('should return the zone current width if active', function () {
			expect(
				GraphHelpers.getZoneWidthWithVerticalScrollbar(mockVisibleGraphZone.type, mockActiveGraphZones),
			).to.equal(mockVisibleGraphZone.currentWidth);
		});

		it('should return the zone minimum width if hidden', function () {
			expect(
				GraphHelpers.getZoneWidthWithVerticalScrollbar(mockHiddenGraphZone.type, mockActiveGraphZones),
			).to.equal(mockHiddenGraphZone.minimumWidth);
		});
	});

	describe('isLastColumn', function () {
		it('should return false if the column is not the last column', function () {
			expect(GraphHelpers.isLastColumn(mockGraphZones[0].type, mockGraphZones)).to.be.false;
		});

		it('should return true if the column is the last column', function () {
			expect(GraphHelpers.isLastColumn(mockGraphZones[mockGraphZones.length - 1].type, mockGraphZones)).to.be
				.true;
		});
	});

	describe('rowContainsCurrentHeadRef', function () {
		it('should return false if the row does not contain the current head ref', function () {
			expect(GraphHelpers.rowContainsCurrentHeadRef(mockProcessedGraphRows[0])).to.be.false;
		});

		it('should return true if the row contains the current head ref', function () {
			expect(GraphHelpers.rowContainsCurrentHeadRef(mockProcessedGraphRows[1])).to.be.true;
		});
	});

	describe('getHeadRefShaFromGraphRows', function () {
		it('should return the sha of the head ref', function () {
			expect(GraphHelpers.getHeadRefShaFromGraphRows(mockGraphRows)).to.equal(mockHeadRefSha);
		});

		it('should return undefined if the head ref is not found', function () {
			expect(GraphHelpers.getHeadRefShaFromGraphRows([])).to.be.undefined;
		});
	});

	describe('workDirStatsHaveChanges', function () {
		const mockWorkDirStatsWithAdded = { added: 1, modified: 0, deleted: 0 };
		const mockWorkDirStatsWithModified = { added: 0, modified: 1, deleted: 0 };
		const mockWorkDirStatsWithDeleted = { added: 0, modified: 0, deleted: 1 };
		const mockWorkDirStatsWithNoChanges = { added: 0, modified: 0, deleted: 0 };
		it('should return true if the work dir stats have changes', function () {
			expect(GraphHelpers.workDirStatsHaveChanges(mockWorkDirStatsWithAdded)).to.be.true;
			expect(GraphHelpers.workDirStatsHaveChanges(mockWorkDirStatsWithModified)).to.be.true;
			expect(GraphHelpers.workDirStatsHaveChanges(mockWorkDirStatsWithDeleted)).to.be.true;
		});

		it('should return false if the work dir stats have no changes', function () {
			expect(GraphHelpers.workDirStatsHaveChanges(mockWorkDirStatsWithNoChanges)).to.be.false;
		});
	});

	describe('GraphRowHelper', function () {
		const mockGraphRowHelperProps: GraphHelpers.GraphRowHelperProps = {
			currentlyHoveredCommitSha: mockHeadRefSha,
			dimRowsOfSelectedCommit: mockDimRowsOfSelectedCommit,
			graphZones: mockGraphZones,
			highlightedShas: { [mockHeadRefSha]: true, [mockHeadRefParentSha]: true },
			hoveredRefGroup: mockHeadRefGroup,
			selectedShas: mockSelectedShas,
			processedRows: mockProcessedGraphRows,
			processedGraphRowBySha: mockProcessedGraphRowBySha,
			cssVariables: mockCssVariables,
			enabledScrollMarkerTypes: mockEnabledScrollMarkerTypes,
			height: mockHeight,
			hasMoreCommits: mockHasMoreCommits,
			isLoadingRows: mockIsLoadingRows,
		};

		const mockGraphRowHelper: GraphHelpers.GraphRowHelper = new GraphHelpers.GraphRowHelper(
			mockGraphRowHelperProps,
		);

		describe('getActiveGraphZone', function () {
			it('should return the graph zone if it is not hidden', function () {
				expect(mockGraphRowHelper.getActiveGraphZone(mockVisibleGraphZone.type)).to.equal(mockVisibleGraphZone);
			});

			it('should return undefined if the zone is hidden or doesnt exist', function () {
				expect(mockGraphRowHelper.getActiveGraphZone(mockHiddenGraphZone.type)).to.be.undefined;
				expect(mockGraphRowHelper.getActiveGraphZone('nonexistent' as any)).to.be.undefined;
			});
		});

		describe('getRow', function () {
			it('should return the row if it exists', function () {
				expect(mockGraphRowHelper.getRow(1)).to.equal(mockProcessedGraphRows[1]);
			});

			it('should return undefined if the row does not exist', function () {
				expect(mockGraphRowHelper.getRow(mockProcessedGraphRows.length + 1)).to.be.undefined;
			});
		});

		describe('getZoneWidth', function () {
			it('should return the width of a zone', function () {
				expect(mockGraphRowHelper.getZoneWidth(mockVisibleGraphZone.type)).to.equal(
					mockVisibleGraphZone.currentWidth,
				);
			});
		});

		describe('isDateTimeZoneActive', function () {
			it('should return false if the date time zone is hidden', function () {
				expect(mockGraphRowHelper.isDateTimeZoneActive()).to.be.false;
			});

			it('should return true if the date time zone is active', function () {
				const mockGraphZonesWithDateTimeZoneActive = JSON.parse(JSON.stringify(mockGraphZones));
				const dateTimeZoneIndex: number = mockGraphZonesWithDateTimeZoneActive.findIndex(
					(zone: any) => zone.type === GraphConstants.commitDateTimeZone,
				);
				(mockGraphZonesWithDateTimeZoneActive[dateTimeZoneIndex] as any).isHidden = false;
				const mockGraphRowHelperWithActiveDateTimeZone: GraphHelpers.GraphRowHelper =
					new GraphHelpers.GraphRowHelper({
						...mockGraphRowHelperProps,
						graphZones: mockGraphZonesWithDateTimeZoneActive,
					});
				expect(mockGraphRowHelperWithActiveDateTimeZone.isDateTimeZoneActive()).to.be.true;
			});
		});

		describe('isFirstRow', function () {
			it('should return true if the row is the first row', function () {
				expect(mockGraphRowHelper.isFirstRow(0)).to.be.true;
			});

			it('should return false if the row is not the first row', function () {
				expect(mockGraphRowHelper.isFirstRow(1)).to.be.false;
			});
		});

		describe('isHighlighted', function () {
			it('should return true if the row is highlighted', function () {
				expect(mockGraphRowHelper.isHighlighted(1)).to.be.true;
				expect(mockGraphRowHelper.isHighlighted(2)).to.be.true;
			});

			it('should return false if the row is not highlighted', function () {
				expect(mockGraphRowHelper.isHighlighted(0)).to.be.false;
			});
		});

		describe('isHovering', function () {
			it('should return true if the row is the hovered row', function () {
				expect(mockGraphRowHelper.isHovering(1)).to.be.true;
			});

			it('should return false if the row is not the hovered row', function () {
				expect(mockGraphRowHelper.isHovering(0)).to.be.false;
				expect(mockGraphRowHelper.isHovering(2)).to.be.false;
			});
		});

		describe('isMissingHoveredRefGroup', function () {
			const mockGraphRowHelperPropsWithoutHighlightedShas = Object.assign({}, mockGraphRowHelperProps);
			delete mockGraphRowHelperPropsWithoutHighlightedShas.highlightedShas;
			const mockGraphRowHelperWithNoHighlightedShas: GraphHelpers.GraphRowHelper =
				new GraphHelpers.GraphRowHelper(mockGraphRowHelperPropsWithoutHighlightedShas);

			const mockGraphRowHelperPropsWithoutHoveredRefGroup = Object.assign({}, mockGraphRowHelperProps);
			delete mockGraphRowHelperPropsWithoutHoveredRefGroup.hoveredRefGroup;
			const mockGraphRowHelperWithNoHoveredRefGroup: GraphHelpers.GraphRowHelper =
				new GraphHelpers.GraphRowHelper(mockGraphRowHelperPropsWithoutHoveredRefGroup);

			it('should return true if the row is missing the hovered ref group', function () {
				expect(mockGraphRowHelperWithNoHighlightedShas.isMissingHoveredRefGroup(0)).to.be.true;
				expect(mockGraphRowHelperWithNoHighlightedShas.isMissingHoveredRefGroup(2)).to.be.true;
			});

			it('should return false if the row is not missing the hovered ref group', function () {
				expect(mockGraphRowHelperWithNoHighlightedShas.isMissingHoveredRefGroup(1)).to.be.false;
			});

			const mockGraphRowHelperPropsWithDimCommitsProps = Object.assign(
				{},
				mockGraphRowHelperPropsWithoutHighlightedShas,
			);
			mockGraphRowHelperPropsWithDimCommitsProps.dimRowsOfSelectedCommit = true;
			const mockGraphRowHelperPropsWithDimCommits: GraphHelpers.GraphRowHelper = new GraphHelpers.GraphRowHelper(
				mockGraphRowHelperPropsWithDimCommitsProps,
			);

			it('should return true if the row is missing the selected ref group', function () {
				expect(mockGraphRowHelperPropsWithDimCommits.isMissingHoveredRefGroup(1)).to.be.true;
			});

			it('should return false if the row is not missing the selected ref group', function () {
				expect(mockGraphRowHelperPropsWithDimCommits.isMissingHoveredRefGroup(2)).to.be.false;
			});

			it("should return true if the commit hasn't refs", function () {
				const mockCurrentRefGroup = [
					{
						id: mockBaseRemoteRef.id,
						name: mockBaseRemoteRef.name,
						refType: RefConstants.refTypes.REMOTE,
					},
				];
				const mockCurrentGraphRowHelperProps = Object.assign({}, mockGraphRowHelperPropsWithoutHighlightedShas);
				mockCurrentGraphRowHelperProps.hoveredRefGroup = mockCurrentRefGroup;
				const mockCurrentGraphRowHelpers: GraphHelpers.GraphRowHelper = new GraphHelpers.GraphRowHelper(
					mockCurrentGraphRowHelperProps,
				);
				expect(mockCurrentGraphRowHelpers.isMissingHoveredRefGroup(4)).to.be.true;
			});

			it('should return false if the commit has refs and reftype is tag', function () {
				const mockCurrentRefGroup = [
					{
						id: mockBaseTagRef.id,
						name: mockBaseTagRef.name,
						refType: RefConstants.refTypes.TAG,
					},
				];
				const mockCurrentGraphRowHelperProps = Object.assign({}, mockGraphRowHelperPropsWithoutHighlightedShas);
				mockCurrentGraphRowHelperProps.hoveredRefGroup = mockCurrentRefGroup;
				const mockCurrentGraphRowHelpers: GraphHelpers.GraphRowHelper = new GraphHelpers.GraphRowHelper(
					mockCurrentGraphRowHelperProps,
				);
				expect(mockCurrentGraphRowHelpers.isMissingHoveredRefGroup(5)).to.be.false;
			});

			it("should return false if the commit hasn't refs and reftype is tag", function () {
				const mockCurrentRefGroup = [
					{
						id: mockBaseTagRef.id,
						name: mockBaseTagRef.name,
						refType: RefConstants.refTypes.TAG,
					},
				];
				const mockCurrentGraphRowHelperProps = Object.assign({}, mockGraphRowHelperPropsWithoutHighlightedShas);
				mockCurrentGraphRowHelperProps.hoveredRefGroup = mockCurrentRefGroup;
				const mockCurrentGraphRowHelpers: GraphHelpers.GraphRowHelper = new GraphHelpers.GraphRowHelper(
					mockCurrentGraphRowHelperProps,
				);
				expect(mockCurrentGraphRowHelpers.isMissingHoveredRefGroup(5)).to.be.false;
			});

			it('should return false if there are any highlighted shas', function () {
				expect(mockGraphRowHelper.isMissingHoveredRefGroup(0)).to.be.false;
				expect(mockGraphRowHelper.isMissingHoveredRefGroup(1)).to.be.false;
				expect(mockGraphRowHelper.isMissingHoveredRefGroup(2)).to.be.false;
			});

			it('should return false if the row is not found', function () {
				expect(mockGraphRowHelperWithNoHighlightedShas.isMissingHoveredRefGroup(3)).to.be.false;
			});

			it('should return false if there is no hovered ref group', function () {
				expect(mockGraphRowHelperWithNoHoveredRefGroup.isMissingHoveredRefGroup(0)).to.be.false;
				expect(mockGraphRowHelperWithNoHoveredRefGroup.isMissingHoveredRefGroup(1)).to.be.false;
				expect(mockGraphRowHelperWithNoHoveredRefGroup.isMissingHoveredRefGroup(2)).to.be.false;
			});
		});

		const mockGraphRefGroupByNameWithoutRemotes = [[mockGraphHeadRef]];

		const mockGraphRefGroupByNameWithRemotes = [
			[mockGraphHeadRef],
			[
				{
					...mockGraphRemoteRef,
					fullName: `${mockGraphRemoteRef.owner}/${mockGraphRemoteRef.name}`,
				},
			],
		];

		const mockGraphRefGroupByNameWithRemotesWithoutOwner = [
			[mockGraphHeadRef],
			[
				{
					...mockGraphRemoteRefWithoutOwner,
					fullName: mockGraphRemoteRefWithoutOwner.name,
				},
			],
		];

		describe('getGraphRefGroupsByNameForRow', function () {
			const testCases = [
				{ desc: 'when row is not found', rowIndex: 1000, result: [] },
				// eslint-disable-next-line max-len
				{
					desc: 'when row is found without grouped branches',
					rowIndex: 1,
					result: mockGraphRefGroupByNameWithoutRemotes,
				},
				{
					desc: 'when row is found with grouped branches with ref owner',
					rowIndex: 3,
					result: mockGraphRefGroupByNameWithRemotes,
				},
				{
					desc: 'when row is found with grouped branches without ref owner',
					rowIndex: 6,
					result: mockGraphRefGroupByNameWithRemotesWithoutOwner,
				},
			];

			testCases.forEach(testCase => {
				it(testCase.desc, function () {
					expect(mockGraphRowHelper.getGraphRefGroupsByNameForRow(testCase.rowIndex)).to.eql(testCase.result);
				});
			});
		});

		describe('getSelectedRefGroupByName', function () {
			const testCases = [
				{ desc: 'when row is not found', rowIndex: 1000, selection: new Set<string>(), result: [] },
				// eslint-disable-next-line max-len
				{
					desc: 'when row is found without grouped branches',
					rowIndex: 1,
					selection: new Set<string>([mockProcessedGraphRows[1].sha]),
					result: mockGraphRefGroupByNameWithoutRemotes,
				},
				// eslint-disable-next-line max-len
				{
					desc: 'when row is found with grouped branches',
					rowIndex: 3,
					selection: new Set<string>([mockProcessedGraphRows[3].sha]),
					result: mockGraphRefGroupByNameWithRemotes,
				},
			];

			testCases.forEach(testCase => {
				it(testCase.desc, function () {
					const mockCurrentGraphRowHelper: GraphHelpers.GraphRowHelper = new GraphHelpers.GraphRowHelper({
						...mockGraphRowHelperProps,
						selectedShas: testCase.selection,
					});

					expect(mockCurrentGraphRowHelper.getSelectedRefGroupByName()).to.eql(testCase.result);
				});
			});
		});

		describe('isLastColumn', function () {
			const firstColumnType = mockGraphZones[0].type;
			const lastColumnType = mockGraphZones[mockGraphZones.length - 1].type;
			it('should return true if the column is the last column', function () {
				expect(mockGraphRowHelper.isLastColumn(lastColumnType)).to.be.true;
			});

			it('should return false if the column is not the last column', function () {
				expect(mockGraphRowHelper.isLastColumn(firstColumnType)).to.be.false;
			});
		});

		describe('isSelected', function () {
			it('should return true if the commit row is selected', function () {
				expect(mockGraphRowHelper.isSelected(2)).to.be.true;
			});

			it('should return false if the commit row is not selected', function () {
				expect(mockGraphRowHelper.isSelected(1)).to.be.false;
			});
		});

		describe('isSingleSelected', function () {
			it('should return true if the commit row is single selected', function () {
				expect(mockGraphRowHelper.isSingleSelected(2)).to.be.true;
			});

			it('should return false if the commit row is not single selected', function () {
				expect(mockGraphRowHelper.isSingleSelected(1)).to.be.false;
			});
		});

		describe('hasTimeline', function () {
			it('should return true if the row has a timeline entry', function () {
				expect(mockGraphRowHelper.hasTimeline(2)).to.be.true;
			});

			it('should return false if the row does not have a timeline entry', function () {
				expect(mockGraphRowHelper.hasTimeline(0)).to.be.false;
				expect(mockGraphRowHelper.hasTimeline(1)).to.be.false;
			});
		});

		describe('rowContainsCurrentHeadRef', function () {
			it('should return true if the row contains the current head ref', function () {
				expect(mockGraphRowHelper.rowContainsCurrentHeadRef(mockProcessedGraphRows[1])).to.be.true;
			});

			it('should return false if the row does not contain the current head ref', function () {
				expect(mockGraphRowHelper.rowContainsCurrentHeadRef(mockProcessedGraphRows[0])).to.be.false;
				expect(mockGraphRowHelper.rowContainsCurrentHeadRef(mockProcessedGraphRows[2])).to.be.false;
			});
		});

		describe('getScrollToAlignment', function () {
			const testCases = [
				// eslint-disable-next-line max-len
				{
					rowHeight: 28,
					clientHeight: 400,
					scrollTop: 0,
					scrollToIndex: -1,
					result: GraphConstants.SCROLL_TO_ALIGNMENT_AUTO,
				},
				// eslint-disable-next-line max-len
				{
					rowHeight: 2,
					clientHeight: 400,
					scrollTop: 60,
					scrollToIndex: 50,
					result: GraphConstants.SCROLL_TO_ALIGNMENT_AUTO,
				},
				// eslint-disable-next-line max-len
				{
					rowHeight: 28,
					clientHeight: 400,
					scrollTop: 30,
					scrollToIndex: 20,
					result: GraphConstants.SCROLL_TO_ALIGNMENT_CENTER,
				},
			];

			testCases.forEach(testCase => {
				const { rowHeight, clientHeight, scrollTop, scrollToIndex, result } = testCase;

				let desc = `For rowHeight ${rowHeight}, clientHeight ${clientHeight}, scrollTop ${scrollTop}`;
				desc += ` and scrollToIndex ${scrollToIndex}: it should return '${result}'`;

				it(desc, function () {
					expect(
						GraphHelpers.getScrollToAlignment(rowHeight, clientHeight, scrollTop, scrollToIndex),
					).to.equal(result);
				});
			});
		});
	});
});
