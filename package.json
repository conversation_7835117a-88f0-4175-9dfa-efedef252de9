{"name": "@gitkraken/gitkraken-components", "version": "13.0.0-vnext.7", "description": "GitKraken React components shared between the app and GitLens", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"analyze:bundle": "webpack --mode production --env analyzeBundle", "clean": "git clean -xdff -e /.vscode/", "compile": "webpack", "lint": "eslint", "build": "yarn compile --mode production && yarn build:types", "build:dev": "yarn compile && yarn build:types", "build:types": "tsc --emitDeclarationOnly --incremental false", "prepack": "yarn build", "prepublishOnly": "yarn lint && yarn check", "pretty": "prettier --write . --experimental-cli", "pretty:check": "prettier --check . --experimental-cli", "test": "mocha --config .mocharc.js", "update-licenses": "node ./scripts/generateLicenses.mjs", "verifyAndCompile": "yarn lint && yarn check && yarn test && yarn compile", "watch": "yarn compile --watch --mode development"}, "author": "", "license": "SEE LICENSE IN LICENSE", "dependencies": {"@axosoft/react-virtualized": "9.22.3-gitkraken.3", "classnames": "2.5.1", "re-resizable": "6.11.2", "react": "19.0.0", "react-bootstrap": "2.10.7", "react-dom": "19.0.0", "react-dragula": "1.1.17", "react-onclickoutside": "6.13.2"}, "peerDependencies": {"react": "19.0.0"}, "devDependencies": {"@eamodio/eslint-lite-webpack-plugin": "0.2.0", "@eslint/compat": "1.3.1", "@eslint/js": "9.31.0", "@swc/core": "1.12.11", "@types/chai": "5.2.2", "@types/chai-spies": "1.0.6", "@types/dragula": "3.7.5", "@types/mocha": "10.0.10", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@types/react-dragula": "1.1.3", "@types/react-onclickoutside": "6.7.10", "@types/react-virtualized": "^9.22.2", "@typescript-eslint/parser": "8.36.0", "chai": "5.2.1", "chai-spies": "1.1.0", "css-loader": "7.1.2", "css-minimizer-webpack-plugin": "7.0.2", "cssnano-preset-advanced": "7.0.7", "esbuild": "0.25.6", "esbuild-loader": "4.3.0", "eslint": "9.31.0", "eslint-import-resolver-typescript": "4.4.4", "eslint-plugin-anti-trojan-source": "1.1.1", "eslint-plugin-import-x": "4.16.1", "eslint-plugin-jasmine": "4.2.2", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-react": "7.37.5", "eslint-plugin-react-hooks": "5.2.0", "fork-ts-checker-webpack-plugin": "9.1.0", "less": "4.3.0", "less-loader": "12.3.0", "license-checker-rseidelsohn": "4.4.2", "mini-css-extract-plugin": "2.9.2", "mocha": "11.7.1", "prettier": "3.6.2", "terser-webpack-plugin": "5.3.14", "ts-loader": "9.5.2", "ts-node": "10.9.2", "typescript": "5.8.3", "typescript-eslint": "8.36.0", "webpack": "5.100.1", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "6.0.1", "webpack-remove-empty-scripts": "1.1.1"}, "resolutions": {"@types/react": "19.0.0", "@types/react-dom": "19.0.0", "jackspeak": "2.1.1", "react": "19.0.0", "react-dom": "19.0.0"}, "packageManager": "yarn@1.22.22+sha1.ac34549e6aa8e7ead463a7407e1c7390f61a6610"}