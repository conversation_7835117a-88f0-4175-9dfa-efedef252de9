import type { GridCellProps, GridCellRangeProps, GridCellRangeRenderer, SizeAndPositionData } from 'react-virtualized';
import type { CSSProperties, ReactElement } from 'react';
import { isValidElement } from 'react';
import type { Style } from '../generic/GenericTypes';

export type GetRealKeyForCellFunc = (rowIndex: number, columnIndex?: number) => string;

export type CustomCellCacheValue = { realKeyForCell: string; cell?: ReactElement<any> };
export type CustomCellCache = { [key: string]: CustomCellCacheValue };

function warnAboutMissingStyle(parent: object, renderedCell?: ReactElement): void {
	if (isValidElement<{ children: any }>(renderedCell)) {
		// If the direct child is a CellMeasurer, then we should check its child
		// See issue #611
		if (renderedCell.type && (renderedCell.type as any).__internalCellMeasurerFlag) {
			renderedCell = renderedCell.props.children;
		}

		if (
			isValidElement<{ style: CSSProperties }>(renderedCell) &&
			renderedCell?.props?.style === undefined &&
			(parent as any).__warnedAboutMissingStyle !== true
		) {
			(parent as any).__warnedAboutMissingStyle = true;

			console.warn('Rendered cell should include style property for positioning.');
		}
	}
}

export const makeSmartCellRangeRenderer =
	(getRealKeyForCell: GetRealKeyForCellFunc): GridCellRangeRenderer =>
	({
		cellCache,
		cellRenderer,
		columnSizeAndPositionManager,
		columnStartIndex,
		columnStopIndex,
		deferredMeasurementCache,
		horizontalOffsetAdjustment,
		isScrolling,
		parent, // Grid (or List or Table)
		rowSizeAndPositionManager,
		rowStartIndex,
		rowStopIndex,
		styleCache,
		verticalOffsetAdjustment,
		visibleColumnIndices,
		visibleRowIndices,
	}: GridCellRangeProps): React.ReactNode[] => {
		const renderedCells: React.ReactNode[] = [];

		// Note: we are using our own type for the cellCache instead of the defined type
		// of "react-virtualized" (which is "ReactElement<any>""). Take a look at the
		// "flow-typed" definition or at the source code of the library for more details.
		// This does not affect the functioning since this callback is managed by us only.
		const customCellCache: CustomCellCache = cellCache as any;

		// Browsers have native size limits for elements (eg Chrome 33M pixels, IE 1.5M pixes).
		// User cannot scroll beyond these size limitations.
		// In order to work around this, ScalingCellSizeAndPositionManager compresses offsets.
		// We should never cache styles for compressed offsets though as this can lead to bugs.
		// See issue #576 for more.
		const areOffsetsAdjusted: boolean =
			columnSizeAndPositionManager.areOffsetsAdjusted() || rowSizeAndPositionManager.areOffsetsAdjusted();

		const canCacheStyle: boolean = !isScrolling && !areOffsetsAdjusted;

		for (let rowIndex: number = rowStartIndex; rowIndex <= rowStopIndex; rowIndex += 1) {
			const rowDatum = rowSizeAndPositionManager.getSizeAndPositionOfCell(rowIndex);

			for (let columnIndex: number = columnStartIndex; columnIndex <= columnStopIndex; columnIndex += 1) {
				const columnDatum: SizeAndPositionData =
					columnSizeAndPositionManager.getSizeAndPositionOfCell(columnIndex);
				const isVisible: boolean =
					columnIndex >= visibleColumnIndices.start &&
					columnIndex <= visibleColumnIndices.stop &&
					rowIndex >= visibleRowIndices.start &&
					rowIndex <= visibleRowIndices.stop;
				const key = `${rowIndex}-${columnIndex}`;
				let style: Style;

				// Cache style objects so shallow-compare doesn't re-render unnecessarily.
				if (canCacheStyle && styleCache[key]) {
					style = styleCache[key] as any;
				} else if (deferredMeasurementCache && !deferredMeasurementCache.has(rowIndex, columnIndex)) {
					// In deferred mode, cells will be initially rendered before we know their size.
					// Don't interfere with CellMeasurer's measurements by setting an invalid size.
					// Position not-yet-measured cells at top/left 0,0,
					// And give them width/height of 'auto' so they can grow larger than the parent Grid if necessary.
					// Positioning them further to the right/bottom influences their measured size.
					style = {
						height: 'auto',
						left: 0,
						position: 'absolute',
						top: 0,
						width: 'auto',
					};
				} else {
					style = {
						height: rowDatum.size,
						left: columnDatum.offset + horizontalOffsetAdjustment,
						position: 'absolute',
						top: rowDatum.offset + verticalOffsetAdjustment,
						width: columnDatum.size,
					};

					styleCache[key] = style;
				}

				const cellRendererParams: GridCellProps = {
					columnIndex: columnIndex,
					isScrolling: isScrolling,
					isVisible: isVisible,
					key: key,
					parent: parent,
					rowIndex: rowIndex,
					style: style,
				};

				let renderedCell: React.ReactNode | undefined;

				// Avoid re-creating cells while scrolling.
				// This can lead to the same cell being created many times and can cause performance issues for 'heavy' cells.
				// If a scroll is in progress- cache and reuse cells.
				// This cache will be thrown away once scrolling completes.
				// However if we are scaling scroll positions and sizes, we should also avoid caching.
				// This is because the offset changes slightly as scroll position changes and caching leads to stale values.
				// For more info refer to issue #395
				if (isScrolling && !horizontalOffsetAdjustment && !verticalOffsetAdjustment) {
					const realKeyForCell: string = getRealKeyForCell(rowIndex, columnIndex);
					if (!customCellCache[key] || customCellCache[key].realKeyForCell !== realKeyForCell) {
						customCellCache[key] = {
							realKeyForCell: realKeyForCell,
							cell: cellRenderer(cellRendererParams) as ReactElement,
						};
					}

					renderedCell = customCellCache[key].cell;

					// If the user is no longer scrolling, don't cache cells.
					// This makes dynamic cell content difficult for users and would also lead to a heavier memory footprint.
				} else {
					renderedCell = cellRenderer(cellRendererParams);
				}

				if (renderedCell === null || renderedCell === undefined) {
					continue;
				}

				if (process.env.NODE_ENV !== 'production') {
					warnAboutMissingStyle(parent, renderedCell as ReactElement);
				}

				renderedCells.push(renderedCell);
			}
		}

		return renderedCells;
	};
