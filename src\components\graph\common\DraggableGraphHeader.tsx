import classnames from 'classnames';
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ReactElement } from 'react';
import React from 'react';
import { Overlay, Popover } from 'react-bootstrap';
import type { GraphZone, GraphZoneType } from '../../../domain/graph/GraphConstants';
import {
	changesZone,
	HEADER_ROW_HEIGHT,
	HEADER_ROW_MARGIN_BOTTOM,
	refZone,
	ResizableHandleCorrection,
} from '../../../domain/graph/GraphConstants';
import {
	getGraphZoneFromGraphZones,
	getGraphZoneIconByType,
	getGraphZoneWidthConstraintsFromGraphZones,
	isLastColumn,
	parseContext,
	shouldUseColumnHeaderIcons,
} from '../../../domain/graph/GraphHelpers';
import type {
	ExcludeRefsById,
	GetExternalIcon,
	GraphColumnSetting,
	GraphItemContext,
	GraphRef,
	GraphRefOptData,
	IncludeOnlyRefsById,
	OnFilterColumnClick,
	OnGraphZoneResize,
	OnGraphZoneResizeEnd,
	OnGraphZoneResizeFromPropChange,
	OnGraphZoneResizeStart,
	OnSettingsClick,
	OnToggleRefNodesShown,
} from '../../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';
import { refTypes } from '../../../domain/ref/RefConstants';
import type {
	OnMouseEnter,
	OnMouseLeave,
	OnResizeFromPropChangeParams,
	OnResizeParams,
} from '../../../domain/ui/UiTypes';
import Resizable from '../../resizable/Resizable';
import { Icon } from '../refzone/Icon';
import { getRefIconAndTooltipForRef } from '../refzone/RefNode';

type GraphHeaderProps = {
	columnSetting?: GraphColumnSetting;
	enableResizer?: boolean;
	enableShowHideRefsOptions: boolean;
	getExternalIcon: GetExternalIcon;
	graphWidth: number;
	graphHeight: number;
	graphZones: GraphZone[];
	graphZoneType: GraphZoneType;
	excludeRefsById: ExcludeRefsById;
	id: string;
	includeOnlyRefsById: IncludeOnlyRefsById;
	isDraggable: boolean;
	isDroppable: boolean;
	rowsStatsLoading: boolean;
	repoPath: string;
	onFilterColumnClick: OnFilterColumnClick;
	onHover?: OnMouseEnter;
	onResize?: OnGraphZoneResize;
	onResizeEnd?: OnGraphZoneResizeEnd;
	onResizeFromPropChange?: OnGraphZoneResizeFromPropChange;
	onResizeStart?: OnGraphZoneResizeStart;
	onSettingsClick?: OnSettingsClick;
	onToggleRefNodesShown: OnToggleRefNodesShown;
	onUnhover?: OnMouseLeave;
	settingsContext?: GraphItemContext;
	showRemoteNamesOnRefs: boolean;
	translate: TranslationFn;
};

type GraphHeaderState = {
	showPopoverMenu: boolean;
};

class GraphHeader extends React.Component<GraphHeaderProps, GraphHeaderState> {
	private headIconsRef = React.createRef<HTMLDivElement>();
	private containerRef = React.createRef<HTMLDivElement>();
	private popoverMenuTargetRef = React.createRef<HTMLDivElement>();

	constructor(props: GraphHeaderProps) {
		super(props);

		this.state = {
			showPopoverMenu: false,
		};
	}

	onHiddenRefsClick: MouseEventHandler = e => {
		const { showPopoverMenu } = this.state;
		e.stopPropagation();

		this.setState({
			showPopoverMenu: !showPopoverMenu,
		});
	};

	onShowRefNodes: OnToggleRefNodesShown = (
		event: React.MouseEvent<any>,
		refs: GraphRefOptData[],
		visible: boolean,
	): void => {
		this.props.onToggleRefNodesShown(event, refs, visible);

		if (Object.keys(this.props.excludeRefsById).length === 0) {
			this.setState({
				showPopoverMenu: true,
			});
		}
	};

	compareGraphRefOpts(a: GraphRefOptData, b: GraphRefOptData): number {
		const comparationResult = a.name.localeCompare(b.name);
		if (comparationResult === 0) {
			// If names are equals
			if (a.type === refTypes.REMOTE) {
				return -1;
			}
		}
		return comparationResult;
	}

	override render(): ReactElement<any> | null {
		const {
			columnSetting,
			enableResizer,
			enableShowHideRefsOptions,
			getExternalIcon,
			graphWidth,
			graphZones,
			graphZoneType,
			excludeRefsById,
			includeOnlyRefsById,
			isDraggable,
			onFilterColumnClick,
			onHover,
			onResize,
			onResizeEnd,
			onResizeFromPropChange,
			onResizeStart,
			onUnhover,
			onSettingsClick,
			rowsStatsLoading,
			settingsContext,
			showRemoteNamesOnRefs,
			translate,
		} = this.props;

		const { showPopoverMenu } = this.state;

		const graphZone: GraphZone | undefined = getGraphZoneFromGraphZones(graphZoneType, graphZones);
		if (!graphZone) {
			return null;
		}

		const isLastHeader: boolean = isLastColumn(graphZoneType, graphZones);
		const headerWidth: number = graphZone.currentWidth - ResizableHandleCorrection;

		const headerIdAndKey = `${graphZoneType}-header`;
		const showHeaderIcon = shouldUseColumnHeaderIcons(graphZone);
		const isFilterable: boolean = columnSetting?.isFilterable || false;

		const reduceIconSize = showHeaderIcon && (isFilterable || enableShowHideRefsOptions);
		const columnsBtnBaseClassnames = classnames('columns-btn', { 'small-btn': reduceIconSize });

		let maybeHideRefIconBtn: ReactElement<'div'> | null = null;
		let optsPopoverMenu = null;

		if (graphZoneType === refZone) {
			const excludeRefsByIdKeysAndValues = Object.entries(excludeRefsById);
			const includeOnlyItems = Object.values(includeOnlyRefsById);
			const hasIncludes = includeOnlyItems.length > 0;
			const hasIncludesWithTags =
				hasIncludes && includeOnlyItems.some((refOptData: any) => refOptData.type === refTypes.TAG);
			const excludeRefsFiltered = excludeRefsByIdKeysAndValues.filter(item => {
				const refOpts = item[1];
				return refOpts.type === refTypes.TAG
					? !hasIncludes || !hasIncludesWithTags || !includeOnlyRefsById[item[0]]
					: !includeOnlyRefsById[item[0]];
			});
			const numOfExcludedRefs: number = excludeRefsFiltered.length;

			if (numOfExcludedRefs > 0) {
				optsPopoverMenu = excludeRefsFiltered
					.sort((a, b) => this.compareGraphRefOpts(a[1], b[1]))
					.map((refIdAndOpts: [string, GraphRefOptData], i: number) => {
						const refId = refIdAndOpts[0];
						const refOpts = refIdAndOpts[1];
						excludeRefsById[refId].id = refId;

						const ref: GraphRef = {
							name: refOpts.name,
							refType: refOpts.type,
							owner: refOpts.owner,
							avatarUrl: refOpts.avatarUrl,
						};

						const refName: string =
							(showRemoteNamesOnRefs || ref.name === '*') && ref.refType === refTypes.REMOTE
								? `${ref.owner}/${ref.name}`
								: ref.name;
						return (
							<li
								id={refId}
								key={refId}
								onClick={event => this.onShowRefNodes(event, [excludeRefsById[refId]], true)}
							>
								{getRefIconAndTooltipForRef(getExternalIcon, ref, translate, i)}
								<span className="ml1 text-ellipsis">{refName}</span>
							</li>
						);
					});

				maybeHideRefIconBtn = enableShowHideRefsOptions ? (
					<div
						className={classnames(
							columnsBtnBaseClassnames,
							'right',
							'text-normal',
							'button',
							showPopoverMenu ? 'active' : null,
						)}
						ref={this.popoverMenuTargetRef}
					>
						<Icon
							icon={<span onClick={this.onHiddenRefsClick}>{getExternalIcon('hide')}</span>}
							tooltipId="header-ref-node-hidden-refs-btn"
							tooltipText={translate('GraphHeader-HiddenRefs-btn')}
						/>
					</div>
				) : null;
			}
		}

		const maybeFilterButton = isFilterable ? (
			<div
				className={classnames(columnsBtnBaseClassnames, 'columns-filter', 'right', 'fs-1', {
					filtering: columnSetting?.isFilterActive || false,
				})}
			>
				<Icon
					icon={
						<span
							className="columns-filter-icon text-disabled hover-icon"
							id={`${graphZoneType}-header-filter`}
							onClick={e => onFilterColumnClick(e, graphZoneType)}
							style={{ height: HEADER_ROW_HEIGHT, marginBottom: HEADER_ROW_MARGIN_BOTTOM }}
						>
							{getExternalIcon('filter')}
						</span>
					}
					tooltipId="header-columns-filter"
					tooltipText={translate('GraphHeader-Filter')}
				/>
			</div>
		) : null;

		const maybeLoadingIcon =
			!showHeaderIcon && graphZoneType === changesZone && rowsStatsLoading ? (
				<span className={classnames(columnsBtnBaseClassnames, 'spinner')}>{getExternalIcon('loading')}</span>
			) : null;

		const headerHideOverlayOptions = (
			<Overlay
				container={this.containerRef.current}
				onHide={
					showPopoverMenu
						? () => {
								this.setState({
									showPopoverMenu: false,
								});
						  }
						: undefined
				}
				placement={'bottom'}
				rootClose={showPopoverMenu}
				show={showPopoverMenu}
				target={this.popoverMenuTargetRef.current}
			>
				{props => (
					<Popover className={classnames({ 'is-last-header': isLastHeader })} id="opts-popover" {...props}>
						<ul>{optsPopoverMenu}</ul>
					</Popover>
				)}
			</Overlay>
		);

		const maybeHeaderConfigurator =
			isLastHeader && onSettingsClick ? (
				<div
					className={classnames('columns-btn', 'columns-settings', 'right', 'fs-1')}
					data-vscode-context={parseContext(settingsContext)}
				>
					<Icon
						icon={
							<span
								className="columns-settings-icon text-disabled hover-icon"
								onClick={e => onSettingsClick(e, graphWidth)}
							>
								{getExternalIcon('settings')}
							</span>
						}
						tooltipId="header-columns-settings"
						tooltipText={translate('GraphHeader-EditColumns')}
					/>
				</div>
			) : null;

		const iconsPanelWidth = this.headIconsRef?.current?.clientWidth ?? 0;

		const headerIconsPanel = (
			<div className="header-columns-icons" ref={this.headIconsRef}>
				{maybeHeaderConfigurator}
				{maybeLoadingIcon}
				{maybeFilterButton}
				{maybeHideRefIconBtn}
			</div>
		);

		const headerContent = showHeaderIcon ? (
			<>
				<div
					className={classnames(columnsBtnBaseClassnames, 'justify-start', {
						ml1: !reduceIconSize,
					})}
					style={{ width: headerWidth - iconsPanelWidth }}
					title={translate(graphZone.headerLabelUntranslated)}
				>
					{getExternalIcon(getGraphZoneIconByType(graphZoneType))}
				</div>
				{headerIconsPanel}
			</>
		) : (
			<>
				<div
					className="text-disabled text-ellipsis"
					style={{
						height: HEADER_ROW_HEIGHT,
						overflow: 'hidden',
						width: headerWidth - iconsPanelWidth,
					}}
				>
					<span className="fs-1 ml1 font-monospace">{translate(graphZone.headerLabelUntranslated)}</span>
				</div>
				{headerIconsPanel}
			</>
		);

		const HeaderSection: ReactElement<'div'> = (
			<div onMouseEnter={onHover} onMouseLeave={onUnhover}>
				{headerHideOverlayOptions}
				{headerContent}
			</div>
		);

		const headerWrapperClasses: string = classnames('panel-bg0', 'graph-header', {
			'is-draggable': isDraggable,
			'is-last-header': isLastHeader,
		});

		return (
			<div
				className={headerWrapperClasses}
				id={headerIdAndKey}
				key={headerIdAndKey}
				ref={this.containerRef}
				style={{
					zIndex: 2,
					// Following line is a fix for a weird preview bug associated with react-dnd and list components
					// https://github.com/react-dnd/react-dnd/issues/832#issuecomment-442071628
					transform: 'translate3d(0, 0, 0)',
					...(isLastHeader
						? {
								minWidth: headerWidth,
						  }
						: {
								display: 'flex',
								minWidth: graphZone.currentWidth,
								width: graphZone.currentWidth,
						  }),
				}}
			>
				{isLastHeader ? (
					HeaderSection
				) : (
					<Resizable
						className={headerWrapperClasses}
						enable={enableResizer}
						handleStyles={{ right: { right: 0 } }}
						height={HEADER_ROW_HEIGHT}
						id={`${graphZoneType}HeaderColumn`}
						onResize={
							onResize ? (dimensions: OnResizeParams) => onResize(graphZone, dimensions) : undefined
						}
						onResizeEnd={
							onResizeEnd ? (dimensions: OnResizeParams) => onResizeEnd(graphZone, dimensions) : undefined
						}
						onResizeFromPropChange={
							onResizeFromPropChange
								? (dim: OnResizeFromPropChangeParams) => onResizeFromPropChange(graphZone, dim)
								: undefined
						}
						onResizeStart={
							onResizeStart
								? (dimensions: OnResizeParams) => onResizeStart(graphZone, dimensions)
								: undefined
						}
						resizeContentClassName="z1"
						resizeEdge="right"
						resizeHandleClassName="z2 border-right"
						style={{
							display: 'flex',
						}}
						translate={translate}
						width={graphZone.currentWidth}
						widthConstraints={getGraphZoneWidthConstraintsFromGraphZones(
							graphZoneType,
							graphZones,
							graphWidth,
						)}
					>
						{HeaderSection}
					</Resizable>
				)}
			</div>
		);
	}
}

export const DraggableGraphHeader = GraphHeader;
