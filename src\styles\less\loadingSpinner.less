.gk-graph {
	.loading-spinner {
		@spinner-size: 50px;

		// animation variables
		@spin-start-delay: 0.25s; // delay before spinning rings
		@img-bg-opacity: 0.3; // opacity of kraken background layer
		@outer-rotate-cycle: 1s; // time for outer ring to complete 1 rotation
		@inner-rotate-cycle: 1s; // time for inner ring to complete 1 rotation

		&:not(.inline-block) {
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		.message {
			font-family: 'OpenSans', Arial, sans-serif;
			font-size: var(--fs-3);
			text-align: center;
			color: var(--text-selected);
			margin-top: 0.5rem;

			&:not(.inline-block) {
				font-weight: bold;
			}
		}

		.spinner {
			position: relative;

			&:not(.inline-block) {
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}

		.ring {
			position: absolute;
		}

		.outer,
		.inner,
		.bg-img {
			width: @spinner-size;
			height: @spinner-size;
		}

		.outer {
			animation: rotateOuter @outer-rotate-cycle infinite linear;
			animation-delay: @spin-start-delay;
		}

		.inner {
			animation: rotateInner @inner-rotate-cycle infinite linear;
			animation-delay: @spin-start-delay;
		}

		.bg-img {
			opacity: @img-bg-opacity;
		}

		@keyframes rotateOuter {
			0% {
				transform: rotate(0deg);
			}
			100% {
				transform: rotate(360deg);
			}
		}

		@keyframes rotateInner {
			0% {
				transform: rotate(360deg);
			}
			100% {
				transform: rotate(0deg);
			}
		}
	}
}
