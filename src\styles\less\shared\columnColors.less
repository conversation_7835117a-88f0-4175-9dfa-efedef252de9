@column-color-array:
	var(--graph-color-9) var(--graph-color-9-f10) var(--graph-color-9-f50) var(--graph-color-9-bg50)
		var(--graph-color-9-bg45) var(--graph-color-9-bg25) var(--graph-color-9-bg15),
	var(--graph-color-0) var(--graph-color-0-f10) var(--graph-color-0-f50) var(--graph-color-0-bg50)
		var(--graph-color-0-bg45) var(--graph-color-0-bg25) var(--graph-color-0-bg15),
	var(--graph-color-1) var(--graph-color-1-f10) var(--graph-color-1-f50) var(--graph-color-1-bg50)
		var(--graph-color-1-bg45) var(--graph-color-1-bg25) var(--graph-color-1-bg15),
	var(--graph-color-2) var(--graph-color-2-f10) var(--graph-color-2-f50) var(--graph-color-2-bg50)
		var(--graph-color-2-bg45) var(--graph-color-2-bg25) var(--graph-color-2-bg15),
	var(--graph-color-3) var(--graph-color-3-f10) var(--graph-color-3-f50) var(--graph-color-3-bg50)
		var(--graph-color-3-bg45) var(--graph-color-3-bg25) var(--graph-color-3-bg15),
	var(--graph-color-4) var(--graph-color-4-f10) var(--graph-color-4-f50) var(--graph-color-4-bg50)
		var(--graph-color-4-bg45) var(--graph-color-4-bg25) var(--graph-color-4-bg15),
	var(--graph-color-5) var(--graph-color-5-f10) var(--graph-color-5-f50) var(--graph-color-5-bg50)
		var(--graph-color-5-bg45) var(--graph-color-5-bg25) var(--graph-color-5-bg15),
	var(--graph-color-6) var(--graph-color-6-f10) var(--graph-color-6-f50) var(--graph-color-6-bg50)
		var(--graph-color-6-bg45) var(--graph-color-6-bg25) var(--graph-color-6-bg15),
	var(--graph-color-7) var(--graph-color-7-f10) var(--graph-color-7-f50) var(--graph-color-7-bg50)
		var(--graph-color-7-bg45) var(--graph-color-7-bg25) var(--graph-color-7-bg15),
	var(--graph-color-8) var(--graph-color-8-f10) var(--graph-color-8-f50) var(--graph-color-8-bg50)
		var(--graph-color-8-bg45) var(--graph-color-8-bg25) var(--graph-color-8-bg15);

.getColumnColors(@column-number) {
	@column-color-index: mod(@column-number, length(@column-color-array));
	@column-color-position: (@column-color-index + 1); // + 1 because less lists are 1 based
	@column-colors: extract(@column-color-array, @column-color-position);

	@column-color: extract(@column-colors, 1);
	@column-color-f10: extract(@column-colors, 2);
	@column-color-f50: extract(@column-colors, 3);
	@column-color-bg50: extract(@column-colors, 4);
	@column-color-bg45: extract(@column-colors, 5);
	@column-color-bg25: extract(@column-colors, 6);
	@column-color-bg15: extract(@column-colors, 7);
}
