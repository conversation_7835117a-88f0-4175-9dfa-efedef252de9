import type { <PERSON><PERSON><PERSON>ellP<PERSON>, GridCellRenderer } from 'react-virtualized';
import type { ReactElement } from 'react';
import React from 'react';
import {
	mergeConflictNodeType,
	mergeNodeType,
	unsupportedRebaseWarningNodeType,
	workDirType,
} from '../../../domain/commit/CommitConstants';
import type { GraphCommitDescDisplayMode, GraphZoneType } from '../../../domain/graph/GraphConstants';
import { commitMessageZone, graphCommitDescDisplayModes } from '../../../domain/graph/GraphConstants';
import { GraphRowHelper } from '../../../domain/graph/GraphHelpers';
import type {
	CommonGraphRowDispatchProps,
	CommonGraphRowProps,
	GraphItemContext,
	OnCommitMessageChange,
	OnWipMessageBlur,
	OnWipMessageFocus,
	ProcessedGraphRow,
} from '../../../domain/graph/GraphTypes';
import type { GetRealKeyForCellFunc } from '../../../domain/reactvirtualized/ReactVirtualizedHelpers';
import type { WorkDirStats } from '../../../domain/workdir/WorkDirTypes';
import GenericGraphZone from '../common/GenericGraphZone';
import WorkDirMessageInput from '../messagezone/WorkDirMessageInput';
import InlineWorkDirSummary from '../shared/InlineWorkDirSummary';

interface StateProps {
	graphCommitDescDisplayMode: GraphCommitDescDisplayMode;
	isCommitting: boolean;
	pendingCommitMessageSummary: string;
	workDirStats?: WorkDirStats;
	wipMessageEditable: boolean;
}

interface DispatchProps {
	onBlurWipNodeInput: OnWipMessageBlur;
	onFocusWipNodeInput: OnWipMessageFocus;
	onMessageChange: OnCommitMessageChange;
}

interface Props extends CommonGraphRowProps, CommonGraphRowDispatchProps, StateProps, DispatchProps {}

const makeCommitMessageZoneRowRenderer = (props: Props, getKeyForCell: GetRealKeyForCellFunc): GridCellRenderer => {
	const {
		dimMergeCommits,
		dimRowsOfSelectedCommit,
		processedRows,
		translate,
		getExternalIcon,
		graphCommitDescDisplayMode,
		highlightRowsOnRefHover,
		isInUnsupportedRebase,
		numGraphColumns,
		isCommitting,
		pendingCommitMessageSummary,
		workDirStats,
		onBlurWipNodeInput,
		onFocusWipNodeInput,
		onMessageChange,
		clearCurrentlyHoveredGraphCommit,
		currentlyHoveredCommitSha,
		onCommitContextMenu,
		onClickCommit,
		onDoubleClickCommit,
		setAsCurrentlyHoveredGraphCommit,
		suppressNonRefRowTooltips,
		wipMessageEditable,
	} = props;

	const rowHelper: GraphRowHelper = new GraphRowHelper(props);

	const rowRenderer = ({ rowIndex: index, style }: GridCellProps): ReactElement<any> | undefined => {
		const key: string = getKeyForCell(index);

		const graphRow: ProcessedGraphRow = processedRows[index];
		const { displaySummary, displayBody, sha, type, contexts, message } = graphRow;

		const context: GraphItemContext | undefined = contexts?.message || undefined;
		const rowContext: GraphItemContext | undefined = contexts?.row || undefined;

		const zoneType: GraphZoneType = commitMessageZone;
		const showColorStrip = rowHelper.isColumnFollowingGraphColumn(zoneType);
		const isLastColumn = rowHelper.isLastColumn(zoneType);
		const zoneWidth = rowHelper.getZoneWidth(zoneType);
		const verticalScrollWidth: number = rowHelper.getVerticalScrollWidth(zoneType);
		const isHovering: boolean = rowHelper.isHovering(index);

		const hasWorkDirChanges: boolean =
			type === workDirType && workDirStats !== undefined && Object.keys(workDirStats).length > 0;

		const maybeWorkDirSummary: ReactElement<typeof InlineWorkDirSummary> | null =
			hasWorkDirChanges && !wipMessageEditable ? (
				<InlineWorkDirSummary
					diffStats={workDirStats}
					getExternalIcon={getExternalIcon}
					translate={translate}
				/>
			) : null;

		const shouldDisplayCommitDesc: boolean =
			graphCommitDescDisplayMode === graphCommitDescDisplayModes.ALWAYS ||
			(graphCommitDescDisplayMode === graphCommitDescDisplayModes.ON_HOVER && isHovering);

		const messageInlineComponent =
			type === workDirType && !wipMessageEditable ? (
				<span style={{ display: 'flex', alignItems: 'stretch', textOverflow: 'ellipsis' }}>
					{shouldDisplayCommitDesc ? displaySummary : null}
					{maybeWorkDirSummary}
				</span>
			) : (
				<>
					<span className="message-zone--summary">{displaySummary}</span>
					{shouldDisplayCommitDesc && displayBody ? (
						<span className="message-zone--body">{displayBody}</span>
					) : null}
				</>
			);

		return hasWorkDirChanges && wipMessageEditable ? (
			<WorkDirMessageInput
				clearCurrentlyHoveredGraphCommit={clearCurrentlyHoveredGraphCommit}
				currentlyHoveredCommitSha={currentlyHoveredCommitSha}
				getExternalIcon={getExternalIcon}
				graphZoneType={zoneType}
				isCommitting={isCommitting}
				isHovering={rowHelper.isHovering(index)}
				isSelected={rowHelper.isSelected(index)}
				key={key}
				onBlur={onBlurWipNodeInput}
				onClickCommit={onClickCommit}
				onContextMenu={onCommitContextMenu}
				onFocus={onFocusWipNodeInput}
				onMessageChange={onMessageChange}
				setAsCurrentlyHoveredGraphCommit={setAsCurrentlyHoveredGraphCommit}
				sha={sha}
				style={style}
				translate={translate}
				value={pendingCommitMessageSummary}
				workDirStats={workDirStats}
			/>
		) : (
			<GenericGraphZone
				clearCurrentlyHoveredGraphCommit={clearCurrentlyHoveredGraphCommit}
				column={graphRow.column}
				columnForColoring={graphRow.columnForColoring}
				context={context}
				currentlyHoveredCommitSha={currentlyHoveredCommitSha}
				dimRowsOfSelectedCommit={dimRowsOfSelectedCommit}
				getExternalIcon={getExternalIcon}
				graphZoneType={zoneType}
				highlightRowsOnRefHover={highlightRowsOnRefHover}
				isDimmedMergeCommitRow={type === mergeNodeType && dimMergeCommits}
				isHighlighted={rowHelper.isHighlighted(index)}
				isHovering={isHovering}
				isInUnsupportedRebase={isInUnsupportedRebase}
				isLastColumn={isLastColumn}
				isMissingHoveredRefGroup={rowHelper.isMissingHoveredRefGroup(index)}
				isSelected={rowHelper.isSelected(index)}
				key={key}
				numGraphColumns={numGraphColumns}
				onClickCommit={onClickCommit}
				onContextMenu={onCommitContextMenu}
				onDoubleClickCommit={onDoubleClickCommit}
				rowContext={rowContext}
				setAsCurrentlyHoveredGraphCommit={setAsCurrentlyHoveredGraphCommit}
				sha={sha}
				showColorStrip={showColorStrip}
				showConflictIcon={type === mergeConflictNodeType || type === unsupportedRebaseWarningNodeType}
				showTimeline={rowHelper.hasTimeline(index)}
				style={style}
				title={suppressNonRefRowTooltips ? undefined : message}
				type={type}
				verticalScrollWidth={verticalScrollWidth}
				zoneWidth={zoneWidth}
			>
				{messageInlineComponent}
			</GenericGraphZone>
		);
	};

	return rowRenderer;
};

export default makeCommitMessageZoneRowRenderer;
