import { DEFAULT_COMMIT_SHA_LENGTH } from '../graph/GraphConstants';
import * as CommitConstants from './CommitConstants';
import type { Author, CommitType, Sha, ShortSha } from './CommitTypes';

export const hasPendingChanges: (type: CommitType) => boolean = type =>
	!(type === CommitConstants.commitNodeType || type === CommitConstants.mergeNodeType);

export const getShortSha = (sha?: Sha | null, shaLength: number = DEFAULT_COMMIT_SHA_LENGTH): ShortSha => {
	return !sha ? '' : sha.substring(0, shaLength);
};

const commitDescriptionWithDefaultLineEndingRegex = /^.*(:?\n){0,2}/m;
export function getCommitMessageDescription(commitMessage: string, lineEnding = '\n'): string {
	return commitMessage.replace(
		lineEnding === '\n'
			? commitDescriptionWithDefaultLineEndingRegex
			: new RegExp(`^.*(:?${lineEnding}){0,2}`, 'm'),
		'',
	);
}

const commitMessageCoauthorsRegex = /^co-authored-by:\s*([^\s<>]+(?:\s+[^\s<>]+)*)\s*<([^<>]+)>/gim;
export function getCommitMessageCoauthors(commitMessageDescription: string): Author[] {
	const result: Author[] = [];
	for (
		let match = commitMessageCoauthorsRegex.exec(commitMessageDescription);
		match !== null;
		match = commitMessageCoauthorsRegex.exec(commitMessageDescription)
	) {
		const [, name, email] = match;
		result.push({ name: name, email: email });
	}

	return result;
}
