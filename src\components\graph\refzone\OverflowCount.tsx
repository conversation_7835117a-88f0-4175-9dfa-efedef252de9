import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';

export type OverflowCountProps = {
	hasActive: boolean;
	overflowCount: number;
};
function OverflowCount({ hasActive, overflowCount }: OverflowCountProps): ReactElement<'span'> {
	return (
		<span className={classnames('overflow-count ml1', { 'is-active': hasActive })} data-test-class="overflow-count">
			+{overflowCount}
		</span>
	);
}

export default OverflowCount;
