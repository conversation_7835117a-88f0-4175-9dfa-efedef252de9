import type { CommitType } from '../../../../domain/commit/CommitTypes';
import { getSvgElement, getSvgStyles } from '../../../../domain/edge/EdgeHelpers';
import type { HashMap } from '../../../../domain/generic/GenericTypes';
import { COMMIT_ZONE_EDGE_ARC_PADDING, COMMIT_ZONE_EDGE_ARC_RADIUS } from '../../../../domain/graph/GraphConstants';
import type { ArcEndpointAngle, ArcSvgProps, ColumnColor, SvgElement } from '../../../../domain/graph/GraphTypes';

// NOTE this stuff has to be absolutely as fast as possible. V8 does string concatenation very quickly
// compared to other types of string building.

// These values are cached for speed
const COS = {
	'0': 1,
	'90': 0,
	'180': -1,
	'270': 0,
};

const SIN = {
	'0': 0,
	'90': 1,
	'180': 0,
	'270': -1,
};

const RADIUS_MINUS_PADDING = COMMIT_ZONE_EDGE_ARC_RADIUS - COMMIT_ZONE_EDGE_ARC_PADDING;
const ARC_BEGINNING_SUBSTRING = ` A ${RADIUS_MINUS_PADDING} ${RADIUS_MINUS_PADDING} 0 0 0 `;

type Cartesian = {
	x: number;
	xOffset: number;
	y: number;
	yOffset: number;
};

function polarToCartesian(centerX: number, centerY: number, angleInDegrees: ArcEndpointAngle): Cartesian {
	const cos = COS[angleInDegrees];
	const sin = SIN[angleInDegrees];

	return {
		x: centerX - COMMIT_ZONE_EDGE_ARC_RADIUS * cos,
		xOffset: -(cos * COMMIT_ZONE_EDGE_ARC_PADDING),
		y: centerY + COMMIT_ZONE_EDGE_ARC_RADIUS * sin,
		yOffset: sin * COMMIT_ZONE_EDGE_ARC_PADDING,
	};
}

const arcCache: HashMap = {};

function describeArc(x: number, y: number, startAngle: ArcEndpointAngle, endAngle: ArcEndpointAngle): string {
	const hash = `${x}_${y}_${startAngle}_${endAngle}`;

	let arc: string | undefined = arcCache[hash];
	if (arc) {
		return arc;
	}

	const start: Cartesian = polarToCartesian(x, y, endAngle);
	const end: Cartesian = polarToCartesian(x, y, startAngle);

	// NOTE since the start/end points are always aligned with the X or Y of the previous/next point defining a line
	// segment, we can use 'H'/'V' shorthand instead of 'L' and save two string concatentations each time.
	arc = `M ${start.x} ${start.y}${
		end.xOffset ? ` H ${start.x + end.xOffset}` : ` V ${start.y + end.yOffset}`
	}${ARC_BEGINNING_SUBSTRING}${end.x + start.xOffset} ${end.y + start.yOffset}${
		start.xOffset ? ` H ${end.x}` : ` V ${end.y}`
	}`;

	arcCache[hash] = arc;

	return arc;
}

function getArc(
	color: ColumnColor,
	endAngle: ArcEndpointAngle,
	startAngle: ArcEndpointAngle,
	type: CommitType,
	x: number,
	y: number,
	strokeWidth: number,
	isCompact?: boolean,
): SvgElement {
	const svgProps: ArcSvgProps = getSvgStyles(type, color, strokeWidth, isCompact) as ArcSvgProps;
	svgProps.d = describeArc(x, y, startAngle, endAngle);
	return getSvgElement('path', svgProps);
}

export default getArc;
