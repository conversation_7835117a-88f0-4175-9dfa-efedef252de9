export const commitNodeType = 'commit-node';
export const mergeConflictNodeType = 'merge-conflict-node';
export const mergeNodeType = 'merge-node';
export const unsupportedRebaseWarningNodeType = 'unsupported-rebase-warning-node';
export const stashNodeType = 'stash-node';
export const workDirType = 'work-dir-changes';

export enum CommitDateTimeSources {
	RowEntry = 'rowEntry',
	Tooltip = 'tooltip',
}
