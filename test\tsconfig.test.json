{"compilerOptions": {"baseUrl": ".", "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "incremental": true, "isolatedModules": true, "jsx": "react", "lib": ["es2020", "dom"], "module": "commonjs", "moduleResolution": "node", "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": false, "outDir": "dist/test", "resolveJsonModule": true, "rootDir": "test/specs", "skipLibCheck": true, "sourceMap": true, "strict": true, "strictNullChecks": false, "target": "es2020", "useDefineForClassFields": true, "useUnknownInCatchVariables": false}, "include": ["test/specs/**/*"]}