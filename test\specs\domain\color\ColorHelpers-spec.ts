import { expect } from 'chai';

import * as ColorHelpers from '../../../../src/domain/color/ColorHelpers';

describe('Color Helpers', function () {
	const mockDarkColor = '#480A69';
	const mockLightColor = '#EFFD0A';

	describe('getTextColor', function () {
		it('should return a white color for the text if the background color is dark.', function () {
			expect(ColorHelpers.getTextColor(mockDarkColor)).to.equal(ColorHelpers.COLOR_WHITE);
		});

		it('should return a black color for the text if the background color is light.', function () {
			expect(ColorHelpers.getTextColor(mockLightColor)).to.equal(ColorHelpers.COLOR_BLACK);
		});
	});
});
