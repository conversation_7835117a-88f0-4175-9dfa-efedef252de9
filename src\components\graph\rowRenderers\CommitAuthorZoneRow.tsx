import type { <PERSON>ridCellP<PERSON>, GridCell<PERSON>enderer } from 'react-virtualized';
import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import { commitNodeType, mergeNodeType, workDirType } from '../../../domain/commit/CommitConstants';
import { getCommitMessageCoauthors, getCommitMessageDescription } from '../../../domain/commit/CommitHelpers';
import type { Author } from '../../../domain/commit/CommitTypes';
import type { GraphZoneType } from '../../../domain/graph/GraphConstants';
import {
	COMMIT_AUTHOR_ZONE_AVATAR_INNER_DIAMETER,
	COMMIT_AUTHOR_ZONE_AVATAR_OUTER_DIAMETER,
	commitAuthorZone,
	GraphColumnMode,
} from '../../../domain/graph/GraphConstants';
import { GraphRowHelper } from '../../../domain/graph/GraphHelpers';
import type {
	CommonGraphRowDispatchProps,
	CommonGraphRowProps,
	GraphItemContext,
	ProcessedGraphRow,
} from '../../../domain/graph/GraphTypes';
import type { GetRealKeyForCellFunc } from '../../../domain/reactvirtualized/ReactVirtualizedHelpers';
import { getInitialsFromName, getNameWithEmail } from '../../../utils/TextFormatting';
import GraphAvatar from '../commitzone/GraphAvatar';
import GenericGraphZone from '../common/GenericGraphZone';

interface Props extends CommonGraphRowProps, CommonGraphRowDispatchProps {}

const makeCommitAuthorZoneRowRenderer = (props: Props, getKeyForCell: GetRealKeyForCellFunc): GridCellRenderer => {
	const {
		avatarUrlByEmail,
		clearCurrentlyHoveredGraphCommit,
		cssVariables,
		currentlyHoveredCommitSha,
		dimMergeCommits,
		dimRowsOfSelectedCommit,
		getExternalIcon,
		highlightRowsOnRefHover,
		processedRows,
		isInUnsupportedRebase,
		onCommitContextMenu,
		onClickCommit,
		onDoubleClickCommit,
		numGraphColumns,
		setAsCurrentlyHoveredGraphCommit,
		suppressNonRefRowTooltips,
		useAuthorInitialsForAvatars,
	} = props;

	const rowHelper: GraphRowHelper = new GraphRowHelper(props);

	const rowRenderer = ({ rowIndex: index, style }: GridCellProps): ReactElement<any> | undefined => {
		const key: string = getKeyForCell(index);

		const graphRow: ProcessedGraphRow = processedRows[index];
		const { author, email, message, sha, type, contexts } = graphRow;
		const zoneType: GraphZoneType = commitAuthorZone;
		const authorZone = rowHelper.getActiveGraphZone(zoneType);
		let contents: string | ReactElement<any> | undefined = type !== workDirType ? author : undefined;
		const tooltip: string | undefined = type !== workDirType ? getNameWithEmail(author, email) : undefined;
		const context: GraphItemContext | undefined = contexts?.author || undefined;
		const rowContext: GraphItemContext | undefined = contexts?.row || undefined;
		const showColorStrip = rowHelper.isColumnFollowingGraphColumn(zoneType);
		const isLastColumn = rowHelper.isLastColumn(zoneType);
		const zoneWidth = rowHelper.getZoneWidth(zoneType);
		const useAvatars =
			(zoneWidth <= authorZone.minimumWidth || authorZone?.mode === GraphColumnMode.Rich) && type !== workDirType;

		if (useAvatars) {
			const tooltipTextGetter = (): string => {
				const authorText: string =
					author && author.trim() !== '' && author !== 'Unknown' ? getNameWithEmail(author, email) : email;
				const coauthors: Author[] = getCommitMessageCoauthors(getCommitMessageDescription(message || ''));
				let tooltipText = '';

				if (coauthors.length === 0) {
					tooltipText = authorText;
				} else {
					coauthors.unshift({ email: email, name: author });
					for (let i = 0; i < coauthors.length; i += 1) {
						const coauthor: Author = coauthors[i];
						tooltipText += getNameWithEmail(coauthor.name, coauthor.email);
						if (i < coauthors.length - 1) {
							tooltipText += ', ';
						}
					}
				}

				return tooltipText;
			};
			const avatarUrl: string | undefined = useAuthorInitialsForAvatars ? undefined : avatarUrlByEmail[email];

			contents = (
				<GraphAvatar
					authorInitials={getInitialsFromName(author)}
					avatarUrl={avatarUrl}
					className={classnames('node', commitNodeType, 'z6')}
					column={graphRow.column}
					context={contexts?.avatar || undefined}
					cssVariables={cssVariables}
					fontSize={10}
					height={COMMIT_AUTHOR_ZONE_AVATAR_OUTER_DIAMETER}
					size={COMMIT_AUTHOR_ZONE_AVATAR_INNER_DIAMETER}
					tooltip={suppressNonRefRowTooltips ? undefined : tooltipTextGetter}
					useAuthorInitialsForAvatars={useAuthorInitialsForAvatars}
					width={COMMIT_AUTHOR_ZONE_AVATAR_OUTER_DIAMETER}
				/>
			);
		}

		return (
			<GenericGraphZone
				clearCurrentlyHoveredGraphCommit={clearCurrentlyHoveredGraphCommit}
				column={graphRow.column}
				columnForColoring={graphRow.columnForColoring}
				context={context}
				currentlyHoveredCommitSha={currentlyHoveredCommitSha}
				dimRowsOfSelectedCommit={dimRowsOfSelectedCommit}
				getExternalIcon={getExternalIcon}
				graphZoneType={zoneType}
				hasAvatars={useAvatars}
				highlightRowsOnRefHover={highlightRowsOnRefHover}
				isDimmedMergeCommitRow={type === mergeNodeType && dimMergeCommits}
				isHighlighted={rowHelper.isHighlighted(index)}
				isHovering={rowHelper.isHovering(index)}
				isInUnsupportedRebase={isInUnsupportedRebase}
				isLastColumn={isLastColumn}
				isMissingHoveredRefGroup={rowHelper.isMissingHoveredRefGroup(index)}
				isSelected={rowHelper.isSelected(index)}
				key={key}
				numGraphColumns={numGraphColumns}
				onClickCommit={onClickCommit}
				onContextMenu={onCommitContextMenu}
				onDoubleClickCommit={onDoubleClickCommit}
				rowContext={rowContext}
				setAsCurrentlyHoveredGraphCommit={setAsCurrentlyHoveredGraphCommit}
				sha={sha}
				showColorStrip={showColorStrip}
				showTimeline={rowHelper.hasTimeline(index)}
				style={style}
				title={suppressNonRefRowTooltips ? undefined : tooltip}
				type={type}
				verticalScrollWidth={rowHelper.getVerticalScrollWidth(zoneType)}
				zoneWidth={zoneWidth}
			>
				{contents}
			</GenericGraphZone>
		);
	};

	return rowRenderer;
};

export default makeCommitAuthorZoneRowRenderer;
