import type { <PERSON>rid<PERSON><PERSON><PERSON><PERSON><PERSON>, ScrollParams } from 'react-virtualized';
import type { ReactElement } from 'react';
import type { CommitDateTimeSources } from '../commit/CommitConstants';
import type { CommitType, Sha } from '../commit/CommitTypes';
import type { CssVariables } from '../generic/GenericTypes';
import type { HostingServiceType, IssueTrackerType } from '../hostingservice/HostingServiceTypes';
import type { TranslationFn } from '../language/LanguageTypes';
import type { GraphRefType, RefFullName, RefShorthand } from '../ref/RefTypes';
import type { OnFocus, OnResizeFromPropChangeParams, OnResizeParams } from '../ui/UiTypes';
import type {
	GraphMarkerType,
	GraphZone,
	GraphZoneType,
	TimelineEntry,
	TimelineMsgRowRenderIdType,
} from './GraphConstants';

export type RowRenderType = GraphZoneType | TimelineMsgRowRenderIdType;

export type RowRenderersByIds = { [id: string /* RowRenderType */]: GridCellRenderer };
export type ColumnColor = string;
export type ColumnColorByColumn = { [id: number]: ColumnColor };
export type AbsoluteNodeLeftByColumn = { [id: number]: number };
export type NodeOffsetByColumn = { [id: number]: number };
export type NodeOpacityByColumn = { [id: number]: number };
export type XValueByColumnCache = { [hash: string]: number };

export type ArcSvgElementName = 'path';
export type LineSvgElementName = 'line';
export type SvgElementName = ArcSvgElementName | LineSvgElementName;

export type SvgElement = string;
export type EdgeCache = { [hash: string]: SvgElement };

export type ArcEndpointAngle = 0 | 90 | 180 | 270;

export interface SvgStyles {
	fill: string;
	shapeRendering: string;
	strokeLinejoin: string;
	strokeWidth: number;
	strokeDasharray: number;
	stroke: ColumnColor;
}

export interface LineSvgProps extends SvgStyles {
	x1: number;
	x2: number;
	y1: number;
	y2: number;
}

export interface ArcSvgProps extends SvgStyles {
	d: string;
}

export type SvgProps = ArcSvgProps | LineSvgProps;
export type AnySvgProps = ArcSvgProps & LineSvgProps;

export type Edge = {
	parentSha: Sha;
	type: CommitType;
};

export type EdgeByColumn = {
	[column: number]: Edge;
};

export type RowEdge = {
	ending?: Edge;
	passThrough?: Edge;
	starting?: Edge;
};

export type RowEdges = {
	[column: number]: RowEdge;
};

export interface CommonGraphRowProps {
	alwaysShowTimelines?: boolean;
	enabledRefMetadataTypes: RefMetadataType[];
	enabledScrollMarkerTypes: GraphMarkerType[];
	avatarUrlByEmail: AvatarUrlByEmail;
	currentlyHoveredCommitSha?: Sha;
	cssVariables: CssVariables;
	dimMergeCommits: boolean;
	dimRowsOfSelectedCommit: boolean;
	hasMoreCommits: boolean;
	height: number;
	highlightRowsOnRefHover: boolean;
	isLoadingRows: boolean;
	selectedShas: Set<Sha>;
	showGhostRefsOnRowHover: boolean;
	showRemoteNamesOnRefs: boolean;
	getExternalIcon: GetExternalIcon;
	graphZones: GraphZone[];
	highlightedShas?: HighlightedShas;
	hoveredRefGroup?: GraphRefGroup;
	hoveredRefZoneSha?: Sha;
	isInUnsupportedRebase: boolean;
	numGraphColumns: number;
	processedRows: ProcessedGraphRow[];
	processedGraphRowBySha: ProcessedGraphRowBySha;
	suppressNonRefRowTooltips?: boolean;
	translate: TranslationFn;
	useAuthorInitialsForAvatars: boolean;
	scrollTop: number;
}

export interface CommonGraphRowDispatchProps {
	onClickCommit: OnClickCommit;
	onDoubleClickCommit: OnDoubleClickCommit;
	onCommitContextMenu: OnCommitContextMenu;
	clearCurrentlyHoveredGraphCommit: OnClearCurrentlyHoveredGraphCommit;
	setAsCurrentlyHoveredGraphCommit: OnCurrentlyHoveredGraphCommit;
}

export type ReserverInfo = {
	type: CommitType;
	newestDate: number;
	column: number;
};

export type ExcludeByType = {
	heads?: boolean;
	remotes?: boolean;
	stashes?: boolean;
	tags?: boolean;
};
export type ExcludeRefsById = { [refId: string]: GraphRefOptData };
export type ExcludeRemotesByName = { [remoteName: string]: boolean };
export type IncludeOnlyRefsById = { [refId: string]: GraphRefOptData };
export type IncludeOnlyRemotesByName = { [remoteName: string]: boolean };
export type IsSelectedBySha = { [id: Sha]: boolean };
export type HighlightedShas = { [sha: Sha]: boolean };
export type IsExcludedBySha = { [id: Sha]: boolean };
export type HasSpecialChildBySha = { [id: Sha]: boolean };
export type ReserverInfoBySha = { [id: Sha]: ReserverInfo };

export type RefTargetFunc = () => Element | null | undefined;
export type ColumnContentGenerator = () => ReactElement<any>;

export type GraphItemContext = string | object;
export type RefGroupContexts = { [name: string]: GraphItemContext };

export type RowContexts = {
	row?: GraphItemContext;
	ref?: GraphItemContext;
	refGroups?: RefGroupContexts;
	graph?: GraphItemContext;
	avatar?: GraphItemContext;
	message?: GraphItemContext;
	author?: GraphItemContext;
	date?: GraphItemContext;
	sha?: GraphItemContext;
	stats?: GraphItemContext;
};

export type GraphContexts = {
	graph?: GraphItemContext;
	header?: GraphItemContext;
	settings?: GraphItemContext;
};

export interface Ref {
	id?: string;
	name: string;
	context?: GraphItemContext;
	contextGroup?: GraphItemContext;
}
export interface Head extends Ref {
	isCurrentHead?: boolean;
	upstream?: {
		name: string;
		id: string;
	};
	worktreeId?: string;
}
export interface Remote extends Ref {
	owner?: string;
	avatarUrl?: string;
	url?: string;
	current?: boolean;
	hostingServiceType?: HostingServiceType;
}
export interface Tag extends Ref {
	annotated?: boolean;
	message?: string; // undefined, except on annotated tags
}
export interface GraphRef extends Head, Remote, Tag {
	refType: GraphRefType;
	fullName?: RefFullName;
}

export type RefMetadataById = {
	[id: string]: RefMetadata | null;
} | null;

export interface RefMetadata {
	pullRequest?: PullRequestMetadata[] | null;
	upstream?: UpstreamMetadata | null;
	issue?: IssueMetadata[] | null;
}

export type RefsMissingMetadata = {
	[refId: string]: RefMetadataType[];
};
export type RefMetadataRequestedById = { [refId: string]: boolean };

export type RefMetadataType = keyof RefMetadata;
export const pullRequestMetadataType = 'pullRequest' satisfies RefMetadataType;
export const upstreamMetadataType = 'upstream' satisfies RefMetadataType;
export const issueMetadataType = 'issue' satisfies RefMetadataType;

// Expand these as we add more metadata types above.
export const allMetadataTypes: RefMetadataType[] = [pullRequestMetadataType, upstreamMetadataType, issueMetadataType];
export type RefMetadataItem =
	| {
			refId: string;
			type: typeof pullRequestMetadataType;
			data: PullRequestMetadata;
	  }
	| {
			refId: string;
			type: typeof upstreamMetadataType;
			data: UpstreamMetadata;
	  }
	| {
			refId: string;
			type: typeof issueMetadataType;
			data: IssueMetadata;
	  };

export interface BaseMetadata {
	context?: GraphItemContext;
}

export interface PullRequestMetadata extends BaseMetadata {
	hostingServiceType: HostingServiceType;
	id: number;
	title: string;
	author?: string;
	date?: number;
	state?: string;
	url?: string;
}

export interface UpstreamMetadata extends BaseMetadata {
	name: string;
	owner: string;
	ahead: number;
	behind: number;
	sha?: Sha;
}

export interface IssueMetadata extends BaseMetadata {
	displayId: string;
	id: string;
	issueTrackerType: IssueTrackerType;
	title: string;
}

export interface ChildRefsByType {
	heads: Head[];
	remotes: Remote[];
	tags: Tag[];
}

export interface ChildRefData extends ChildRefsByType {
	sha: Sha;
	parentIndexScore: number;
	rowEntered: number;
}

export type ChildRefCollection = ChildRefData[];

export type GraphRefOptData = {
	id: string;
	name: string;
	type: GraphRefType;
	owner?: string;
	avatarUrl?: string;
};

export type GraphRefGroup = Array<GraphRef>;
export type GraphRefGroupsByName = { [fullName: RefFullName]: GraphRefGroup };

export type HeadsBySha = { [id: Sha]: Head[] };
export type RemotesBySha = { [id: Sha]: Remote[] };
export type TagsBySha = { [id: Sha]: Tag[] };

export interface RowStats {
	files: number;
	additions: number;
	deletions: number;
}

export interface RowStatsConstraints {
	min: number;
	max: number;
}

export interface GraphRow {
	sha: Sha;
	parents: Sha[];
	author: string;
	email: string;
	date: number;
	message: string;
	type: CommitType; // TODO: try to remove this property (added for now to continue working)
	heads?: Head[];
	remotes?: Remote[];
	tags?: Tag[];
	contexts?: RowContexts; // Optional. Provides context attributes to DOM for vscode context menus.
}

export interface GraphRefsData {
	activeGraphRef?: GraphRef;
	activeGraphRefGroup?: GraphRefGroup;
	orderedRefGroups: GraphRefGroup[];
	refGroupsByName: GraphRefGroupsByName;
}

export interface ProcessedGraphRow extends GraphRow {
	rowIndex?: number;
	column: number;
	columnForColoring?: number;
	displayMessage: string;
	displaySummary: string;
	displayBody?: string;
	edges: RowEdges;
	edgeColumnMaxes: number;
	hasRefs?: boolean;
	summary: string;
	body?: string;
	timeLineEntry?: TimelineEntry;
	childRefs?: ChildRefsByType;
	refsData?: GraphRefsData;
}

export type TopAndBottomVisibleRowIndex = { top: number; bottom: number };
export type GraphRowBySha = { [sha: Sha]: GraphRow };
export type ProcessedGraphRowBySha = { [sha: Sha]: ProcessedGraphRow };
export type ShaByRefId = { [refId: string]: Sha };
export type ChildrenBySha = { [sha: Sha]: Sha[] };
export type GraphZonesByType = { [zoneType: string /* GraphZoneType */]: GraphZone };

export type GraphColumnSetting = {
	width: number;
	isFilterable?: boolean;
	isFilterActive?: boolean;
	isHidden: boolean;
	mode?: string /* GraphColumnMode */;
	order?: number;
};

export type GraphColumnsSettings = { [graphZoneType: string /* GraphZoneType */]: GraphColumnSetting };
export type GraphZoneModeConstants = {
	COMMIT_ZONE_LINE_WIDTH: number;
	COMMIT_ZONE_GUTTER_WIDTH: number;
	COMMIT_ZONE_PADDING_LEFT: number;
	COMMIT_ZONE_PADDING_RIGHT: number;
	COMMIT_COLUMN_WIDTH: number;
	COMMIT_NODE_DIAMETER: number;
	COMMIT_MERGE_NODE_DIAMETER: number;
	COMMIT_NODE_TOP_OFFSET: number;
	COMMIT_MERGE_NODE_LEFT_OFFSET: number;
	COMMIT_MERGE_NODE_TOP_OFFSET: number;
	COMMIT_ZONE_AVATAR_DIAMETER: number;
	COMMIT_ZONE_VIEWPORT_WIDTH_MIN: number;
	COMMIT_ZONE_SHOW_ICON_WIDTH: number;
	IS_COMPACT: boolean;
	RADIUS_DIFF_MERGE_NODE_COMMIT_NODE: number;
};

export type GraphColumnWidthConstraints = {
	min: number;
	max: number;
};

export type GraphMarkerColors = {
	[marker: string /* GraphMarkerType */]: string;
};

export type GraphMarkerRowIndices = {
	[marker: string /* GraphMarkerType */]: number[];
};

export type AvatarUrlByEmail = { [email: string]: string };
export type AvatarUrlRequestedByEmail = { [email: string]: boolean };

export type DownstreamsByUpstream = { [upstreamName: string]: string[] };

// Copied from process.platform of NodeJs
export type GraphPlatform =
	| 'aix'
	| 'android'
	| 'darwin'
	| 'freebsd'
	| 'linux'
	| 'openbsd'
	| 'sunos'
	| 'win32'
	| 'cygwin';

export type ExternalIconKeys =
	| 'added'
	| 'author'
	| 'branch'
	| 'changes'
	| 'check'
	| 'commit'
	| 'datetime'
	| 'deleted'
	| 'files'
	| 'filter'
	| 'graph'
	| 'head'
	| 'hide'
	| `issue-${IssueTrackerType}`
	| 'loading'
	| 'message'
	| 'modified'
	| 'pull-request'
	| 'remote'
	| `remote-${HostingServiceType}`
	| 'renamed'
	| 'resolved'
	| 'settings'
	| 'show'
	| 'stash'
	| 'tag'
	| 'upstream-ahead'
	| 'upstream-behind'
	| 'warning'
	| 'worktree';

export type GetExternalIcon = (key: ExternalIconKeys) => ReactElement<any>;
export type GetMissingAvatar = (email: string, sha: string) => void;
export type GetMissingRefMetadata = (id: string, types: RefMetadataType[]) => void;
export type FormatRefShorthand = (
	shorthand: RefShorthand,
	sha: Sha,
	refType: GraphRefType,
	data?: any | null,
) => RefShorthand;
export type IsRefShorthandValid = (
	shorthand: RefShorthand,
	sha: Sha,
	refType: GraphRefType,
	data?: any | null,
) => boolean;

// Custom events
export type OnHideRefClick = (event: React.MouseEvent<any>, refGroup: GraphRefGroup, sha: Sha) => void;
export type OnDoubleClickRef = (
	event: React.MouseEvent<any>,
	refGroup: GraphRefGroup,
	sha: Sha,
	metadataItem?: RefMetadataItem,
) => void;
export type OnDoubleClickRefMetadata = (event: React.MouseEvent<any>, metadataItem: RefMetadataItem) => void;
export type OnRefZoneContextMenu = (event: React.MouseEvent<any>, refGroup: GraphRefGroup, sha: Sha) => void;
export type OnScrollForZone = (
	zoneType: GraphZoneType,
	scroll: ScrollParams,
	graphWidth: number,
	graphHeight: number,
	hasMoreCommits: boolean,
) => void;

export type RefDndData = {
	commitType: CommitType;
	isGhostRef: boolean;
	isInUnsupportedRebase: boolean;
	refGroup: GraphRefGroup;
	sha: Sha;
};

export type OnLoadSelectedGraphRow = (graphRow: GraphRow) => void;

export type OnRefBeginDrag = (event: React.DragEvent, sourceRefData?: RefDndData) => void;
export type OnRefCanDrag = (sourceRefData?: RefDndData) => boolean;
export type OnRefCanDrop = (event?: React.DragEvent, sourceRefData?: RefDndData, targetRefData?: RefDndData) => boolean;
export type OnRefDragEnter = (event: React.DragEvent, sourceRefData?: RefDndData, targetRefData?: RefDndData) => void;
export type OnRefDragLeave = (event: React.DragEvent, sourceRefData?: RefDndData, targetRefData?: RefDndData) => void;
export type OnRefEndDrag = (event: React.DragEvent, sourceRefData?: RefDndData, targetRefData?: RefDndData) => void;
export type OnRefDrop = (event: React.DragEvent, sourceRefData?: RefDndData, targetRefData?: RefDndData) => void;

export type OnCommitMessageChange = (message: string) => void;
export type OnGraphResized = (width: number, height: number) => void;

export type OnGraphVisibleRowsChanged = (topVisibleRow: GraphRow, bottomVisibleRow: GraphRow) => void;

export type OnGraphMouseEnter = (event: React.MouseEvent<any>) => void;
export type OnGraphMouseLeave = (event: React.MouseEvent<any>) => void;

export type OnClearCurrentlyHoveredGraphCommit = (
	event: React.MouseEvent<any>,
	graphZoneType: GraphZoneType,
	sha: Sha,
	currentlyHoveredCommitSha?: Sha,
) => void;

export type CommitDateTimeSource = CommitDateTimeSources;
export type OnFormatCommitDateTime = (commitDateTime: number, source?: CommitDateTimeSource) => string;
export type FormatCommitMessage = (commitMessage: string) => string;

export type OnCurrentlyHoveredGraphCommit = (
	event: React.MouseEvent<any>,
	graphZoneType: GraphZoneType,
	sha: Sha,
	currentlyHoveredCommitSha?: Sha,
) => void;

export type OnClickCommit = (event: React.MouseEvent<any>, graphZoneType: GraphZoneType, sha: Sha) => void;

export type OnClickRef = (
	event: React.MouseEvent<any>,
	refGroup: GraphRefGroup,
	sha: Sha,
	metadataItem?: RefMetadataItem,
) => void;

export type OnClickRefMetadata = (event: React.MouseEvent<any>, metadataItem: RefMetadataItem) => void;

export type OnDoubleClickCommit = (event: React.MouseEvent<any>, zoneType: GraphZoneType, sha: Sha) => void;

export type OnRefNodeHovered = (event: React.MouseEvent<any>, refGroup: GraphRefGroup, sha: Sha) => void;
export type OnRefNodeUnhovered = (
	event: React.MouseEvent<any> | React.FocusEvent<any> | null,
	refGroup: GraphRefGroup,
	sha: Sha,
) => void;

export type OnRefZoneHovered = (sha: Sha) => void;

export type OnRefZoneUnhovered = () => void;

export type OnToggleRefNodesShown = (
	event: React.MouseEvent<any>,
	refs: GraphRefOptData[],
	refsVisibles: boolean,
	sha?: Sha,
) => void;

export type OnCommitContextMenu = (event: React.MouseEvent<any>, graphZoneType: GraphZoneType, sha: Sha) => void;

export type OnGraphColumnReOrdered = (sourceGraphZoneType: GraphZoneType, targetGraphZoneType?: GraphZoneType) => void;

export type OnGraphZoneResize = (graphZone: GraphZone, dimensions: OnResizeParams) => void;

export type OnGraphZoneResizeEnd = (graphZone: GraphZone, dimensions: OnResizeParams) => void;

export type OnGraphZoneResizeStart = (graphZone: GraphZone, dimensions: OnResizeParams) => void;

export type OnGraphZoneResizeFromPropChange = (graphZone: GraphZone, dimensions: OnResizeFromPropChangeParams) => void;

export type OnPopupGraphHeaderContextMenu = (event: React.MouseEvent<any>, width: number) => void;

export type OnSettingsClick = (event: React.MouseEvent<any>, width: number) => void;

export type OnWipMessageBlur = (event?: React.FocusEvent<HTMLInputElement>) => void;
export type OnWipMessageFocus = OnFocus;

export type OnFilterColumnClick = (event: React.MouseEvent<any>, graphZoneType: GraphZoneType) => void;

export type OnRefCreate = (shorthand: RefShorthand, sha: Sha, refType: GraphRefType, data?: any | null) => void;

export type OnRefCreateCancel = (shorthand: RefShorthand, sha: Sha, refType: GraphRefType, data?: any | null) => void;

export type OnRefCreateContextMenu = (
	event: React.MouseEvent<any>,
	shorthand: RefShorthand,
	sha: Sha,
	refType: GraphRefType,
	data?: any | null,
) => void;

export type OnRefShorthandChange = (shorthand: RefShorthand) => void;

export type OnClickGraphRow = (event: React.MouseEvent<any>, graphZoneType: GraphZoneType, graphRow: GraphRow) => void;
export type OnDoubleClickGraphRow = (
	event: React.MouseEvent<any>,
	graphZoneType: GraphZoneType,
	graphRow: GraphRow,
) => void;

export type OnClickGraphRef = (
	event: React.MouseEvent<any>,
	refGroup: GraphRefGroup,
	graphRow: GraphRow,
	metadataItem?: RefMetadataItem,
) => void;

export type OnDoubleClickGraphRef = (
	event: React.MouseEvent<any>,
	refGroup: GraphRefGroup,
	graphRow: GraphRow,
	metadataItem?: RefMetadataItem,
) => void;

export type OnRowContextMenu = (event: React.MouseEvent<any>, graphZoneType: GraphZoneType, graphRow: GraphRow) => void;
export type OnRefContextMenu = (event: React.MouseEvent<any>, refGroup: GraphRefGroup, graphRow: GraphRow) => void;

export type OnToggleRefsVisibilityClick = (
	event: React.MouseEvent<any>,
	refs: GraphRefOptData[],
	visible: boolean,
	graphRow?: GraphRow,
) => void;

export type OnSelectGraphRows = (selectedRows: GraphRow[]) => void;
export type OnGraphRowHovered = (
	event: React.MouseEvent<any>,
	graphZoneType: GraphZoneType,
	graphRow: GraphRow,
) => void;
export type OnGraphRowUnhovered = (
	event: React.MouseEvent<any>,
	graphZoneType: GraphZoneType,
	graphRow: GraphRow,
) => void;
export type OnGraphRefNodeHovered = (event: React.MouseEvent<any>, refGroup: GraphRefGroup, graphRow: GraphRow) => void;
export type OnGraphScrollForZone = (graphZoneType: GraphZoneType, scroll: ScrollParams) => void;

export type OnGraphRefNodeUnhovered = (
	event: React.MouseEvent<any> | React.FocusEvent<any> | null,
	refGroup: GraphRefGroup,
	graphRow: GraphRow,
) => void;

export type OnGraphColumnsReOrdered = (columnsSettings: GraphColumnsSettings) => void;
export type OnGraphColumnResized = (graphZoneType: GraphZoneType, columnSettings: GraphColumnSetting) => void;
export type OnShowMoreCommits = () => void;
export type OnEmailsMissingAvatarUrls = (emails: AvatarUrlByEmail) => void;
export type OnRefsMissingMetadata = (refs: RefsMissingMetadata) => void;
