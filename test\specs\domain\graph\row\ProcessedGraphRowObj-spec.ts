import chai, { expect } from 'chai';
import spies from 'chai-spies';
import { ProcessedGraphRowObj } from '../../../../../src/domain/graph/row/ProcessedGraphRowObj';
import { GraphRef, GraphRefsData, GraphRow } from '../../../../../src/domain/graph/GraphTypes';
import { commitNodeType } from '../../../../../src/domain/commit/CommitConstants';
import { refTypes } from '../../../../../src/domain/ref/RefConstants';
import { IGraphContainer } from '../../../../../src/components/graph/IGraphContainer';

chai.use(spies);

describe('ProcessedGraphRowObj', function () {
	const mockGraphContainer: IGraphContainer = {
		formatCommitMessageCallback: commitMessage => commitMessage,
		translateCallback: key => key,
	};

	const mockGraphHeadRef: GraphRef = {
		id: 'test-head-id',
		name: 'test-head',
		isCurrentHead: true,
		refType: refTypes.HEAD,
	};

	const mockGraphNoCurrentHeadRef: GraphRef = {
		id: 'test-no-current-head-id',
		name: 'test-no-current-head',
		isCurrentHead: false,
		refType: refTypes.HEAD,
	};

	const mockBaseRemoteRef: GraphRef = {
		id: 'test-remote-id',
		name: 'test-remote',
		owner: 'origin',
		avatarUrl: 'fakeurl.fake/avatar',
		refType: refTypes.REMOTE,
	};

	const mockBaseRemoteRefWithoutOwner: GraphRef = {
		id: 'test-remote-without-owner-id',
		name: 'test-remote-without-owner',
		avatarUrl: 'fakeurl.fake/avatar',
		refType: refTypes.REMOTE,
	};

	const mockGraphRow: GraphRow = {
		sha: 'a',
		parents: ['b'],
		author: 'Test Author',
		email: '<EMAIL>',
		date: 99,
		message: 'Test commit 1',
		type: commitNodeType,
		heads: [mockGraphHeadRef],
		remotes: [],
		tags: [],
	};

	const mockGraphRowWithMultiLineMessage = {
		...mockGraphRow,
		message: 'line 1\nline 2\nline 3\n',
	};

	const mockGraphRowWithoutRefs: GraphRow = {
		sha: 'a',
		parents: ['b'],
		author: 'Test Author',
		email: '<EMAIL>',
		date: 99,
		message: 'Test commit 1',
		type: commitNodeType,
		heads: [],
		remotes: [],
		tags: [],
	};

	const mockGraphRowRefWithOwner: GraphRow = {
		sha: 'a',
		parents: ['b'],
		author: 'Test Author',
		email: '<EMAIL>',
		date: 99,
		message: 'Test commit 1',
		type: commitNodeType,
		heads: [],
		remotes: [mockBaseRemoteRef],
		tags: [],
	};

	const mockGraphRowRefWithoutOwner: GraphRow = {
		sha: 'a',
		parents: ['b'],
		author: 'Test Author',
		email: '<EMAIL>',
		date: 99,
		message: 'Test commit 1',
		type: commitNodeType,
		heads: [],
		remotes: [mockBaseRemoteRefWithoutOwner],
		tags: [],
	};

	const mockGraphRowRefWithUnorderedHeads: GraphRow = {
		sha: 'a',
		parents: ['b'],
		author: 'Test Author',
		email: '<EMAIL>',
		date: 99,
		message: 'Test commit 1',
		type: commitNodeType,
		heads: [mockGraphNoCurrentHeadRef, mockGraphHeadRef],
		remotes: [mockBaseRemoteRef],
		tags: [],
	};

	const mockGraphRowRefWithOrderedHeads: GraphRow = {
		...mockGraphRowRefWithUnorderedHeads,
		heads: [mockGraphHeadRef, mockGraphNoCurrentHeadRef],
		remotes: [mockBaseRemoteRef],
		tags: [],
	};

	const mockProcessedRowWithRefWithOwner = {
		...mockBaseRemoteRef,
		fullName: `${mockBaseRemoteRef.owner}/${mockBaseRemoteRef.name}`,
	};

	const mockProcessedRowWithRefWithoutOwner = {
		...mockBaseRemoteRefWithoutOwner,
		fullName: mockBaseRemoteRefWithoutOwner.name,
	};

	const testCases = [
		{
			desc: 'Check "summary" property of a commit message without multiple lines',
			row: mockGraphRow,
			propKey: 'summary',
			expectedResult: 'Test commit 1',
		},
		{
			desc: 'Check "body" property of a commit message without multiple lines',
			row: mockGraphRow,
			propKey: 'body',
			expectedResult: undefined,
		},
		{
			desc: 'Check "summary" property of a commit message with multiple lines',
			row: mockGraphRowWithMultiLineMessage,
			propKey: 'summary',
			expectedResult: 'line 1',
		},
		{
			desc: 'Check "body" property of a commit message with multiple lines',
			row: mockGraphRowWithMultiLineMessage,
			propKey: 'body',
			expectedResult: 'line 2 | line 3',
		},
		{
			desc: 'hasRefs should return true if refs are defined',
			row: mockGraphRow,
			propKey: 'hasRefs',
			expectedResult: true,
		},
		{
			desc: 'hasRefs should return false if refs are not defined',
			row: mockGraphRowWithoutRefs,
			propKey: 'hasRefs',
			expectedResult: false,
		},
	];

	testCases.forEach(testCase => {
		it(testCase.desc, function () {
			const mockProcessedGraphRowObj = new ProcessedGraphRowObj(
				mockGraphContainer, // GraphContainer
				{ ...testCase.row }, // row
				0, // column
				1, // edgeColumnMaxes
				{}, // edges
			);
			const keyTyped = testCase.propKey as keyof ProcessedGraphRowObj;
			expect(mockProcessedGraphRowObj[keyTyped]).to.equal(testCase.expectedResult);
		});
	});

	describe('refsData', function () {
		const refsDataTestCases = [
			{
				desc: 'FullName of a ref with owner should include the owner in the path',
				row: mockGraphRowRefWithOwner,
				propKey: 'orderedRefGroups',
				expectedResult: [[mockProcessedRowWithRefWithOwner]],
			},
			{
				desc: 'FullName of a ref without owner should not include the owner in the path',
				row: mockGraphRowRefWithoutOwner,
				propKey: 'orderedRefGroups',
				expectedResult: [[mockProcessedRowWithRefWithoutOwner]],
			},
			{
				desc: 'Unordered refs should be ordered by current head',
				row: mockGraphRowRefWithUnorderedHeads,
				propKey: 'orderedRefGroups',
				expectedResult: [[mockGraphHeadRef], [mockGraphNoCurrentHeadRef], [mockProcessedRowWithRefWithOwner]],
			},
			{
				desc: 'Refs must be ordered by the current head even if they were initially ordered',
				row: mockGraphRowRefWithOrderedHeads,
				propKey: 'orderedRefGroups',
				expectedResult: [[mockGraphHeadRef], [mockGraphNoCurrentHeadRef], [mockProcessedRowWithRefWithOwner]],
			},
		];

		refsDataTestCases.forEach(testCase => {
			it(testCase.desc, function () {
				const mockProcessedGraphRowObj = new ProcessedGraphRowObj(
					mockGraphContainer, // GraphContainer
					{ ...testCase.row }, // row
					0, // column
					1, // edgeColumnMaxes
					{}, // edges
				);
				const keyTyped = testCase.propKey as keyof GraphRefsData;
				expect(mockProcessedGraphRowObj.refsData?.[keyTyped]).to.deep.equal(testCase.expectedResult);
			});
		});
	});
});
