# Contributing

## Requirements

- Latest node@8, latest yarn.

## Making Changes

You will most likely be making changes to gitkraken-components within another project like GitKraken or GitLens.

To work on both at the same time:

- Go to the folder containing your "GitKrakenComponents" clone
- Run `yarn install` to install dependencies
- Run `yarn build` to compile and build
- Run `yarn link`
- Go to the folder containing your "GitKraken" or "GitLens" clone
- run `yarn link @gitkraken/gitkraken-components`

Your larger project will now use your local "GitKrakenComponents" clone. Any changes
you make in your local "GitKrakenComponents" will be reflected.
(just run `yarn build` in "GitKrakenComponents")

If you need to unlink:

- Go to the folder containing your "GitKraken" or "GitLens" clone
- Run `yarn unlink @gitkraken/gitkraken-components`

To avoid warnings in VS Code when importing components (OPTIONAL):

- As this library is not already available on Github as a private library of gitkraken, you will need to add the dependency as following in your "package.json":
  `"@gitkraken/gitkraken-components": "file:./node_modules/@gitkraken/gitkraken-components"`
- Then you will be able to imports a component in your code like for example:
  `import GraphContainer from '@gitkraken/gitkraken-components/lib/components/graph/GraphContainer';`

**To build in development mode**

- This will generate the source maps to be able to debug our library

```
yarn build:dev
```

**To run tests**

```
yarn test
```

**To publish the library to NPM**

Prerequisites

- The npm auth token is required and must be in your .npmrc for `@gitkraken`.

Follow below steps:

Using GKC:

- Bump the library version in our `package.json` file (for example change `"version": "1.0.0-rc.14"` to `"version": "1.0.0-rc.15"`).
- Commit the change and create a local tag (for example: `1.0.0-rc.15`).
- Push tag to origin.

Using command line:

- To bump locally the `package.json` version using the preid we have now, you just need to run:
  - `yarn version --prerelease --preid=rc`
- To push the tag:
  - `git push --follow-tags` (running this command is important after versioning to ensure both the commits and tags are uploaded. Otherwise, the CI/automation won't publish the new version).

## Using the Graph Library

The main entrypoint into this library is the `GraphContainer` React component. This component is the high-level container in charge of displaying the graph and can be accessed directly from the library.
For example, in your application, you can inject it into React code like this:

```
<GraphContainer
    graphRows={mockGraphRows}
    useAuthorInitialsForAvatars={false}
/>
```

Data is passed into the container using the graphRows prop, and should contain an array of GraphRow objects with the following properties:

- sha: The ID or SHA of the row, as a string.
- parents: An array of strings containing the IDs or SHAs of the parents of this row, used to form the git commit tree (when applicable). Typical commits have their preceding commit as their parent, while merge
  commits have the merge points as their parents, and the initial commit has no parent. See here for more info on parent SHAS in git terminology: https://git-scm.com/docs/git-commit-tree
- author: The full name of the author of the row, as a string.
- email: The email address of the author of the row, as a string.
- date: The date of the row, as a timestamp in milliseconds.
- message: The commit message, as a string (when applicable).
- type: The type of the node in the graph.
  - Current supported values are:
    - "commit-node": A regular commit.
    - "merge-node": A merge commit.
  - Future supported types (in progress) are:
    - "stash-node": A stash point.
    - "merge-conflict-node": A merge conflict point.
    - "unsupported-rebase-warning-node": A warning that a rebase is not supported.
    - "work-dir-changes": Indicates uncommited work-in-progress (WIP) changes.
- heads: array of Head references for the current row, when applicable.
  Each Head reference has the following properties:
  - id: Optional id. If it is not provided, the component will generate its own id.
  - name: The name of the head, as a string. Required.
  - isCurrentHead: Boolean which denotes that this is the current head. Optional, and defaults to false.
- remotes: array for Remote references for the current row, when applicable.
  Each Remote reference has the following properties:
  - id: Optional id. If it is not provided, the component will generate its own id.
  - name: The name of the remote, as a string. Required.
  - url: The URL of the remote, as a string. Optional.
  - current: Optional boolean. Indicates whether this reference is the current upstream or not.
  - avatarUrl: The URL of the remote's avatar, as a string. Optional.
  - hostingServiceType: optional. Hosting service type. Will be used to display the remote icon.
- tags: array of Tag references for the current row, when applicable.
  Each Tag reference has the following properties:
  - id: Optional id. If it is not provided, the component will generate its own id.
  - name: The name of the tag, as a string. Required.
  - annotated: Boolean which denotes that this is an annotated tag. Optional, and defaults to false.

Here is an example of a formatted graph row:

```
  {
    sha: '1211222233334444',
    parents: [],
    author: 'Ramin Tadayon',
    email: '<EMAIL>',
    date: 1654892260000,
    message: 'Initial commit',
    type: 'commit-node',
    heads: [
      {
        name: 'master',
        isCurrentHead: true
      }
    ]
  }
```

To ensure the graph topology matches what you expect, ensure that the parents of each row are defined correctly.

Other settings for customization are provided as props and include:

- avatarUrlByEmail: Optionally pass a mapping object with author emails as keys and avatar url as their values to display avatar images for those authors.
- createRefFormData: Optional object. If we set this property, the ref input box will be displayed to enter a new name and proceed to create it. This property must be combined with following properties: "formatRefFullName", "isRefFullNameValid", "onRefCreate", "onRefCreateCancel" and "OnRefCreateContextMenu". Definition is as following:

```
{
	sha: Sha;
	shorthand: RefShorthand;
	type: GraphRefType;
	data?: any | null; // This property adds the possiblity to include extra data. This data may be necessary to carry out checks from the other callbacks.
}
```

- formatRefShorthand: Callback to format refs shorthand names. This property is optional and definition is as following: (shorthand: RefShorthand, sha: Sha, refType: GraphRefType, data?: any | null) => RefShorthand.
- enableShowHideRefsOptions: boolean. Optional. Default value is false. This option is to enable show and hide Branches/tags options.
- refMetadataById: Optionally pass a mapping object with ref ids as keys and ref metadata as their values to display ref metadata for those refs. Metadata that can be provided includes:
  - `pullRequest`: An array of pull request objects with the following properties:
    - hostingServiceType: The hosting service type of the pull request. Currently supported values are:
      - "github"
      - "githubEnterprise"
      - "gitlab"
      - "gitlabSelfHosted"
      - "azureDevops"
      - "bitbucket"
      - "bitbucketServer"
    - id: The ID of the pull request, as a number. Required.
    - title: The title of the pull request, as a string. Required.
    - author: The author of the pull request, as a string. Optional.
    - date: The date of the pull request, as a number (UNIX timestamp). Optional.
    - state: The state of the pull request, as a string. Optional.
    - context: VSCode context for the pull request as a stringified or plain JSON object. Optional.
  - `upstream`: An object with the following properties:
    - name: The name of the upstream, as a string. Required.
    - owner: The owner of the upstream, as a string. Required.
    - ahead: The number of commits ahead of the upstream, as a number. Required.
    - behind: The number of commits behind the upstream, as a number. Required.
    - sha: The SHA of the upstream, as a string. Optional.
  - `issue`: An object with the following properties:
    - displayId: the display id of the issue. Required.
    - id: the id of the issue. Required.
    - issueTrackerType: Required. The issue tracker type of the issue. Currently supported values are:
      - "github"
      - "githubEnterprise"
      - "gitlab"
      - "gitlabSelfHosted"
      - "jiraCloud"
      - "jiraServer"
      - "trello"
      - "azureDevops"
    - title: the title of the issue.
- contexts: Optionally pass VS Code contexts as serialized or plain JSON object strings to attach to the graph at various levels of the DOM. Current options are "graph" and "header".
- enableMultiSelection: A boolean to enable or not the row multi-selection. This property is optional and default value is false. Take into account that this property works with the "platform" property.
- shiftSelectMode: A string to determine which mode is used when using shift-selection, This property is optional. Currently support values are: 'simple'.
- searchMode: A string to determine how commit rows are affected when searching/highlighting. This property is optional. Currently support values are: 'normal' and 'filter'.
- suppressNonRefRowTooltips: A boolean to enable or not the row tooltips of the graph, with the exception of the branch/tag tooltips (which cannot be disabled). This property is optional and default value is false.
- excludeByType: Optional property to show/hide a node by type. Type definition is as following: { heads?: boolean, remotes?: boolean, stashes?: boolean, tags?: boolean }
- excludeRefsById: Optional property to show/hide a ref node by ref.id. Type definition is as following: {[refId: string]: GraphRefOptData}
- includeOnlyRefsById: Optional property to include a ref node by ref.id. Type definition is as following: {[refId: string]: GraphRefOptData}. If at least one ref is included in this property, then all refs will be hidden by default.
- isRefShorthandValid: Callback to valid a ref shorthand. This property is optional. Type definition is as following: (shorthand: RefShorthand, sha: Sha, refType: GraphRefType, data?: any | null) => boolean.
- platform: String to specify the current platform: Valid platforms are: . This property is optional and default value is "darwin". This property is used to know which keyboard key will be used for row multi-selection. For example, for rows multi-selection in Windows we use Control key + mouse left click and in Mac we use Option key + mouse left click.
- formatCommitDateTime: callback to format date times on the graph component. This property is optional and definition is as following: (commitDateTime: number, source?: CommitDateTimeSource) => string.
- formatCommitMessage: callback to format commit message before displaying it on the graph component. This property is optional and definition is as following: (commitMessage: string) => string.
- getExternalIcon: callback to retrieve an icon based on a key string. This property is optional and definition is as following: (iconKey: string) => React.ReactElement.
- useAuthorInitialsForAvatars: A boolean indicating whether to use the author's initials for avatars. Defaults to true. If set to `false`, avatar url or Gravatar (https://gravatar.com/) will be used to generate author icons in the graph.
- cssVariables: An object which accepts style values to paint the graph with custom styling. Check DEFAULT_CSS_VARIABLES to see the fields and default values.
- dimMergeCommits: boolean to enable/disable feature to dim merge commit rows by default.
- shaLength: to specify the length of the sha to be displayed on the graph component. This property is optional and its default value is 6.
- highlightedShas: Used to highlight graph rows by SHA. This property is optional and definition is as following: { [sha: string]: boolean }.
- highlightRowsOnRefHover: boolean to enable/disable feature to highlight the rows on ref hover.
- showGhostRefsOnRowHover: boolean to enable/disable feature to show ghost refs when hovering a row.
- showRemoteNamesOnRefs: boolean to enable/disable feature to show remote names on remote refs.
- enabledRefMetadataTypes: Optional array of active ref metadata types. Currently supported values are:
  - "pullRequest"
  - "upstream"
  - "issue"
- enabledScrollMarkerTypes: Optional array of active scroll marker types. Currently supported values are:
  - "localBranches"
  - "remoteBranches"
  - "tags"
  - "stashes"
  - "head"
  - "upstream"
  - "selection"
  - "highlights"
  - "pullRequests"
- scrollRowPadding: number to specify the padding of the row to be scrolled into view. This property is optional and its default value is 0.
- customFooterRow: ReactElement to customize the footer of the graph when is not currently in loading state and the list of commits is not empty. This property is optional with undefined value as default.
- graphCommitDescDisplayMode: optional enum to specify how the graph should display the description of commit messages. Possibles values are:
  - `ALWAYS`: always display commit message descriptions. **This is the default value.**
  - `ON_HOVER`: display commit message descriptions only when the row is hovered.
  - `NEVER`: never display commit message descriptions.
- refIconsPosition: optional enum to specify in which position the graph should display the ref node icons (avatars, issues, pull requests). Possibles values are:
  - `refIconsPositions.LEFT`: display icons to the left (before the ref label). **This is the default value.**
  - `refIconsPositions.RIGHT`: display icons to the right (after the ref label).
- isContainerWindowFocused: boolean to specify if the container window application has or not the focus. This property was added to be set from the Gitlens side (or Electron app) to notify the graph component that the application window focus has changed. In this way, the graph will be able to disable the hover of the refs if VSCode (or Electron app) loses the focus. This input property is not needed to be set on a normal case. Default value is true. This property was added to fix issue #153.
- wipMessageEditable: boolean. Optional. Default is false. Enable or disable the WIP commit message input box to edit an uncommited message.
- isCommitting: boolean to specify if commit is in progress or not. Useful to disable the WIP commit message input box when a commit is in progress then and avoid its edition. Value is optional and its default is false.
- pendingCommitMessageSummary: string. Optional. If it is set, it will take that value on the editable WIP commit message input box.
- onRowContextMenu: Event to use if we want to show a context menu of graph row. This property is optional and definition is as following: (event: any, graphZoneType: GraphZoneType, graphRow: GraphRow) => void.
- onRefContextMenu: Event to use if we want to show a context menu of graph ref node (column BRANCH/TAG). This property is optional and definition is as following: (event: any, refGroup: GraphRefGroup, graphRow: GraphRow) => void;
- onToggleRefsVisibilityClick: This event is triggered when we click hide/show button of ref nodes. This property is optional and definition is as following: (event: any, refs: GraphRefOptData[], visible: boolean, graphRow: GraphRow) => void
- onClickGraphRow: This event is triggered when we click over a graph row. This property is optional and definition is as following: (event: any, graphZoneType: GraphZoneType, graphRow: GraphRow) => void.
- onClickGraphRef: This event is triggered when we click over a ref node. This property is optional and definition is as following: (event: any, refGroup: GraphRefGroup, graphRow: GraphRow, metadataItem?: RefMetadataItemWithIdAndType) => void.
- onDoubleClickGraphRow: This event is triggered when we double click over a graph row. This property is optional and definition is as following: (event: any, graphZoneType: GraphZoneType, graphRow: GraphRow) => void.
- OnDoubleClickGraphRef: This event is triggered when we double click over a ref node. This property is optional and definition is as following: (event: any, refGroup: GraphRefGroup, graphRow: GraphRow, metadataItem?: RefMetadataItemWithIdAndType) => void.
- onSelectGraphRows: This event is triggered when we select graph rows. This property is optional and definition is as following: (selectedRows: GraphRow[]) => void.
- `onLoadSelectedGraphRow`: This event is triggered after selecting a graph row. Unlike the `onSelectGraphRows` event (which is called multiple times when using the navigation keys), this one is only called once (when releasing the up/down arrow keys or doing a mouse click over a row). This could be useful to avoid using a debounce in `onSelectGraphRows` to simulate the same effect. This property is optional and definition is as following: (graphRow: GraphRow) => void.
- onGraphRowHovered: This event is triggered when a row is hovered. This property is optional and definition is as following: (event: any, graphZoneType: GraphZoneType, graphRow: GraphRow) => void.
- onGraphRowUnhovered: This event is triggered when a row is unhovered. This property is optional and definition is as following: (event: any, graphZoneType: GraphZoneType, graphRow: GraphRow) => void.
- onGraphRefNodeHovered: This event is triggered when a ref node is hovered. This property is optional and definition is as following: (event: any, refGroup: GraphRefGroup, graphRow: GraphRow) => void.
- onGraphRefNodeUnhovered: This event is triggered when a ref node is unhovered. This property is optional and definition is as following: (event: any, refGroup: GraphRefGroup, graphRow: GraphRow) => void.
- onGraphColumnsReOrdered: This event is triggered when columns are reordered. This property is optional and definition is as following: export type OnGraphColumnsReOrdered = (columnsSettings: GraphColumnsSettings) => void.
- onColumnResized: This event is triggered when a column is resized. This property is optional and definition is as following: (graphZoneType: GraphZoneType, columnSettings: GraphColumnSetting) => void.
- onGraphResized: This event is triggered when the graph container is resized. This property is optional and definition is as following: OnGraphResized = (width: number, height: number) => void.
- onGraphVisibleRowsChanged: This event triggers when the top and bottom visible rows of the graph view change. This property is optional and definition is as following: (topVisibleRow: GraphRow, bottomVisibleRow: GraphRow) => void.
- onGraphMouseEnter: This event triggers when the mouse enters the graph view. This property is optional and definition is as following: (event: any) => void.
- onGraphMouseLeave: This event triggers when the mouse leaves the graph view. This property is optional and definition is as following: (event: any) => void.
- onShowMoreCommits: This event is triggered when the user has scrolled close to the bottom of the graph and more commits are needed. This property is optional and determines if commits lazy loading is enabled or not. Definition is as following: () => void.
- onEmailsMissingAvatarUrls: This event triggers when the graph is scrolled and emails are found that do not have an avatar url. This property is optional and definition is as following: (emails: { [email: string]: string }) => void.
- onRefsMissingMetadata: This event triggers when the graph is scrolled and refs are found that do not have metadata. This property is optional and definition is as following: (refs: { [id: string]: RefMetadataType[] }) => void, where RefMetadataType is a string from the following options: "pullRequest".
- columnsSettings: to be able to set the the desired settings by column.

  - We can set the width of the columns by using the `width` property.
  - We can set the visibility of the columns by using the `isHidden` property.
  - We can set the mode of the column by using the `mode` property. Mode can be: `compact`, `rich` or `text` (see `GraphColumnMode` type for more details).
  - We can set the order of the column by using the `order` property.
  - We can set if a column can be filtered by using the `isFilterable` property. If this property is set to true, an icon will appear on the column header. If we click on it, the `onFilterColumnClick` input property will be called. This property can also be combined with `isFilterActive`.
  - We can set if a column filter is active or not by setting the `isFilterActive` property. This property will highlight the filter button of the column to indicate that the filter is active.

  For example, we can set following value:

```
{
  commitAuthorZone: {width: 81, isFilterable: true, isFilterActive: false, isHidden: true, order: 5, mode: 'text'},
  commitDateTimeZone: {width: 75, isFilterable: false, isFilterActive: false, isHidden: true, order: 4, mode: 'text'},
  commitMessageZone: {width: 271, isFilterable: false, isFilterActive: false, isHidden: false, order: 3, mode: 'text'},
  commitShaZone: {width: 100, isFilterable: false, isFilterActive: false, isHidden: true, order: 2, mode: 'text'},
  commitZone: {width: 120, isFilterable: false, isFilterActive: false, isHidden: false, order: 1, mode: 'compact'},
  refZone: {width: 179, isFilterable: false, isFilterActive: false, isHidden: false, order: 0, mode: 'text'},
}
```

- onPopupGraphHeaderContextMenu: Event to use if we want to show a context menu of graph header row when right clicking on a column header. This property is optional and definition is as following: (event: React.MouseEvent<any>, graphWidth: number) => void. Note: if this property is not set, the column settings button won't be displayed.
- onSettingsClick: This event is triggered when user click over the graph header settings button. This property is optional, therefore, if value is not defined, the settings button won't be displayed. Property definition is as following: (event: React.MouseEvent<any>, graphWidth: number) => void. Note: if this property is not set, the column settings button won't be displayed.

- onRefBeginDrag: This event fires when a branch or tag drag begins. This property is optional. Property definition is as following: (event: React.DragEvent, sourceRefData?: RefDndData) => void.
- onRefCanDrag: This event is fired when a ref node is displayed and if the returned value is true, then the user will be able to drag that ref node. This property is optional. Property definition is as following: (sourceRefData?: RefDndData) => boolean.
- onRefCanDrop: This event fires when a branch or tag is dragged over another ref. If the returned value is true, then the user will be able to drop the ref node over this one. This property is optional. Property definition is as following: (event?: React.DragEvent, sourceRefData?: RefDndData, targetRefData?: RefDndData) => boolean.
- onRefEndDrag: This event is fired when the drag of a branch or tag is finished. This property is optional. Property definition is as following: (event: React.DragEvent, sourceRefData?: RefDndData, targetRefData?: RefDndData) => void.
- onRefDragEnter: Called when an item is hovered over the component when dragging. This property is optional. Property definition is as following: (event: React.DragEvent, sourceRefData?: RefDndData, targetRefData?: RefDndData) => void.
- onRefDragLeave: Called when an item is unhovered when dragging. This property is optional. Property definition is as following: (event: React.DragEvent, sourceRefData?: RefDndData, targetRefData?: RefDndData) => void.
- onRefDrop: This event triggers when a branch or tag has been dropped into another one. This property is optional. Property definition is as following: (event: React.DragEvent, sourceRefData?: RefDndData, targetRefData?: RefDndData) => void.
- onFilterColumnClick: This event is triggered when user click over the graph header filter button. To display that icon, we should set the `isFilterable` property of the `columnsSettings` option to true (see `columnsSettings` doc for more details). This event is optional. Definition is as following: (event: React.MouseEvent<any>, graphZoneType: GraphZoneType) => void.

- onRefCreate: This event triggers when a new branch or tag has been created in the graph. This property is optional. Property definition is as following: (shorthand: RefShorthand, sha: Sha, refType: GraphRefType, data?: any | null) => void.
- onRefCreateCancel: This event triggers when the creation of a new branch or tag has been cancelled in the graph. This property is optional. Property definition is as following: (shorthand: RefShorthand, sha: Sha, refType: GraphRefType, data?: any | null) => void.
- OnRefCreateContextMenu: Event to use if we want to show a context menu on the input box to enter the name when creating a new ref. This property is optional. Property definition is as following: (event: React.MouseEvent<any>, shorthand: RefShorthand, sha: Sha, refType: GraphRefType, data?: any | null) => void.

- nonce: A string to be used as a nonce for the graph component. Read more here: https://developer.mozilla.org/en-US/docs/Web/HTML/Global_attributes/nonce. This property is optional.

- workDirStats: Optional property to set additional info on the WIP node of the graph. For example we can set:

```
{
	added: 2; // Number of uncommitted added files
	deleted: 1; // Number of uncommitted deleted files
	modified: 3; // Number of uncommitted modified files
	context?: { ... }; // Object or string used in VSCode to set the context. This data will be stringifyied a JSON and added into the DOM by using the `data-vscode-context` property. This is used to display context menus for example.
	renamed?: 1; // Number of uncommitted renamed files. This value is optional.
}
```
