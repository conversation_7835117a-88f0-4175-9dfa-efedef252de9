import type { ReactElement } from 'react';
import React from 'react';
import type { GraphMarkerMetadata, GraphMarkerShapeMetadata, GraphMarkerType } from '../../domain/graph/GraphConstants';
import {
	GRAPH_ROW_HEIGHT,
	GRAPH_SCROLL_MARKER_LANES,
	graphMarkerMetadata,
	graphMarkerShapeMetadata,
} from '../../domain/graph/GraphConstants';
import type { GraphMarkerColors, GraphMarkerRowIndices } from '../../domain/graph/GraphTypes';

interface Props {
	branchUpstreamRowIndices: number[];
	enabledScrollMarkerTypes: GraphMarkerType[];
	viewportWidth: number;
	viewportHeight: number;
	markerRowIndices: GraphMarkerRowIndices;
	markerColors: GraphMarkerColors;
	scrollWidth?: number;
	totalRows: number;
}

function calculateMarkerHeightAndOffset(
	type: GraphMarkerType,
	totalRows: number,
	viewportHeight: number,
): { height: number; offset: number } {
	const containerHeight = totalRows * GRAPH_ROW_HEIGHT;
	const { shape } = graphMarkerMetadata[type];
	const shapeMetadata: GraphMarkerShapeMetadata | undefined = graphMarkerShapeMetadata[shape];

	if (!shapeMetadata) {
		return { height: 0, offset: 0 };
	}

	const { baseHeight, minHeight, maxHeight } = shapeMetadata;
	let normalizedHeight: number = baseHeight * (viewportHeight / containerHeight);
	if (minHeight !== undefined && normalizedHeight < minHeight) {
		normalizedHeight = minHeight;
	}

	if (maxHeight !== undefined && normalizedHeight > maxHeight) {
		normalizedHeight = maxHeight;
	}

	const normalizedRowHeight = GRAPH_ROW_HEIGHT * (viewportHeight / containerHeight);
	const offset = normalizedHeight >= normalizedRowHeight ? 0 : (normalizedRowHeight - normalizedHeight) / 2;

	// normalize the height and offset to the viewport height
	return {
		height: normalizedHeight,
		offset: offset,
	};
}

function calculateMarkerVerticalPosition(index: number, viewportHeight: number, totalRows: number): number {
	const containerHeight = totalRows * GRAPH_ROW_HEIGHT;
	const containerPosition = index * GRAPH_ROW_HEIGHT;
	const containerPositionFactor = containerPosition / containerHeight;
	return containerPositionFactor * viewportHeight;
}

export default class ScrollMarker extends React.PureComponent<Props> {
	canvas: HTMLCanvasElement | null;

	override componentDidMount(): void {
		this.updateCanvas();
	}

	override componentDidUpdate(nextProps: Props): void {
		if (
			nextProps.branchUpstreamRowIndices !== this.props.branchUpstreamRowIndices ||
			nextProps.markerRowIndices !== this.props.markerRowIndices ||
			nextProps.totalRows !== this.props.totalRows ||
			nextProps.viewportHeight !== this.props.viewportHeight ||
			nextProps.viewportWidth !== this.props.viewportWidth ||
			nextProps.markerColors !== this.props.markerColors ||
			nextProps.enabledScrollMarkerTypes !== this.props.enabledScrollMarkerTypes ||
			nextProps.scrollWidth !== this.props.scrollWidth
		) {
			this.updateCanvas();
		}
	}

	updateCanvas(nextProps?: Props): void {
		const {
			branchUpstreamRowIndices,
			enabledScrollMarkerTypes,
			viewportHeight,
			markerRowIndices,
			markerColors,
			scrollWidth,
			totalRows,
		} = nextProps || this.props;
		const canvas = this.canvas.getContext('2d');
		canvas.canvas.width = scrollWidth;
		canvas.canvas.height = viewportHeight;
		const width = scrollWidth;
		const laneWidth = width / GRAPH_SCROLL_MARKER_LANES;

		canvas.clearRect(0, 0, canvas.canvas.width, canvas.canvas.height);

		for (const [markerType, markerMetadata] of Object.entries(graphMarkerMetadata) as [
			GraphMarkerType,
			GraphMarkerMetadata,
		][]) {
			if (
				!enabledScrollMarkerTypes.includes(markerType) &&
				!(markerType === 'remoteBranches' && enabledScrollMarkerTypes.includes('localBranches'))
			) {
				continue;
			}

			const indices: number[] =
				markerType === 'remoteBranches' && !enabledScrollMarkerTypes.includes('remoteBranches')
					? branchUpstreamRowIndices
					: markerRowIndices[markerType] || [];
			const color: string | undefined = markerColors[markerType];
			const markerLanes = markerMetadata.lanes;
			if (color && indices.length) {
				canvas.fillStyle = color;
				indices.forEach((index: number) => {
					const { height: markerHeight, offset: markerOffset } = calculateMarkerHeightAndOffset(
						markerType,
						totalRows,
						viewportHeight,
					);

					if (markerLanes.length) {
						canvas.fillRect(
							markerLanes[0] * laneWidth,
							calculateMarkerVerticalPosition(index, viewportHeight, totalRows) +
								markerOffset -
								0.5 * markerHeight,
							laneWidth * markerLanes.length,
							markerHeight,
						);
					}
				});
			}
		}
	}

	override render(): ReactElement<'canvas'> {
		return (
			<canvas
				className="graph-scroll-markers"
				ref={c => {
					this.canvas = c;
				}}
			/>
		);
	}
}
