//@ts-check
/** @typedef {import('webpack').Configuration} WebpackConfig **/

import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin';
import esbuild from 'esbuild';
import { ESLintLitePlugin } from '@eamodio/eslint-lite-webpack-plugin';
import ForkTsCheckerPlugin from 'fork-ts-checker-webpack-plugin';
import fs from 'fs';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import { createRequire } from 'module';
import { availableParallelism } from 'os';
import path from 'path';
import RemoveEmptyScriptsPlugin from 'webpack-remove-empty-scripts';
import TerserPlugin from 'terser-webpack-plugin';
import webpack from 'webpack';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const require = createRequire(import.meta.url);

const cores = Math.max(Math.floor(availableParallelism() / 6) - 1, 1);
const eslintWorker = { max: cores, filesPerWorker: 100 };

const DIST_PATH = path.resolve(__dirname, 'dist');
const ENTRY_SCRIPT_PATH = path.resolve(__dirname, './src/index.tsx');
const ENTRY_TYPES_PATH = path.resolve(__dirname, './src/types.ts');
const ENTRY_STYLE_PATH = path.resolve(__dirname, './src/styles/index.less');

/**
 * @param {{ analyzeBundle?: boolean; esbuild?: boolean; skipLint?: boolean } | undefined } env
 * @param {{ mode: 'production' | 'development' | 'none' | undefined }} argv
 * @returns { WebpackConfig }
 */
export default function (env, argv) {
	const mode = argv.mode || 'none';

	env = {
		analyzeBundle: false,
		esbuild: true,
		skipLint: false,
		...env,
	};

	const tsConfigPath = path.join(__dirname, 'tsconfig.json');

	/**
	 * @type WebpackConfig['plugins'] | any
	 */
	const plugins = [
		new ForkTsCheckerPlugin({
			async: false,
			formatter: 'basic',
			typescript: { configFile: tsConfigPath },
		}),
		new RemoveEmptyScriptsPlugin(),
		new MiniCssExtractPlugin(),
	];

	if (!env.skipLint) {
		plugins.push(
			new ESLintLitePlugin({
				files: path.join(__dirname, 'src', '**', '*.ts?(x)'),
				worker: eslintWorker,
				eslintOptions: {
					cache: true,
					cacheLocation: path.join(__dirname, '.eslintcache/'),
					cacheStrategy: 'content',
					// overrideConfigFile: path.join(__dirname, '.eslintrc.json'),
				},
			}),
		);
	}

	if (env.analyzeBundle) {
		const out = path.join(__dirname, 'out');
		if (!fs.existsSync(out)) {
			fs.mkdirSync(out);
		}

		plugins.push(
			new BundleAnalyzerPlugin({
				analyzerMode: 'static',
				generateStatsFile: true,
				openAnalyzer: false,
				reportFilename: path.join(out, 'bundle-report.html'),
				statsFilename: path.join(out, 'stats.json'),
			}),
		);
	}

	return {
		entry: {
			index: { import: ENTRY_SCRIPT_PATH, filename: 'index.js' },
			// indexTypes: { import: ENTRY_TYPES_PATH, filename: 'types.js' },
			styles: ENTRY_STYLE_PATH,
		},
		mode: mode,
		target: 'web',
		devtool: mode === 'production' && !env.analyzeBundle ? false : 'inline-source-map',
		output: {
			path: DIST_PATH,
			clean: true,
			library: { type: 'module' },
			libraryTarget: 'module',
		},
		experiments: { outputModule: true },
		optimization: {
			minimizer: [
				new TerserPlugin({
					minify: TerserPlugin.swcMinify,
					extractComments: false,
					parallel: true,
					terserOptions: {
						compress: {
							drop_console: true,
							drop_debugger: true,
							ecma: 2020,
							module: true,
						},
						format: { comments: false, ecma: 2020 },
					},
				}),
				new CssMinimizerPlugin({ minimizerOptions: { preset: require.resolve('cssnano-preset-advanced') } }),
			],
		},
		externals: {
			react: { module: 'react', commonjs: 'react', commonjs2: 'react', root: 'React' },
			'react-dom': { module: 'react-dom', commonjs: 'react-dom', commonjs2: 'react-dom', root: 'ReactDOM' },
		},
		module: {
			rules: [
				{ test: /\.m?js/, resolve: { fullySpecified: false } },
				{
					exclude: /\.d\.ts$/,
					include: path.join(__dirname, 'src'),
					test: /\.tsx?$/,
					use: env.esbuild
						? {
								loader: 'esbuild-loader',
								options: {
									format: 'esm',
									implementation: esbuild,
									target: ['es2023', 'chrome124', 'node20.14.0'],
									tsconfig: tsConfigPath,
								},
						  }
						: {
								loader: 'ts-loader',
								options: { configFile: tsConfigPath, experimentalWatchApi: true, transpileOnly: true },
						  },
				},
				{ test: /\.((c|le)ss)$/i, use: [MiniCssExtractPlugin.loader, 'css-loader', 'less-loader'] },
			],
		},
		resolve: {
			alias: { 'react-virtualized': '@axosoft/react-virtualized' },
			extensions: ['.tsx', '.ts', '.js', '.less', '.css'],
			modules: [path.join(__dirname, 'src'), 'node_modules'],
		},
		plugins: plugins,
	};
}
