import type { Sha } from '../commit/CommitTypes';
import type * as RefConstants from './RefConstants';

export type GraphRefType = (typeof RefConstants.refTypes)[keyof typeof RefConstants.refTypes];

export type RefFullName = string;
export type RefShorthand = string;

export type CreateRefFormData = {
	sha: Sha;
	shorthand: RefShorthand;
	type: GraphRefType;
	data?: any | null;
};

export type RefIconsPosition = RefConstants.refIconsPositions;
