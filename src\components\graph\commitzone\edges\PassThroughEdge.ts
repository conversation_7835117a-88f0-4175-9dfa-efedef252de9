import type { CommitType } from '../../../../domain/commit/CommitTypes';
import {
	buildPassThroughEdgeHash,
	getLineElement,
	getSvgStyles,
	getXValueAtColumn,
} from '../../../../domain/edge/EdgeHelpers';
import { COMMIT_ZONE_ROW_HEIGHT } from '../../../../domain/graph/GraphConstants';
import type { ColumnColorByColumn, EdgeCache, LineSvgProps, SvgElement } from '../../../../domain/graph/GraphTypes';

let passThroughEdgeCache: EdgeCache = {};

export function clearPassThroughEdgeCache() {
	passThroughEdgeCache = {};
}

export function getPassThroughEdge(
	column: number,
	type: CommitType,
	columnColorByColumn: ColumnColorByColumn,
	columnWidth: number,
	gutterWidth: number,
	strokeWidth: number,
	isCompact?: boolean,
): SvgElement {
	const hash: string = buildPassThroughEdgeHash(column, type, isCompact);

	let passThroughEdge: SvgElement | undefined = passThroughEdgeCache[hash];
	if (passThroughEdge) {
		return passThroughEdge;
	}

	const x: number = getXValueAtColumn(column, columnWidth, gutterWidth, isCompact);

	const svgProps: LineSvgProps = getSvgStyles(
		type,
		columnColorByColumn[column],
		strokeWidth,
		isCompact,
	) as LineSvgProps;
	svgProps.x1 = x;
	svgProps.x2 = x;
	svgProps.y1 = 0;
	svgProps.y2 = COMMIT_ZONE_ROW_HEIGHT;

	passThroughEdge = getLineElement(svgProps);

	passThroughEdgeCache[hash] = passThroughEdge;

	return passThroughEdge;
}
