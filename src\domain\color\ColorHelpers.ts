export const COLOR_WHITE = '#FFFFFF';
export const COLOR_BLACK = '#000000';

interface RGBA {
	type: 'rgba';
	r: number;
	g: number;
	b: number;
	a: number;
}

interface HSLA {
	type: 'hsla';
	h: number;
	s: number;
	l: number;
	a: number;
}

type Color = RGBA | HSLA;

const enum CharCode {
	/**
	 * The `#` character.
	 */
	Hash = 35,
	/**
	 * The `/` character.
	 */
	Slash = 47,
	Digit0 = 48,
	Digit1 = 49,
	Digit2 = 50,
	Digit3 = 51,
	Digit4 = 52,
	Digit5 = 53,
	Digit6 = 54,
	Digit7 = 55,
	Digit8 = 56,
	Digit9 = 57,
	/**
	 * The `\` character.
	 */
	Backslash = 92,
	A = 65,
	B = 66,
	C = 67,
	D = 68,
	E = 69,
	F = 70,
	Z = 90,
	a = 97,
	b = 98,
	c = 99,
	d = 100,
	e = 101,
	f = 102,
	z = 122,
}

function parseHexDigit(charCode: CharCode): number {
	switch (charCode) {
		case CharCode.Digit0:
			return 0;
		case CharCode.Digit1:
			return 1;
		case CharCode.Digit2:
			return 2;
		case CharCode.Digit3:
			return 3;
		case CharCode.Digit4:
			return 4;
		case CharCode.Digit5:
			return 5;
		case CharCode.Digit6:
			return 6;
		case CharCode.Digit7:
			return 7;
		case CharCode.Digit8:
			return 8;
		case CharCode.Digit9:
			return 9;
		case CharCode.a:
			return 10;
		case CharCode.A:
			return 10;
		case CharCode.b:
			return 11;
		case CharCode.B:
			return 11;
		case CharCode.c:
			return 12;
		case CharCode.C:
			return 12;
		case CharCode.d:
			return 13;
		case CharCode.D:
			return 13;
		case CharCode.e:
			return 14;
		case CharCode.E:
			return 14;
		case CharCode.f:
			return 15;
		case CharCode.F:
			return 15;

		// no default
	}

	return 0;
}

export function parseHexColor(hex: string): Color | null {
	hex = hex.trim();
	const length = hex.length;

	if (length === 0) {
		// Invalid color
		return null;
	}

	if (hex.charCodeAt(0) !== CharCode.Hash) {
		// Does not begin with a #
		return null;
	}

	if (length === 7) {
		// #RRGGBB format
		const r = 16 * parseHexDigit(hex.charCodeAt(1)) + parseHexDigit(hex.charCodeAt(2));
		const g = 16 * parseHexDigit(hex.charCodeAt(3)) + parseHexDigit(hex.charCodeAt(4));
		const b = 16 * parseHexDigit(hex.charCodeAt(5)) + parseHexDigit(hex.charCodeAt(6));
		return {
			type: 'rgba',
			r: r,
			g: g,
			b: b,
			a: 1,
		};
	}

	if (length === 9) {
		// #RRGGBBAA format
		const r = 16 * parseHexDigit(hex.charCodeAt(1)) + parseHexDigit(hex.charCodeAt(2));
		const g = 16 * parseHexDigit(hex.charCodeAt(3)) + parseHexDigit(hex.charCodeAt(4));
		const b = 16 * parseHexDigit(hex.charCodeAt(5)) + parseHexDigit(hex.charCodeAt(6));
		const a = 16 * parseHexDigit(hex.charCodeAt(7)) + parseHexDigit(hex.charCodeAt(8));
		return {
			type: 'rgba',
			r: r,
			g: g,
			b: b,
			a: a / 255,
		};
	}

	if (length === 4) {
		// #RGB format
		const r = parseHexDigit(hex.charCodeAt(1));
		const g = parseHexDigit(hex.charCodeAt(2));
		const b = parseHexDigit(hex.charCodeAt(3));
		return {
			type: 'rgba',
			r: 16 * r + r,
			g: 16 * g + g,
			b: 16 * b + b,
			a: 1,
		};
	}

	if (length === 5) {
		// #RGBA format
		const r = parseHexDigit(hex.charCodeAt(1));
		const g = parseHexDigit(hex.charCodeAt(2));
		const b = parseHexDigit(hex.charCodeAt(3));
		const a = parseHexDigit(hex.charCodeAt(4));
		return {
			type: 'rgba',
			r: 16 * r + r,
			g: 16 * g + g,
			b: 16 * b + b,
			a: (16 * a + a) / 255,
		};
	}

	// Invalid color
	return null;
}

function hue2rgb(p: number, q: number, t: number): number {
	if (t < 0) {
		t += 1;
	}

	if (t > 1) {
		t -= 1;
	}

	if (t < 1 / 6) {
		return p + (q - p) * 6 * t;
	}

	if (t < 1 / 2) {
		return q;
	}

	if (t < 2 / 3) {
		return p + (q - p) * (2 / 3 - t) * 6;
	}

	return p;
}

function hslToRgb(hsla: { h: number; s: number; l: number; a: number }): RGBA {
	const h = hsla.h / 360;
	const { s, l, a } = hsla;
	let r: number;
	let g: number;
	let b: number;

	if (s === 0) {
		r = g = b = l; // achromatic
	} else {
		const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
		const p = 2 * l - q;
		r = hue2rgb(p, q, h + 1 / 3);
		g = hue2rgb(p, q, h);
		b = hue2rgb(p, q, h - 1 / 3);
	}

	return { type: 'rgba', r: Math.round(r * 255), g: Math.round(g * 255), b: Math.round(b * 255), a: a };
}

const cssColorRegex = /^((?:rgb|hsl)a?)\((-?\d+%?)[,\s]+(-?\d+%?)[,\s]+(-?\d+%?)[,\s]*(-?[\d.]+%?)?\)$/i;
export function parseColor(value: string): Color | null {
	value = value.trim();
	const length = value.length;

	// Invalid color
	if (length === 0) {
		return null;
	}

	// Begin with a #
	if (value.charCodeAt(0) === CharCode.Hash) {
		return parseHexColor(value);
	}

	const result = cssColorRegex.exec(value);
	if (result == null) {
		return null;
	}

	const mode = result[1];
	let colors: number[];
	switch (mode) {
		case 'rgb':
		case 'hsl':
			colors = [parseInt(result[2], 10), parseInt(result[3], 10), parseInt(result[4], 10), 1];
			break;
		case 'rgba':
		case 'hsla':
			colors = [parseInt(result[2], 10), parseInt(result[3], 10), parseInt(result[4], 10), parseFloat(result[5])];
			break;
		default:
			return null;
	}

	if (mode === 'hsl' || mode === 'hsla') {
		return hslToRgb({ h: colors[0], s: colors[1], l: colors[2], a: colors[3] });
	}

	return { type: 'rgba', r: colors[0], g: colors[1], b: colors[2], a: colors[3] };
}

export function formatColor(color: Color): string {
	if (color.type === 'rgba') {
		return `rgba(${color.r}, ${color.g}, ${color.b}, ${Number(color.a.toFixed(2))})`;
	}

	return `hsla(${color.h}, ${(color.s * 100).toFixed(2)}%, ${(color.l * 100).toFixed(2)}%, ${Number(
		color.a.toFixed(2),
	)})`;
}

function isDark(rgba: Color): boolean {
	if (rgba.type === 'hsla') {
		rgba = hslToRgb(rgba);
	}

	const yiq = (rgba.r * 299 + rgba.g * 587 + rgba.b * 114) / 1000;
	return yiq < 128;
}

export const getTextColor = (hexCode: string): string => (isDark(parseColor(hexCode)) ? COLOR_WHITE : COLOR_BLACK);
