import classnames from 'classnames';
import type { CSSProperties, ReactElement, ReactNode } from 'react';
import React from 'react';
import type { IEvent, ISubscription } from '../../domain/generic/EventService';
import { eventService } from '../../domain/generic/EventService';

export type DndCanDrag = (sourceData?: any) => boolean;
export type DndOnBeginDrag = (event: React.DragEvent, sourceData?: any) => void;
export type DndOnDragEnter = (event: React.DragEvent, sourceData?: any, targetData?: any) => void;
export type DndOnDragLeave = (event: React.DragEvent, sourceData?: any, targetData?: any) => void;
export type DndOnEndDrag = (event: React.DragEvent, sourceData?: any, targetData?: any) => void;

export type DndCanDrop = (event?: React.DragEvent, sourceData?: any, targetData?: any) => boolean;
export type DndOnDrop = (event: React.DragEvent, sourceData?: any, targetData?: any) => void;

export type DndId = string;

export type DndChildBaseProps = {
	isDraggable?: boolean;
	isDragging?: boolean;
	isDroppable?: boolean;
	isOver?: boolean;
};

export type DndComponentProps = {
	children?: ReactElement<DndChildBaseProps>;
	className?: string;
	canDrag?: DndCanDrag | boolean;
	canDrop?: DndCanDrop | boolean;
	dndId: DndId;
	forwardDndPropsToChildren?: boolean;
	onBeginDrag?: DndOnBeginDrag;
	onDragEnter?: DndOnDragEnter;
	onDragLeave?: DndOnDragLeave;
	onDrop?: DndOnDrop;
	onEndDrag?: DndOnEndDrag;
	dndData?: any;
	style?: CSSProperties;
};

export type DndComponentState = {
	isDragging: boolean;
	isDroppable: boolean;
	isOver: boolean;
};

const ON_DRAG_START_EVENT_KEY = 'ON_DRAG_START';
const ON_DRAG_END_EVENT_KEY = 'ON_DRAG_END';

type ActiveId = DndId;
type DndDataTransfer = any;

let _activeId: ActiveId | null = null;
const _dataTransfer: Map<ActiveId, DndDataTransfer> = new Map();

class DndComponent extends React.Component<DndComponentProps, DndComponentState> {
	private dndComponentRef: any = React.createRef();
	private mounted: boolean = false;

	private onDragStartSubscription: ISubscription;
	private onDragEndSubscription: ISubscription;

	constructor(props: DndComponentProps) {
		super(props);

		this.onDragStartSubscription = eventService.subscribe(ON_DRAG_START_EVENT_KEY, (event: IEvent) => {
			const sourceData: DndDataTransfer | undefined = event.content !== null && _dataTransfer.get(event.content);
			this.setState({ isDroppable: this.canDrop(undefined, sourceData, this.props.dndData) });
		});

		this.onDragEndSubscription = eventService.subscribe(ON_DRAG_END_EVENT_KEY, () => {
			if (this.mounted) {
				this.setState({ isDroppable: false });
			}
		});

		this.state = {
			isDragging: false,
			isDroppable: false,
			isOver: false,
		};
	}

	override componentDidMount(): void {
		this.mounted = true;

		if (_activeId !== null) {
			const sourceData = _dataTransfer.get(_activeId);
			this.setState({ isDroppable: this.canDrop(undefined, sourceData, this.props.dndData) });
		}
	}

	override componentWillUnmount(): void {
		this.mounted = false;

		this.onDragStartSubscription.unsubscribe();
		this.onDragEndSubscription.unsubscribe();
	}

	private canDrop(event?: React.DragEvent, sourceData?: any, targetData?: any): boolean {
		const { canDrop } = this.props;

		return canDrop && (typeof canDrop === 'boolean' || canDrop(event, sourceData, targetData));
	}

	// Note: we add dynamically the "dragend" event when drag starts because the component could
	// be removed from the DOM. This should not happen in a normal situation but the refs nodes
	// components of the Graph (for example) if they are hovered, then the component is removed
	// and created again. Regarding the event listener: it will be removed automatically after
	// it has executed because of the "{ once: true }" parameter.
	private addDragEndEventListenerOnce(): void {
		const dndElement: Element | undefined = this.dndComponentRef?.current;
		if (dndElement) {
			dndElement.removeEventListener('dragend', this.onDragEnd);
			dndElement.addEventListener('dragend', this.onDragEnd, { once: true });
		}
	}

	private isDraggable(): boolean {
		const { canDrag, dndData } = this.props;

		if (canDrag) {
			return typeof canDrag === 'boolean' ? true : canDrag(dndData);
		}

		return false;
	}

	// NOTES: we use "event.stopPropagation()" in following events because sometimes in GKC,
	// draggable="true" is not working. I guess it is because we are using "react-dnd" at the
	// same time for other things. This workaround fixes the issue. See for more details:
	// https://github.com/frontend-collective/react-sortable-tree/issues/863
	// https://github.com/react-dnd/react-dnd/issues/2868

	private onDragEnter(event: React.DragEvent<HTMLElement>): void {
		event.stopPropagation();

		this.setState({ isOver: true });

		if (this.props.onDragEnter) {
			const sourceData = _dataTransfer.get(_activeId);
			this.props.onDragEnter(event, sourceData, this.props.dndData);
		}
	}

	private onDragLeave(event: React.DragEvent<HTMLElement>): void {
		event.stopPropagation();

		const sourceData = _dataTransfer.get(_activeId);

		if (this.props.onDragLeave) {
			this.props.onDragLeave(event, sourceData, this.props.dndData);
		}

		this.setState({ isDroppable: this.canDrop(undefined, sourceData, this.props.dndData), isOver: false });
	}

	private onDrag(event: React.DragEvent<HTMLElement>): void {
		event.stopPropagation();
	}

	private onDragOver(event: React.DragEvent<HTMLElement>): void {
		event.stopPropagation();

		const sourceData = _dataTransfer.get(_activeId);

		if (this.canDrop(event, sourceData, this.props.dndData)) {
			event.preventDefault();
			this.setState({ isOver: true });
		}
	}

	private onDragStart(event: React.DragEvent<HTMLElement>): void {
		event.stopPropagation();

		const { dndData, dndId, onBeginDrag } = this.props;

		_activeId = dndId;
		_dataTransfer.set(_activeId, dndData);

		// Notes: We add the "onDragEnd" event at runtime here for several reasons.
		// For more info, see the comment of the "addDragEndEventListener" procedure.
		this.addDragEndEventListenerOnce();

		eventService.broadcast({ name: ON_DRAG_START_EVENT_KEY, content: _activeId });

		if (onBeginDrag) {
			onBeginDrag(event, dndData);
		}

		this.setState({ isDragging: true });
	}

	private onDragEnd = (event: any): void => {
		event.stopPropagation();

		const { dndData, onEndDrag } = this.props;

		if (onEndDrag) {
			onEndDrag(event, _dataTransfer.get(_activeId), dndData);
		}

		eventService.broadcast({ name: ON_DRAG_END_EVENT_KEY, content: _activeId });

		_dataTransfer.delete(_activeId);
		_activeId = null;

		if (this.mounted) {
			this.setState({ isDragging: false });
		}
	};

	private onDrop(event: React.DragEvent<HTMLElement>): void {
		event.stopPropagation();

		const { dndData, onDrop } = this.props;

		if (onDrop) {
			onDrop(event, _dataTransfer.get(_activeId), dndData);
			this.setState({ isOver: false });
		}
	}

	cloneChildrenWithShallowMergedProps(children?: ReactNode, childProps?: DndChildBaseProps): ReactNode | undefined {
		if (children) {
			return React.Children.map(children, (child: ReactNode) =>
				React.isValidElement(child) ? React.cloneElement<DndChildBaseProps>(child, childProps) : child,
			);
		}

		return undefined;
	}

	override render(): ReactElement<any> {
		const { children, className, forwardDndPropsToChildren, style } = this.props;

		const { isDragging, isDroppable, isOver } = this.state;

		const isDraggable = this.isDraggable();

		const classes: string = classnames('dnd-component', className, {
			draggable: isDraggable,
			dragging: isDragging,
			droppable: isDroppable,
			hovering: isOver,
		});

		return (
			<div
				className={classes}
				draggable={isDraggable}
				onDrag={e => this.onDrag(e)}
				onDragEnter={e => this.onDragEnter(e)}
				onDragLeave={e => this.onDragLeave(e)}
				// Notes: "onDragEnd" is not added here for several reasons. Instead, it
				// will be added at runtime when needed. For more info, see the comment
				// of the "addDragEndEventListener" procedure.
				onDragOver={e => this.onDragOver(e)}
				onDragStart={e => this.onDragStart(e)}
				onDrop={e => this.onDrop(e)}
				ref={this.dndComponentRef}
				style={style}
			>
				{forwardDndPropsToChildren
					? this.cloneChildrenWithShallowMergedProps(children, {
							isDraggable: isDraggable,
							isDragging: isDragging,
							isDroppable: isDroppable,
							isOver: isOver,
					  })
					: children}
			</div>
		);
	}
}

export default DndComponent;
