// Avatar constants
export const DEFAULT_AVATAR_BACKGROUND_COLOR = '#199489';

// Graph Scroll Constants
export const GRAPH_SCROLL_MARKER_LANES = 3;

// Graph Row Constants
export const GRAPH_HEADER_ROW_HEIGHT = 26;
export const GRAPH_ROW_HEIGHT = 28;
export const GRAPH_ROW_INNER_HEIGHT = 22;
export const GRAPH_ROW_LAZY_LOAD_COMMITS_OFFSET = GRAPH_ROW_HEIGHT * 50; // load more commits at 50 from bottom
export const GRAPH_ROW_PADDING = GRAPH_ROW_HEIGHT - GRAPH_ROW_INNER_HEIGHT;

// Commit Zone Constants
export const COMMIT_NODE_MIN_ALPHA = 0.5;
export const COMMIT_ZONE_MARGIN_BOTTOM = 3;
export const COMMIT_ZONE_MARGIN_TOP = 3;
export const COMMIT_ZONE_ROW_HEIGHT = GRAPH_ROW_HEIGHT;
export const COMMIT_ZONE_ROW_INNER_HEIGHT = GRAPH_ROW_INNER_HEIGHT;
export const COMMIT_ZONE_EDGE_ARC_PADDING = 3;
export const COMMIT_ZONE_EDGE_ARC_RADIUS = COMMIT_ZONE_ROW_INNER_HEIGHT / 2;
export const COMMIT_ZONE_DEFAULT_VIEWPORT_WIDTH_MIN = 56;
export const COMMIT_ZONE_SHOW_ICON_WIDTH = 56;

// Author Zone Constants
export const COMMIT_AUTHOR_ZONE_MIN_WIDTH = 32;
export const COMMIT_AUTHOR_ZONE_AVATAR_OUTER_DIAMETER = GRAPH_ROW_INNER_HEIGHT;
export const COMMIT_AUTHOR_ZONE_AVATAR_INNER_DIAMETER = COMMIT_AUTHOR_ZONE_AVATAR_OUTER_DIAMETER - 4;
export const COMMIT_AUTHOR_ZONE_SHOW_ICON_WIDTH = 55;

// Date/Time Zone Constants
export const COMMIT_DATE_TIME_ZONE_MIN_WIDTH = 50;
export const TIMESTAMP_FORMAT_DATE_TIME = 'short+short';
export const COMMIT_DATE_TIME_ZONE_SHOW_ICON_WIDTH = 55;

// Sha Zone Constants
export const COMMIT_SHA_ZONE_MIN_WIDTH = 50;
export const COMMIT_SHA_ZONE_SHOW_ICON_WIDTH = 50;

// Message Zone Constants
export const COMMIT_MESSAGE_ZONE_MIN_WIDTH = 50;
export const COMMIT_MESSAGE_ZONE_SHOW_ICON_WIDTH = 55;

// Ref Zone Constants
export const REF_ZONE_TEXT_HEIGHT = 18;
export const REF_ZONE_MIN_WIDTH = 32;
export const REF_ZONE_MARGIN_LEFT = 2;
export const REF_ZONE_MARGIN_RIGHT = 6;
export const REF_NODE_ICON_WIDTH = 14;
export const REF_NODE_ICON_MARGIN = 5;
export const REF_NODE_ICON_SPACING = REF_NODE_ICON_WIDTH + REF_NODE_ICON_MARGIN;
export const REF_NODE_OUTER_SPACING = 10;
export const REF_ZONE_SHOW_ICON_WIDTH = 55;
export const REF_ZONE_MAX_REFS_TO_RENDER = 100;

// Changes Zone Constants
export const CHANGES_ZONE_MIN_WIDTH = 50;
export const CHANGES_BAR_MIN_WIDTH = 10;
export const CHANGES_BAR_RIGHT_MARGIN = 25;
export const CHANGES_ZONE_SHOW_ICON_WIDTH = 58;

// Header Row Constants
export const HEADER_ROW_HEIGHT = 22;
export const HEADER_ROW_MARGIN_BOTTOM = 2; // space between bottom of header row and top of first row in graph

// Graph Theme Constants
export const OPACITY_FACTOR_BY_THEME = {
	dark: 1,
	light: 0.5,
};

// WIP Node Constants
export const INLINE_SUMMARY_MARGIN_LEFT = 10;
export const TINY_FILES_READOUT_FONT_SIZE = 12;
export const TINY_FILES_READOUT_RIGHT_MARGIN = 6;
export const TINY_ICON_RIGHT_MARGIN = 3;
export const TINY_ICON_SIZE = 12;

// react-virtualized scroll alignment constants
// NOTE these values are defined in "react-virtualized_vx.x.x.js"
// they must be kept in accordance until Flow has better support.
export const SCROLL_TO_ALIGNMENT_AUTO = 'auto';
export const SCROLL_TO_ALIGNMENT_CENTER = 'center';

// Magic Numbers
export const LeftPanelToGraphMarginGap = 7;
export const ResizableHandleCorrection = 4;

export type TimelineMsgRowRenderIdType = 'timelineMessage';
export const timelineMsgRowRenderId = 'timelineMessage';

// Graph Zone Constants (consumes each of the individual Zone constants)
export type RefZoneType = 'ref';
export type CommitZoneType = 'graph';
export type CommitMessageZoneType = 'message';
export type CommitAuthorZoneType = 'author';
export type CommitDateTimeZoneType = 'datetime';
export type CommitShaZoneType = 'sha';
export type ChangesZoneType = 'changes';

export const refZone: RefZoneType = 'ref';
export const commitZone: CommitZoneType = 'graph';
export const commitMessageZone: CommitMessageZoneType = 'message';
export const commitAuthorZone: CommitAuthorZoneType = 'author';
export const commitDateTimeZone: CommitDateTimeZoneType = 'datetime';
export const commitShaZone: CommitShaZoneType = 'sha';
export const changesZone: ChangesZoneType = 'changes';

export enum GraphColumnMode {
	Compact = 'compact',
	Rich = 'rich',
	Text = 'text',
}

// Graph Scroll Marker Constants
export type GraphMarkerType =
	| 'head'
	| 'highlights'
	| 'localBranches'
	| 'pullRequests'
	| 'remoteBranches'
	| 'selection'
	| 'stashes'
	| 'tags'
	| 'upstream';

export enum GraphMarkerShape {
	Block = 'block',
	FullLine = 'fullLine',
	ThinLine = 'thinLine',
}

export interface GraphMarkerShapeMetadata {
	type: GraphMarkerShape;
	baseHeight: number;
	minHeight?: number;
	maxHeight?: number;
}

export const graphMarkerShapeMetadata: { [shape: string /* GraphMarkerShape */]: GraphMarkerShapeMetadata } = {
	[GraphMarkerShape.Block]: {
		type: GraphMarkerShape.Block,
		// eslint-disable-next-line no-implicit-coercion
		baseHeight: 1.0 * GRAPH_ROW_HEIGHT,
		minHeight: 5,
		maxHeight: 20,
	},
	[GraphMarkerShape.FullLine]: {
		type: GraphMarkerShape.FullLine,
		baseHeight: 0.5 * GRAPH_ROW_HEIGHT,
		minHeight: 2,
		maxHeight: 4,
	},
	[GraphMarkerShape.ThinLine]: {
		type: GraphMarkerShape.ThinLine,
		baseHeight: 0.25 * GRAPH_ROW_HEIGHT,
		minHeight: 1,
		maxHeight: 2,
	},
};

export interface GraphMarkerMetadata {
	type: GraphMarkerType;
	colorCssKey: string;
	lanes: number[];
	shape: GraphMarkerShape;
}

// Note: ordering matters here. Markers later in the list will be drawn on top of markers earlier in the list.
export const graphMarkerMetadata: { [marker in GraphMarkerType]: GraphMarkerMetadata } = {
	stashes: {
		type: 'stashes',
		colorCssKey: '--color-graph-scroll-marker-stashes',
		lanes: [0],
		shape: GraphMarkerShape.Block,
	},
	tags: {
		type: 'tags',
		colorCssKey: '--color-graph-scroll-marker-tags',
		lanes: [2],
		shape: GraphMarkerShape.Block,
	},
	remoteBranches: {
		type: 'remoteBranches',
		colorCssKey: '--color-graph-scroll-marker-remote-branches',
		lanes: [2],
		shape: GraphMarkerShape.Block,
	},
	localBranches: {
		type: 'localBranches',
		colorCssKey: '--color-graph-scroll-marker-local-branches',
		lanes: [0],
		shape: GraphMarkerShape.Block,
	},
	highlights: {
		type: 'highlights',
		colorCssKey: '--color-graph-scroll-marker-highlights',
		lanes: [1],
		shape: GraphMarkerShape.Block,
	},
	upstream: {
		type: 'upstream',
		colorCssKey: '--color-graph-scroll-marker-upstream',
		lanes: [1, 2],
		shape: GraphMarkerShape.Block,
	},
	pullRequests: {
		type: 'pullRequests',
		colorCssKey: '--color-graph-scroll-marker-pull-requests',
		lanes: [2],
		shape: GraphMarkerShape.Block,
	},
	head: {
		type: 'head',
		colorCssKey: '--scroll-marker-head-color',
		lanes: [0, 1],
		shape: GraphMarkerShape.Block,
	},
	selection: {
		type: 'selection',
		colorCssKey: '--color-graph-scroll-marker-selection',
		lanes: [0, 1, 2],
		shape: GraphMarkerShape.FullLine,
	},
};

export const numGraphColumnsDefault = 10;

export const DEFAULT_COMMIT_SHA_LENGTH = 6;

export type GraphZoneType =
	| RefZoneType
	| CommitZoneType
	| CommitMessageZoneType
	| CommitAuthorZoneType
	| CommitDateTimeZoneType
	| CommitShaZoneType
	| ChangesZoneType;

export interface GraphZoneMetaData {
	contentWidth?: number;
	headerLabelUntranslated: string;
	isCustomizable: boolean;
	listId: string;
	maximumWidth?: number;
	minimumWidth: number;
	showIconWidth: number;
}

export interface GraphZone extends GraphZoneMetaData {
	currentWidth: number;
	preferredWidth?: number;
	isActive?: boolean;
	isHidden: boolean;
	mode?: GraphColumnMode;
	type: GraphZoneType;
	order: number;
}

export const graphZoneMetaData: { [zone: string /* GraphZoneType */]: GraphZoneMetaData } = {
	[commitAuthorZone]: {
		headerLabelUntranslated: 'GraphHeader-CommitAuthor',
		isCustomizable: true,
		listId: 'commit-author-zone',
		minimumWidth: COMMIT_AUTHOR_ZONE_MIN_WIDTH,
		showIconWidth: COMMIT_AUTHOR_ZONE_SHOW_ICON_WIDTH,
	},
	[commitDateTimeZone]: {
		headerLabelUntranslated: 'GraphHeader-CommitDateTime',
		isCustomizable: true,
		listId: 'commit-date-time-zone',
		minimumWidth: COMMIT_DATE_TIME_ZONE_MIN_WIDTH,
		showIconWidth: COMMIT_DATE_TIME_ZONE_SHOW_ICON_WIDTH,
	},
	[commitMessageZone]: {
		headerLabelUntranslated: 'GraphHeader-CommitMessage',
		isCustomizable: true,
		listId: 'commit-message-zone',
		minimumWidth: COMMIT_MESSAGE_ZONE_MIN_WIDTH,
		showIconWidth: COMMIT_MESSAGE_ZONE_SHOW_ICON_WIDTH,
	},
	[commitShaZone]: {
		headerLabelUntranslated: 'GraphHeader-CommitSha',
		isCustomizable: true,
		listId: 'commit-sha-zone',
		minimumWidth: COMMIT_SHA_ZONE_MIN_WIDTH,
		showIconWidth: COMMIT_SHA_ZONE_SHOW_ICON_WIDTH,
	},
	[commitZone]: {
		headerLabelUntranslated: 'GraphHeader-CommitGraph',
		isCustomizable: true,
		listId: 'commit-zone',
		minimumWidth: COMMIT_ZONE_DEFAULT_VIEWPORT_WIDTH_MIN,
		showIconWidth: COMMIT_ZONE_SHOW_ICON_WIDTH,
	},
	[refZone]: {
		headerLabelUntranslated: 'GraphHeader-BranchTag',
		isCustomizable: true,
		listId: 'ref-zone',
		minimumWidth: REF_ZONE_MIN_WIDTH,
		showIconWidth: REF_ZONE_SHOW_ICON_WIDTH,
	},
	[changesZone]: {
		headerLabelUntranslated: 'GraphHeader-Changes',
		isCustomizable: true,
		listId: 'changes-zone',
		minimumWidth: CHANGES_ZONE_MIN_WIDTH,
		showIconWidth: CHANGES_ZONE_SHOW_ICON_WIDTH,
	},
};

export type TimelineEntry = {
	date: number; // unix ms timestamp
	label: string; // translatable string (used with `value`)
	timeUnit: string; // time unit e.g. 'day'
	value: number; // how many of the `unit` in the past this is (e.g. 3 when `unit` is 'days' means 3 days ago)
};

export const lookbackLimitByPeriod = {
	hour: 23,
	day: 6,
	week: 4,
	month: 11,
	year: 6,
};

export type TimelineEntriesByPeriod = {
	hour: TimelineEntry[];
	day: TimelineEntry[];
	week: TimelineEntry[];
	month: TimelineEntry[];
	year: TimelineEntry[];
};

export const DEFAULT_WORKDIR_STATS = { added: 0, deleted: 0, modified: 0 };

export enum graphCommitDescDisplayModes {
	ALWAYS = 'ALWAYS',
	ON_HOVER = 'ON_HOVER',
	NEVER = 'NEVER',
}

export type GraphCommitDescDisplayMode =
	| graphCommitDescDisplayModes.ALWAYS
	| graphCommitDescDisplayModes.ON_HOVER
	| graphCommitDescDisplayModes.NEVER;

export type ShiftSelectMode = 'simple' | 'topological';

export type GraphSearchMode = 'normal' | 'filter';
