import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import type { CommitType, Sha } from '../../../domain/commit/CommitTypes';
import type { CssVariables, Style } from '../../../domain/generic/GenericTypes';
import { commitZone } from '../../../domain/graph/GraphConstants';
import { parseContext } from '../../../domain/graph/GraphHelpers';
import type {
	ColumnColorByColumn,
	GetExternalIcon,
	GraphItemContext,
	GraphZoneModeConstants,
	OnClearCurrentlyHoveredGraphCommit,
	OnClickCommit,
	OnCommitContextMenu,
	OnCurrentlyHoveredGraphCommit,
	OnDoubleClickCommit,
	RowEdges,
} from '../../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';
import { getInitialsFromName } from '../../../utils/TextFormatting';
import { RefLineForCommit } from '../common/RefLine';
import Timeline from '../common/Timeline';
import { CommitZoneBackgroundStreak } from './BackgroundStreak';
import CommitNode from './CommitNode';
import Edges from './edges/Edges';
import { LeftGutter, RightGutter } from './Gutter';

type Props = {
	authorEmail: string;
	authorName: string;
	avatarContext?: GraphItemContext;
	avatarUrl?: string;
	clearCurrentlyHoveredGraphCommit: OnClearCurrentlyHoveredGraphCommit;
	column: number;
	columnColorByColumn: ColumnColorByColumn;
	columnForColoring?: number;
	commitZoneWidth: number;
	cssVariables: CssVariables;
	currentlyHoveredCommitSha?: Sha;
	edgeColumnMax: number;
	edges: RowEdges;
	getExternalIcon: GetExternalIcon;
	graphZoneModeConstants: GraphZoneModeConstants;
	hasRefs: boolean;
	hasTimeline: boolean;
	isActiveSha: boolean;
	isInUnsupportedRebase: boolean;
	isLastColumn: boolean;
	isSelected: boolean;
	leftGutterBoxShadowAlpha: number;
	leftGutterWidth: number;
	message: string;
	nodeOffset: number;
	nodeOpacity: number;
	numGraphColumns: number;
	onContextMenu: OnCommitContextMenu;
	onClickCommit: OnClickCommit;
	onDoubleClickCommit: OnDoubleClickCommit;
	rightGutterBoxShadowAlpha: number;
	rightGutterWidth: number;
	rowContext?: GraphItemContext;
	scrollLeft: number;
	setAsCurrentlyHoveredGraphCommit: OnCurrentlyHoveredGraphCommit;
	sha: Sha;
	shouldShowRefLine: boolean;
	style: Style;
	suppressNonRefRowTooltips?: boolean;
	translate: TranslationFn;
	type: CommitType;
	useAuthorInitialsForAvatars: boolean;
	zoneContext?: GraphItemContext;
};

export default class CommitZone extends React.PureComponent<Props> {
	override render(): ReactElement<'div'> {
		const {
			authorEmail,
			authorName,
			avatarContext,
			avatarUrl,
			clearCurrentlyHoveredGraphCommit,
			column,
			columnColorByColumn,
			columnForColoring,
			commitZoneWidth,
			cssVariables,
			currentlyHoveredCommitSha,
			edgeColumnMax,
			edges,
			hasRefs,
			hasTimeline,
			getExternalIcon,
			graphZoneModeConstants,
			isActiveSha,
			isInUnsupportedRebase,
			isLastColumn,
			isSelected,
			leftGutterBoxShadowAlpha,
			leftGutterWidth,
			message,
			nodeOffset,
			nodeOpacity,
			numGraphColumns,
			onContextMenu,
			onClickCommit,
			onDoubleClickCommit,
			rightGutterBoxShadowAlpha,
			rightGutterWidth,
			rowContext,
			scrollLeft,
			setAsCurrentlyHoveredGraphCommit,
			sha,
			shouldShowRefLine,
			suppressNonRefRowTooltips,
			style,
			translate,
			type,
			useAuthorInitialsForAvatars,
			zoneContext,
		} = this.props;

		// lists in less are 1-indexed based instead of 0 so this is compensatation.
		const columnForClassList: number = ((columnForColoring ?? column) % numGraphColumns) + 1;

		const baseClassName: string = classnames(
			'relative',
			'commit-zone',
			'height-100-percent',
			'pt3',
			type,
			`column-${columnForClassList}`,
			{
				'is-selected': isSelected,
				'has-active': isActiveSha,
			},
		);

		const maybeTimeline = hasTimeline ? <Timeline z2 /> : null;

		const zoneContent = (
			<div
				className={baseClassName}
				data-vscode-context={parseContext(zoneContext)}
				onContextMenu={isInUnsupportedRebase ? undefined : event => onContextMenu(event, commitZone, sha)}
				onDoubleClick={event => onDoubleClickCommit(event, commitZone, sha)}
				onMouseDown={event => onClickCommit(event, commitZone, sha)}
				onMouseEnter={event =>
					setAsCurrentlyHoveredGraphCommit(event, commitZone, sha, currentlyHoveredCommitSha)
				}
				onMouseLeave={event =>
					clearCurrentlyHoveredGraphCommit(event, commitZone, sha, currentlyHoveredCommitSha)
				}
				style={{
					...style,
					...(isLastColumn && { width: '100%' }),
				}}
			>
				{maybeTimeline}
				<LeftGutter
					boxShadowAlpha={leftGutterBoxShadowAlpha}
					hasTimeline={hasTimeline}
					scrollLeft={scrollLeft}
					width={leftGutterWidth}
				/>
				{!shouldShowRefLine ? null : (
					<RefLineForCommit
						graphZoneModeConstants={graphZoneModeConstants}
						hasRefs={hasRefs}
						isActiveSha={isActiveSha}
						nodeOffset={nodeOffset}
						type={type}
					/>
				)}
				<CommitNode
					authorEmail={authorEmail}
					authorInitials={getInitialsFromName(authorName)}
					authorName={authorName}
					avatarUrl={avatarUrl}
					column={column}
					context={avatarContext}
					cssVariables={cssVariables}
					getExternalIcon={getExternalIcon}
					graphZoneModeConstants={graphZoneModeConstants}
					left={nodeOffset}
					message={message}
					opacity={nodeOpacity}
					suppressNonRefRowTooltips={suppressNonRefRowTooltips}
					translate={translate}
					type={type}
					useAuthorInitialsForAvatars={useAuthorInitialsForAvatars}
				/>
				<CommitZoneBackgroundStreak column={column} graphZoneModeConstants={graphZoneModeConstants} />
				<Edges
					columnColorByColumn={columnColorByColumn}
					edgeColumnMax={edgeColumnMax}
					edges={edges}
					graphZoneModeConstants={graphZoneModeConstants}
					nodeColumn={column}
				/>
				<RightGutter
					boxShadowAlpha={rightGutterBoxShadowAlpha}
					commitZoneWidth={commitZoneWidth}
					hasTimeline={hasTimeline}
					scrollLeft={scrollLeft}
					width={rightGutterWidth}
				/>
			</div>
		);

		return rowContext ? <div data-vscode-context={parseContext(rowContext)}>{zoneContent}</div> : zoneContent;
	}
}
