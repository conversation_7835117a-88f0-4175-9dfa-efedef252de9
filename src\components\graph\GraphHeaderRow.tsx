import type { ReactElement } from 'react';
import React from 'react';
import type { GraphZone } from '../../domain/graph/GraphConstants';
import {
	HEADER_ROW_HEIGHT,
	HEADER_ROW_MARGIN_BOTTOM,
	LeftPanelToGraphMarginGap,
	ResizableHandleCorrection,
} from '../../domain/graph/GraphConstants';
import { parseContext } from '../../domain/graph/GraphHelpers';
import type {
	ExcludeRefsById,
	GetExternalIcon,
	GraphColumnsSettings,
	GraphItemContext,
	IncludeOnlyRefsById,
	OnFilterColumnClick,
	OnGraphColumnReOrdered,
	OnGraphZoneResize,
	OnGraphZoneResizeEnd,
	OnGraphZoneResizeFromPropChange,
	OnPopupGraphHeaderContextMenu,
	OnSettingsClick,
	OnToggleRefNodesShown,
} from '../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../domain/language/LanguageTypes';
import type { OnMouseEnter, OnMouseLeave } from '../../domain/ui/UiTypes';
import type { DndIsDraggable, DndIsDroppable, DndOnDrop } from '../dnd/DndContainer';
import DndContainer from '../dnd/DndContainer';
import { DraggableGraphHeader } from './common/DraggableGraphHeader';

type Props = {
	columnsSettings: GraphColumnsSettings;
	dragAppendToContainer?: Element | undefined;
	enableResizer?: boolean;
	enableShowHideRefsOptions: boolean;
	getExternalIcon: GetExternalIcon;
	excludeRefsById: ExcludeRefsById;
	headerContext?: GraphItemContext;
	includeOnlyRefsById: IncludeOnlyRefsById;
	rowsStatsLoading: boolean;
	onColumnReOrdered?: OnGraphColumnReOrdered;
	onFilterColumnClick: OnFilterColumnClick;
	onPopupGraphHeaderContextMenu?: OnPopupGraphHeaderContextMenu;
	onGraphZoneResize?: OnGraphZoneResize;
	onGraphZoneResizeEnd?: OnGraphZoneResizeEnd;
	onGraphZoneResizeFromPropChange?: OnGraphZoneResizeFromPropChange;
	onSettingsClick?: OnSettingsClick;
	onToggleRefNodesShown: OnToggleRefNodesShown;
	graphZones: GraphZone[];
	repoPath: string;
	showRemoteNamesOnRefs: boolean;
	translate: TranslationFn;
	width: number;
	height: number;
	settingsContext?: GraphItemContext;
};

class GraphHeaderRow extends React.PureComponent<Props> {
	canDrag = false;

	isDraggable: DndIsDraggable = (source: ReactElement<any>): boolean => {
		return this.canDrag && (source.props.isDraggable as boolean);
	};

	isDroppable: DndIsDroppable = (source: ReactElement<any>, target: ReactElement<any> | null): boolean => {
		return (
			source.props.graphZoneType !== target?.props?.graphZoneType &&
			(!target || (target.props.isDroppable as boolean))
		);
	};

	onDrop: DndOnDrop = (source: ReactElement<any>, target: ReactElement<any> | null): void => {
		if (this.props.onColumnReOrdered) {
			this.props.onColumnReOrdered(source.props.graphZoneType, target ? target.props.graphZoneType : null);
		}
	};

	onZoneHover: OnMouseEnter = (): void => {
		this.canDrag = true;
	};

	onZoneUnHover: OnMouseLeave = (): void => {
		this.canDrag = false;
	};

	override render(): ReactElement<'div'> {
		const {
			columnsSettings,
			dragAppendToContainer,
			enableResizer,
			enableShowHideRefsOptions,
			getExternalIcon,
			excludeRefsById,
			headerContext,
			includeOnlyRefsById,
			onFilterColumnClick,
			onPopupGraphHeaderContextMenu,
			onGraphZoneResize,
			onGraphZoneResizeEnd,
			onGraphZoneResizeFromPropChange,
			onSettingsClick,
			onToggleRefNodesShown,
			graphZones,
			repoPath,
			rowsStatsLoading,
			showRemoteNamesOnRefs,
			translate,
			width: graphContainerWidth,
			height: graphContainerHeight,
			settingsContext,
		} = this.props;

		// This thing's sole purpose is to make the header row look like it touches the left panel
		const headerGapPlug = (
			<div
				className="panel-bg0"
				style={{
					height: HEADER_ROW_HEIGHT,
					marginBottom: HEADER_ROW_MARGIN_BOTTOM,
					marginLeft: -LeftPanelToGraphMarginGap,
					minWidth: LeftPanelToGraphMarginGap + ResizableHandleCorrection,
				}}
			/>
		);

		return (
			<div
				className="graph-header flex"
				data-vscode-context={parseContext(headerContext)}
				onContextMenu={
					onPopupGraphHeaderContextMenu
						? e => onPopupGraphHeaderContextMenu(e, graphContainerWidth)
						: undefined
				}
				style={{ height: HEADER_ROW_HEIGHT, marginBottom: HEADER_ROW_MARGIN_BOTTOM }}
			>
				{headerGapPlug}
				<DndContainer
					className="flex"
					direction="horizontal"
					isDraggable={this.isDraggable}
					isDroppable={this.isDroppable}
					mirrorContainer={dragAppendToContainer}
					onDrop={this.onDrop}
				>
					{graphZones.map((graphZone: GraphZone) => (
						<DraggableGraphHeader
							columnSetting={columnsSettings[graphZone.type]}
							enableResizer={enableResizer}
							enableShowHideRefsOptions={enableShowHideRefsOptions}
							excludeRefsById={excludeRefsById}
							getExternalIcon={getExternalIcon}
							graphHeight={graphContainerHeight}
							graphWidth={graphContainerWidth}
							graphZones={graphZones}
							graphZoneType={graphZone.type}
							id={`${graphZone.type}Header`}
							includeOnlyRefsById={includeOnlyRefsById}
							isDraggable={graphZone.isCustomizable}
							isDroppable={graphZone.isCustomizable}
							key={`${graphZone.type}Header`}
							onFilterColumnClick={onFilterColumnClick}
							onHover={this.onZoneHover}
							onResize={onGraphZoneResize}
							onResizeEnd={onGraphZoneResizeEnd}
							onResizeFromPropChange={onGraphZoneResizeFromPropChange}
							onSettingsClick={onSettingsClick}
							onToggleRefNodesShown={onToggleRefNodesShown}
							onUnhover={this.onZoneUnHover}
							repoPath={repoPath}
							rowsStatsLoading={rowsStatsLoading}
							settingsContext={settingsContext}
							showRemoteNamesOnRefs={showRemoteNamesOnRefs}
							translate={translate}
						/>
					))}
				</DndContainer>
			</div>
		);
	}
}

export default GraphHeaderRow;
