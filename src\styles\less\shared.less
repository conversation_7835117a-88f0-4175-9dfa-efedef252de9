.gk-graph {
	.box-label {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 5px;
		font-weight: bolder;
		margin-left: 5px;

		border: 1px solid var(--section-border, rgba(255, 255, 255, 0.08));
		background-color: var(--panel__bg2, #3d424d);

		&.light-blue {
			color: var(--label__light-blue-color, #15a0bf);
			border: 2px solid var(--label__light-blue-color, #15a0bf);
		}

		&.purple {
			color: var(--label__purple-color, #c517b6);
			border: 2px solid var(--label__purple-color, #c517b6);
		}

		&.yellow {
			color: var(--label__yellow-color, #f2ca33);
			border: 2px solid var(--label__yellow-color, #f2ca33);
		}
	}

	.branch-select {
		& > div {
			display: flex;
			flex-direction: column;
			align-items: stretch;

			label {
				font-size: var(--fs-2, 1.2rem);
				font-weight: normal;
			}
		}

		& > div:first-child {
			padding-bottom: 10px;
		}
	}

	input.form-control[type='checkbox']:focus {
		box-shadow: none;
	}

	.comma {
		margin-right: 1px;
	}

	.fast-spin {
		-webkit-animation: fa-spin 0.75s infinite linear;
		animation: fa-spin 0.75s infinite linear;
	}

	.pointer-events-none {
		pointer-events: none;
	}

	.hover-icon {
		&:hover {
			color: var(--text-selected);
		}
	}

	.hover-button-text {
		&:hover {
			span {
				color: var(--text-selected);
			}
		}
	}

	.hover-text {
		&:hover {
			color: var(--text-selected);
		}
	}

	.hover-text-underline {
		&:hover {
			text-decoration: underline;
		}
	}

	.hosting-service-not-connected {
		text-align: center;
		padding: 20px;
		button {
			margin: 10px 0;
		}
	}

	.gk-text-area-width-300 {
		max-width: 300px;
	}

	.gk-text-input,
	.gk-text-input[disabled] {
		background-color: var(--input__bg, rgba(0, 0, 0, 0.2));
		border-color: var(--section-border, rgba(255, 255, 255, 0.08));
		color: var(--text-normal, rgba(255, 255, 255, 0.75));
		resize: none;
	}

	span.has-error {
		color: var(--red);
		font-weight: 700;
		font-size: 10px;
	}

	select.has-error {
		border-color: var(--red, #d9413d) !important;
		box-shadow: 0 0 0 1px var(--red, #d9413d);
	}

	.gk-submit-button-wrapper {
		position: relative;

		span.gk-submit-button-overlay {
			cursor: not-allowed;
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 1;
		}
	}

	.commit-popover {
		&.yellow {
			.arrow {
				border-bottom-color: var(--label__yellow-color, #f2ca33);

				&:after {
					border-bottom-color: var(--label__yellow-color, #f2ca33);
				}
			}

			border: 2px solid var(--label__yellow-color, #f2ca33);
			border-top: 4px solid var(--label__yellow-color, #f2ca33);
		}

		&.light-blue {
			.arrow {
				border-bottom-color: var(--label__light-blue-color, #15a0bf);

				&:after {
					border-bottom-color: var(--label__light-blue-color, #15a0bf);
				}
			}

			border: 2px solid var(--label__light-blue-color, #15a0bf);
			border-top: 4px solid var(--label__light-blue-color, #15a0bf);
		}
	}

	.merge-label {
		span {
			margin-left: 2px;
			margin-right: 2px;
		}
		.merge-branch-label {
			padding-left: 5px;
			padding-right: 5px;
		}

		&.light-blue {
			.merge-branch-label {
				color: var(--label__light-blue-color, #15a0bf);
				font-weight: 900;
				font-style: italic;
			}
		}

		&.purple {
			.merge-branch-label {
				background-color: var(--label__purple-color-f25);
				border-left: 1px solid var(--label__purple-color, #c517b6);
				border-right: 1px solid var(--label__purple-color, #c517b6);
			}
		}

		&.yellow {
			.merge-branch-label {
				color: var(--label__yellow-color, #f2ca33);
				font-weight: 900;
				font-style: italic;
			}
		}
	}

	.mini-commit-info {
		display: flex;
		justify-content: space-between;

		.col-left {
			width: var(--mini-commit-col-left-width, 30px);
		}

		.col-middle {
			max-width: calc(
				100% -
					(
						var(--mini-commit-col-left-width, 30px) + var(--mini-commit-col-middle-padding, 6px) * 2 +
							var(--mini-commit-col-right-width, 56px)
					)
			);
			width: 100%;
			margin-left: var(--mini-commit-col-middle-padding, 6px);
			margin-right: var(--mini-commit-col-middle-padding, 6px);
			color: var(--text-normal, rgba(255, 255, 255, 0.75));

			.message,
			.signature {
				letter-spacing: 0.02em;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;

				line-height: 16px;
			}
			.message {
				font-weight: 400;
				font-size: var(--fs-2, 1.2rem);
			}
			.signature {
				font-weight: 300;
				font-size: 10px;

				.date {
					color: var(--text-secondary, rgba(255, 255, 255, 0.6));
				}
			}
		}

		.col-right {
			width: var(--mini-commit-col-right-width, 56px);

			.sha {
				font-family: var(
					--font-monospace,
					'Monaco',
					'Menlo',
					'Ubuntu Mono',
					'Consolas',
					'source-code-pro',
					monospace
				);
				font-size: var(--fs-2, 1.2rem);
				user-select: default;
				color: var(--text-secondary, rgba(255, 255, 255, 0.6));
				border: 1px solid transparent;
				line-height: 12px;
				padding: 0 5px;

				&:hover {
					color: var(--text-normal, rgba(255, 255, 255, 0.75));
					text-decoration: underline;
				}
			}
		}
	}

	.gpg-icon-tooltip {
		opacity: 1 !important;
		background-color: var(--panel__bg1);
		padding-left: 0px !important;
		padding-right: 0px !important;
		border-radius: 4px;
		box-shadow: 0 6px 12px var(--shadow-color, rgba(0, 0, 0, 0.4));

		.tooltip-arrow {
			display: none;
		}

		&.tooltip.bottom {
			padding: 0px;
		}
	}

	.tier-label {
		height: 15px;
		transform: skew(-20deg);
		text-align: center;
		display: inline-block;
		cursor: pointer;
		background: #e75225;
		padding: 0 10px;

		&.standalone-label {
			cursor: default;
		}

		.tier-label-text {
			transform: skew(20deg);
			color: white;
			font-style: normal;
			font-weight: 400;
			line-height: 15px;
		}

		span {
			margin-left: 5px;
		}
	}

	.modal {
		background-color: var(--modal-overlay-color, rgba(0, 0, 0, 0.5));

		.modal-content {
			background-color: var(--panel__bg1, #32363f);
			border-radius: 0;

			.modal-header {
				display: flex;
				padding: 10px 20px;
				border-bottom: 1px solid var(--panel-border);
				background-color: var(--panel__bg2, #3d424d);
				align-items: center;
			}

			.modal-body {
				padding: 30px 40px;
				color: var(--text-normal, rgba(255, 255, 255, 0.75));
			}
		}

		.icon-and-label-button {
			align-items: center;
			display: flex;
			margin-bottom: 20px;
			width: 100%;

			.icon-and-label-button--icon-wrapper {
				justify-content: center;
				display: flex;
				padding-right: 12px;
				width: 42px;
				border-right: 2px solid var(--text-selected);

				.icon-and-label-button--icon {
					font-size: 2em;
				}
			}

			.icon-and-label-button--label {
				margin-left: 12px;
			}
		}
	}

	.close-button {
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.lfs-icon {
		font-family: var(--font-default);
		font-size: 10px;
		color: var(--text-accent);
		border: 1px solid var(--text-accent);
		padding: 0 0.5rem;
		border-radius: 2px;
		margin-left: 2rem;
		letter-spacing: 0.1rem;
	}

	.beta-tag {
		color: black;
		font-size: 9px;
		font-weight: 600;
		background-color: var(--orange);
		padding: 0 0.25em;
		border-radius: 2px;
	}

	.glo-beta-tag {
		color: black;
		font-size: 9px;
		font-weight: 600;
		background-color: #00ffe0;
		padding: 0 0.25em;
		border-radius: 2px;
	}

	.wrap {
		white-space: pre-wrap;
		word-wrap: break-word;
	}

	#toast-container {
		.animated {
			animation-duration: 0.25s;
		}
	}

	.feature-table {
		display: flex;
		margin-bottom: 20px;

		.column {
			display: flex;
			flex-direction: column;

			> * {
				height: 3rem;
			}

			*:first-child {
				display: flex;
				align-items: center;
				font-weight: bold;
				height: 32px;
			}

			&.features {
				padding: 0 20px;
				background-color: var(--panel__bg2);
			}

			&.checklist {
				align-items: center;
				width: 100px;
				margin: 0 1px;
				background-color: var(--panel__bg2);

				&.highlighted {
					background-color: var(--selected-row);
					border: 1px solid var(--blue);
				}

				.check {
					color: var(--green);
				}
			}
		}
	}

	.jql-input-icon {
		&.fa-check-circle {
			color: var(--green);
		}
		&.fa-times-circle {
			color: var(--red);
		}
	}
}
