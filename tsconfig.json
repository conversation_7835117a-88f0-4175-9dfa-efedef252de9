{"compilerOptions": {"baseUrl": ".", "declaration": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "incremental": true, "isolatedModules": true, "jsx": "react", "lib": ["es2023", "dom"], "libReplacement": false, "module": "esnext", "moduleResolution": "bundler", "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noUnusedLocals": false, "outDir": "dist", "resolveJsonModule": true, "rootDir": "src", "skipLibCheck": true, "sourceMap": true, "strict": true, "strictNullChecks": false, "target": "es2023", "useDefineForClassFields": true, "useUnknownInCatchVariables": false}, "include": ["src/**/*"]}