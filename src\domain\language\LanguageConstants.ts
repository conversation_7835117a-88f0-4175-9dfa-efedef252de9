// Default translations
// See GK<PERSON> file for more details: src/strings/en-us.json
export const DEFAULT_TRANSLATIONS: { [key: string]: string } = {
	'CommitDiffSection-FileAdded': 'added',
	'CommitDiffSection-FileDeleted': 'deleted',
	'CommitDiffSection-FileModified': 'modified',
	'CommitDiffSection-FileRenamed': 'renamed',

	'CommitDiffSection-NFilesAdded': '{0} added',
	'CommitDiffSection-NFilesDeleted': '{0} deleted',
	'CommitDiffSection-NFilesModified': '{0} modified',
	'CommitDiffSection-NFilesRenamed': '{0} renamed',

	'Graph-IsLoadingRows': 'Loading...',
	'Graph-NoCommits': 'No commits',
	'Graph-WorkInProgress': 'Work in progress',
	'GraphHeader-BranchTag': 'BRANCH / TAG',
	'GraphHeader-Changes': 'CHANGES',
	'GraphHeader-CommitAuthor': 'AUTHOR',
	'GraphHeader-CommitDateTime': 'COMMIT DATE / TIME',
	'GraphHeader-CommitGraph': 'GRAPH',
	'GraphHeader-CommitMessage': 'COMMIT MESSAGE',
	'GraphHeader-CommitSha': 'SHA',
	'GraphHeader-EditColumns': 'Columns settings',
	'GraphHeader-Filter': 'Filter',
	'GraphHeader-HiddenRefs-btn': 'Hidden Branches / Tags',
	'OptionalGraphZone-CommitAuthor': 'Author',
	'OptionalGraphZone-CommitDateTime': 'Date / Time',
	'OptionalGraphZone-CommitSha': 'Sha',

	'IssueIcon-Tooltip': '{0}: {1}',
	'PullRequestIcon-Tooltip': '#{0}: {1}\n Double-click to open pull request on {2}',

	'Ref-Current': 'Current Branch',
	'Ref-Local': 'Local Branch',
	'Ref-Remote': 'Remote Branch',
	'Ref-Tag': 'Tag',
	'Ref-Worktree': 'Worktree',

	'RefZone-EnterBranchName': 'enter branch name',
	'RefZone-EnterTagName': 'enter tag name',

	'Timeline-1HourAgo': '1 hour ago',
	'Timeline-NHoursAgo': '{0} hours ago',
	'Timeline-Yesterday': 'yesterday',
	'Timeline-NDaysAgo': '{0} days ago',
	'Timeline-1WeekAgo': 'a week ago',
	'Timeline-NWeeksAgo': '{0} weeks ago',
	'Timeline-1MonthAgo': 'a month ago',
	'Timeline-NMonthsAgo': '{0} months ago',
	'Timeline-1YearAgo': 'a year ago',
	'Timeline-NYearsAgo': '{0} years ago',
	'Timeline-NPlusYearsAgo': '{0}+ years ago',

	'UpstreamIndicatorIcon-BehindAndAheadTooltip':
		'{0} commit(s) behind and {1} commit(s) ahead of {2}\n Double-click to pull changes',
	'UpstreamIndicatorIcon-BehindTooltip': '{0} commit(s) behind {1}\n Double-click to pull changes',
	'UpstreamIndicatorIcon-AheadTooltip': '{0} commit(s) ahead of {1}\n Double-click to push changes',

	ResizePanel: 'Resize Panel',
	Stash: 'Stash',
	Hide: 'Hide',
	Show: 'Show',

	'WorkDirMessageInput-WIPPlaceholder': 'WIP',
};
