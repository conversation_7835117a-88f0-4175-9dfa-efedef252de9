import type { <PERSON>rid<PERSON>ellP<PERSON>, GridCellRenderer } from 'react-virtualized';
import type { ReactElement } from 'react';
import React from 'react';
import { CommitDateTimeSources, mergeNodeType, workDirType } from '../../../domain/commit/CommitConstants';
import type { GraphZoneType } from '../../../domain/graph/GraphConstants';
import { commitDateTimeZone } from '../../../domain/graph/GraphConstants';
import { GraphRowHelper } from '../../../domain/graph/GraphHelpers';
import type {
	CommonGraphRowDispatchProps,
	CommonGraphRowProps,
	GraphItemContext,
	OnFormatCommitDateTime,
	ProcessedGraphRow,
} from '../../../domain/graph/GraphTypes';
import type { GetRealKeyForCellFunc } from '../../../domain/reactvirtualized/ReactVirtualizedHelpers';
import GenericGraphZone from '../common/GenericGraphZone';

interface DispatchProps {
	formatCommitDateTime: OnFormatCommitDateTime;
}

interface Props extends CommonGraphRowProps, CommonGraphRowDispatchProps, DispatchProps {}

const makeCommitDateTimeZoneRowRenderer = (props: Props, getKeyForCell: GetRealKeyForCellFunc): GridCellRenderer => {
	const {
		processedRows,
		getExternalIcon,
		highlightRowsOnRefHover,
		isInUnsupportedRebase,
		numGraphColumns,
		formatCommitDateTime,
		clearCurrentlyHoveredGraphCommit,
		dimMergeCommits,
		dimRowsOfSelectedCommit,
		currentlyHoveredCommitSha,
		onCommitContextMenu,
		onClickCommit,
		onDoubleClickCommit,
		setAsCurrentlyHoveredGraphCommit,
		suppressNonRefRowTooltips,
	} = props;

	const rowHelper: GraphRowHelper = new GraphRowHelper(props);

	const rowRenderer = ({ rowIndex: index, style }: GridCellProps): ReactElement<any> | undefined => {
		const key: string = getKeyForCell(index);

		const graphRow: ProcessedGraphRow = processedRows[index];
		const { date, sha, type, contexts } = graphRow;
		const contents: string | undefined =
			date && type !== workDirType ? formatCommitDateTime(date, CommitDateTimeSources.RowEntry) : undefined;
		const tooltip: string | undefined =
			date && type !== workDirType ? formatCommitDateTime(date, CommitDateTimeSources.Tooltip) : undefined;
		const context: GraphItemContext | undefined = contexts?.date || undefined;
		const rowContext: GraphItemContext | undefined = contexts?.row || undefined;
		const zoneType: GraphZoneType = commitDateTimeZone;
		const showColorStrip = rowHelper.isColumnFollowingGraphColumn(zoneType);
		const isLastColumn = rowHelper.isLastColumn(zoneType);
		const zoneWidth = rowHelper.getZoneWidth(zoneType);
		const verticalScrollWidth: number = rowHelper.getVerticalScrollWidth(zoneType);

		return (
			<GenericGraphZone
				clearCurrentlyHoveredGraphCommit={clearCurrentlyHoveredGraphCommit}
				column={graphRow.column}
				columnForColoring={graphRow.columnForColoring}
				context={context}
				currentlyHoveredCommitSha={currentlyHoveredCommitSha}
				dimRowsOfSelectedCommit={dimRowsOfSelectedCommit}
				getExternalIcon={getExternalIcon}
				graphZoneType={zoneType}
				highlightRowsOnRefHover={highlightRowsOnRefHover}
				isDimmedMergeCommitRow={type === mergeNodeType && dimMergeCommits}
				isHighlighted={rowHelper.isHighlighted(index)}
				isHovering={rowHelper.isHovering(index)}
				isInUnsupportedRebase={isInUnsupportedRebase}
				isLastColumn={isLastColumn}
				isMissingHoveredRefGroup={rowHelper.isMissingHoveredRefGroup(index)}
				isSelected={rowHelper.isSelected(index)}
				key={key}
				numGraphColumns={numGraphColumns}
				onClickCommit={onClickCommit}
				onContextMenu={onCommitContextMenu}
				onDoubleClickCommit={onDoubleClickCommit}
				rowContext={rowContext}
				setAsCurrentlyHoveredGraphCommit={setAsCurrentlyHoveredGraphCommit}
				sha={sha}
				showColorStrip={showColorStrip}
				showTimeline={false}
				style={style}
				title={suppressNonRefRowTooltips ? undefined : tooltip}
				type={type}
				verticalScrollWidth={verticalScrollWidth}
				zoneWidth={zoneWidth}
			>
				{contents}
			</GenericGraphZone>
		);
	};

	return rowRenderer;
};

export default makeCommitDateTimeZoneRowRenderer;
