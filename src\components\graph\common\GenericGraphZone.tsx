import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import type { CommitType, Sha } from '../../../domain/commit/CommitTypes';
import type { HashMap, Style } from '../../../domain/generic/GenericTypes';
import type { GraphZoneType } from '../../../domain/graph/GraphConstants';
import { GRAPH_ROW_INNER_HEIGHT } from '../../../domain/graph/GraphConstants';
import { parseContext } from '../../../domain/graph/GraphHelpers';
import type {
	GetExternalIcon,
	GraphItemContext,
	OnClearCurrentlyHoveredGraphCommit,
	OnClickCommit,
	OnCommitContextMenu,
	OnCurrentlyHoveredGraphCommit,
	OnDoubleClickCommit,
} from '../../../domain/graph/GraphTypes';
import type { ChildrenElements } from '../../../domain/ui/UiTypes';
import Timeline from './Timeline';

type Props = {
	clearCurrentlyHoveredGraphCommit: OnClearCurrentlyHoveredGraphCommit;
	children: ChildrenElements;
	column: number;
	columnForColoring?: number;
	context?: GraphItemContext | null;
	currentlyHoveredCommitSha: Sha | null;
	dimRowsOfSelectedCommit?: boolean;
	getExternalIcon: GetExternalIcon;
	graphZoneType: GraphZoneType;
	hasAvatars?: boolean;
	highlightRowsOnRefHover: boolean;
	isDimmedMergeCommitRow: boolean;
	isHighlighted: boolean;
	isInUnsupportedRebase: boolean;
	isHovering: boolean;
	isLastColumn: boolean;
	isMissingHoveredRefGroup?: boolean;
	isSelected: boolean;
	numGraphColumns: number;
	onContextMenu: OnCommitContextMenu;
	onClickCommit: OnClickCommit;
	onDoubleClickCommit: OnDoubleClickCommit;
	rowContext?: GraphItemContext | null;
	setAsCurrentlyHoveredGraphCommit: OnCurrentlyHoveredGraphCommit;
	sha: Sha;
	showColorStrip?: boolean;
	showConflictIcon?: boolean;
	showTimeline?: boolean;
	showTimelineMessage?: boolean;
	style: Style;
	title?: string | null;
	type: CommitType;
	verticalScrollWidth: number;
	zoneWidth: number;
};

// this component has to be super-fast, so we use string `+` which is faster in V8

const colorStripStyle: Style = { width: 2 };

const colorStrip: ReactElement<'div'> = <div className="color-strip height-100" style={colorStripStyle} />;

const baseClassesCache: HashMap = {};

function getBaseClasses(type: CommitType, isHovering: boolean): string {
	const hash: string = type + isHovering.toString();

	let baseClasses: string | undefined = baseClassesCache[hash];
	if (baseClasses) {
		return baseClasses;
	}

	baseClasses = classnames(
		'graph-row-wrapper',
		'grow-3',
		'height-100',
		'graph-zone-column',
		'min-width-0',
		'pb3',
		'pointer',
		'pt3',
		'relative',
		type,
		{
			'is-hovering': isHovering,
		},
	);

	baseClassesCache[hash] = baseClasses;

	return baseClasses;
}

const innerClassesCache: HashMap = {};

function getInnerClasses(
	type: CommitType,
	columnForClassList: number,
	isHighlighted: boolean,
	isSelected: boolean,
	numGraphColumns: number,
	isMissingHoveredRefGroup = false,
	highlightRowsOnRefHover = false,
	dimRowsOfSelectedCommit = false,
	isDimmedMergeCommitRow = false,
): string {
	const hash = `${type}${columnForClassList}${isSelected}${isHighlighted}${isMissingHoveredRefGroup}${highlightRowsOnRefHover}${dimRowsOfSelectedCommit}${isDimmedMergeCommitRow}${numGraphColumns}`;

	let innerClasses: string | undefined = innerClassesCache[hash];
	if (innerClasses) {
		return innerClasses;
	}

	innerClasses = classnames(
		// lists in less are 1-indexed based instead of 0 so this is compensatation.
		`column-${(columnForClassList % numGraphColumns) + 1}`,
		'graph-row',
		'height-100-percent',
		'flex',
		type,
		{
			'is-selected': isSelected,
		},
		{
			'is-highlighted': isHighlighted,
		},
		{
			'dimmed-row':
				(isDimmedMergeCommitRow && !isHighlighted) ||
				(isMissingHoveredRefGroup && (highlightRowsOnRefHover || dimRowsOfSelectedCommit)),
		},
	);

	innerClassesCache[hash] = innerClasses;

	return innerClasses;
}

export default class GenericGraphZone extends React.PureComponent<Props> {
	override render(): ReactElement<'div'> {
		const {
			clearCurrentlyHoveredGraphCommit,
			children,
			column,
			columnForColoring,
			context,
			currentlyHoveredCommitSha,
			dimRowsOfSelectedCommit,
			getExternalIcon,
			graphZoneType,
			hasAvatars,
			highlightRowsOnRefHover,
			isDimmedMergeCommitRow,
			isHighlighted,
			isHovering,
			isLastColumn,
			isInUnsupportedRebase,
			isMissingHoveredRefGroup,
			isSelected,
			numGraphColumns,
			onContextMenu,
			onClickCommit,
			onDoubleClickCommit,
			rowContext,
			sha,
			showColorStrip,
			showConflictIcon,
			showTimeline,
			setAsCurrentlyHoveredGraphCommit,
			style,
			title,
			type,
			verticalScrollWidth,
			zoneWidth,
		} = this.props;

		const baseClasses: string = getBaseClasses(type, isHovering);
		const innerClasses: string = getInnerClasses(
			type,
			columnForColoring ?? column,
			isHighlighted,
			isSelected,
			numGraphColumns,
			isMissingHoveredRefGroup,
			highlightRowsOnRefHover,
			dimRowsOfSelectedCommit,
			isDimmedMergeCommitRow,
		);

		const maybeTimeline: ReactElement<typeof Timeline> = showTimeline ? <Timeline /> : null;

		return (
			<div
				className={baseClasses}
				data-vscode-context={parseContext(rowContext)}
				onContextMenu={isInUnsupportedRebase ? undefined : event => onContextMenu(event, graphZoneType, sha)}
				onDoubleClick={event => onDoubleClickCommit(event, graphZoneType, sha)}
				onMouseDown={event => onClickCommit(event, graphZoneType, sha)}
				onMouseEnter={e => setAsCurrentlyHoveredGraphCommit(e, graphZoneType, sha, currentlyHoveredCommitSha)}
				onMouseLeave={event =>
					clearCurrentlyHoveredGraphCommit(event, graphZoneType, sha, currentlyHoveredCommitSha)
				}
				style={style}
			>
				{maybeTimeline}
				<div className={innerClasses} data-vscode-context={parseContext(context)}>
					{showColorStrip ? colorStrip : null}
					{showConflictIcon ? <span className={classnames('ml2')}>{getExternalIcon('warning')}</span> : null}
					<span
						className={classnames('graph-zone', 'width-100', 'flex-1')}
						style={{
							lineHeight: `${GRAPH_ROW_INNER_HEIGHT}px`,
							...(isLastColumn && { width: zoneWidth - verticalScrollWidth }),
							...(hasAvatars && { marginLeft: 5 }),
						}}
						title={title}
					>
						{children}
					</span>
				</div>
			</div>
		);
	}
}
