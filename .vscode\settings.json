{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "files.associations": {".eslintrc*.json": "jsonc"}, "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "[html][javascript][json][jsonc][markdown][scss][svg][typescript][typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "npm.packageManager": "yarn", "search.exclude": {"**/dist": true}, "typescript.preferences.importModuleSpecifier": "project-relative", "typescript.tsdk": "node_modules\\typescript\\lib"}