import { hasPendingChanges } from '../commit/CommitHelpers';
import type { CommitType } from '../commit/CommitTypes';
import type {
	AnySvgProps,
	ColumnColor,
	LineSvgProps,
	RowEdges,
	SvgElement,
	SvgElementName,
	SvgProps,
	SvgStyles,
	XValueByColumnCache,
} from '../graph/GraphTypes';

// NOTE this stuff has to be absolutely as fast as possible. V8 does string concatenation very quickly
// compared to other types of string building.

let xValueAtColumnCache: XValueByColumnCache = {};

export function getXValueAtColumn(
	column: number,
	columnWidth: number,
	gutterWidth: number,
	isCompact?: boolean,
): number {
	const columnHash = `${column}${isCompact ? '_compact' : ''}`;
	let xValueAtColumn: number | undefined = xValueAtColumnCache[column];
	if (xValueAtColumn) {
		return xValueAtColumn;
	}

	xValueAtColumn = gutterWidth + column * columnWidth + columnWidth / 2;
	xValueAtColumnCache[columnHash] = xValueAtColumn;

	return xValueAtColumn;
}

export function clearXValueAtColumnCache(): void {
	xValueAtColumnCache = {};
}

const SVG_ELEMENT_PROPS: { [svgPropKey: string]: keyof AnySvgProps } = {
	d: 'd',
	fill: 'fill',
	'shape-rendering': 'shapeRendering',
	'stroke-linejoin': 'strokeLinejoin',
	'stroke-width': 'strokeWidth',
	'stroke-dasharray': 'strokeDasharray',
	stroke: 'stroke',
	x1: 'x1',
	x2: 'x2',
	y1: 'y1',
	y2: 'y2',
};

export function getSvgElement(element: SvgElementName, props: SvgProps): SvgElement {
	let attrs = '';

	for (const [svgPropKey, objPropKey] of Object.entries(SVG_ELEMENT_PROPS)) {
		const value: string | number | undefined = props[objPropKey as keyof SvgProps];
		if (value) {
			attrs += ` ${svgPropKey}="${value}"`; // e.g. result += ` shape-rendering="auto"`
		}
	}

	return `<${element}${attrs} />`;
}

export function getLineElement(props: LineSvgProps): SvgElement {
	return getSvgElement('line', props);
}

export function getSvgStyles(
	type: CommitType,
	color: ColumnColor,
	strokeWidth: number,
	isCompact?: boolean,
): SvgStyles {
	return {
		fill: 'none',
		shapeRendering: 'auto',
		strokeLinejoin: 'round',
		strokeWidth: strokeWidth,
		strokeDasharray: !hasPendingChanges(type) ? 0 : isCompact ? 1 : 2,
		stroke: color,
	};
}

// NOTE getStartingEdge, getEndingEdge, and getPassThroughEdge uses the following two functions for memoization.
// But those have an additional argument (columnColorByColumn) on the end which is _not_ used for memoization.
// The arguments are deliberately not a destructurable object to maximize speed.
// Adjustment arguments of these two functions must be done in harmony with the aforementioned edges.

export function buildStartingOrEndingEdgeHash(
	edgeColumn: number,
	nodeColumn: number,
	type: CommitType | undefined,
	isCompact?: boolean,
): string {
	return `${edgeColumn}_${nodeColumn}_${type || '+'}${isCompact ? '_compact' : ''}`;
}

export function buildPassThroughEdgeHash(column: number, type: CommitType | undefined, isCompact?: boolean): string {
	return `${column}_${type || '+'}${isCompact ? '_compact' : ''}`;
}

// NOTE `edgeColumnMax` is literally the greatest (number) object key of `edges`.
// Object.keys is expensive, so it's in a memoized selector that we pass all the way down to here.
export function buildEdgeHash(edges: RowEdges, edgeColumnMax: number, nodeColumn: number, isCompact?: boolean): string {
	let hash = '';
	// we explicitly want to minimize the number of iterations in this hashing algorithm. Hashing should be as fast as
	// possible to minimize the time it takes to search the cache, which is why we are using ++ here.
	for (let edgeColumn = 0; edgeColumn <= edgeColumnMax; edgeColumn++) {
		const { passThrough, starting, ending } = edges[edgeColumn] ?? {};

		hash = `${hash}_${
			// starting edge
			buildStartingOrEndingEdgeHash(edgeColumn, nodeColumn, starting?.type, isCompact)
		}_${
			// ending edge
			buildStartingOrEndingEdgeHash(edgeColumn, nodeColumn, ending?.type, isCompact)
		}_${
			// pass-through edge
			buildPassThroughEdgeHash(edgeColumn, passThrough?.type, isCompact)
		}`;
	}

	return hash;
}
