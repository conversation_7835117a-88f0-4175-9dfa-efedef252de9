import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import { FormControl } from 'react-bootstrap';
import { workDirType } from '../../../domain/commit/CommitConstants';
import type { Sha } from '../../../domain/commit/CommitTypes';
import type { Style } from '../../../domain/generic/GenericTypes';
import type { GraphZoneType } from '../../../domain/graph/GraphConstants';
import type {
	GetExternalIcon,
	OnClearCurrentlyHoveredGraphCommit,
	OnClickCommit,
	OnCommitContextMenu,
	OnCommitMessageChange,
	OnCurrentlyHoveredGraphCommit,
	OnWipMessageBlur,
	OnWipMessageFocus,
} from '../../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';
import type { WorkDirStats } from '../../../domain/workdir/WorkDirTypes';
import InlineWorkDirSummary from '../shared/InlineWorkDirSummary';

type WorkDirMessageInputDefaultProps = {
	style: Style;
	value: string;
};

type WorkDirMessageInputProps = {
	clearCurrentlyHoveredGraphCommit: OnClearCurrentlyHoveredGraphCommit;
	currentlyHoveredCommitSha: Sha | null;
	getExternalIcon: GetExternalIcon;
	graphZoneType: GraphZoneType;
	isCommitting: boolean;
	isHovering: boolean;
	isSelected: boolean;
	onBlur: OnWipMessageBlur;
	onClickCommit: OnClickCommit;
	onContextMenu: OnCommitContextMenu;
	onFocus: OnWipMessageFocus;
	onMessageChange: OnCommitMessageChange;
	setAsCurrentlyHoveredGraphCommit: OnCurrentlyHoveredGraphCommit;
	sha: Sha;
	style: Style;
	translate: TranslationFn;
	value: string;
	workDirStats: WorkDirStats;
};

export default class WorkDirMessageInput extends React.Component<WorkDirMessageInputProps> {
	textWidthRef: React.RefObject<any> = React.createRef();
	wrapperRef: React.RefObject<any> = React.createRef();

	static defaultProps: WorkDirMessageInputDefaultProps = {
		style: {},
		value: '',
	};

	override componentDidMount(): void {
		if (this.wrapperRef?.current) {
			this.wrapperRef.current.style.width = '0px';
		}
	}

	override componentDidUpdate(): void {
		if (this.textWidthRef?.current && this.wrapperRef?.current) {
			this.wrapperRef.current.style.width = `${Number(this.textWidthRef.current.offsetWidth) + 35}px`;
		}
	}

	override componentWillUnmount(): void {
		if (this.props.onBlur) {
			this.props.onBlur();
		}
	}

	onWipKeyDown(event: Event): void {
		event.stopPropagation();
	}

	override render(): ReactElement<'div'> {
		const {
			clearCurrentlyHoveredGraphCommit,
			currentlyHoveredCommitSha,
			getExternalIcon,
			graphZoneType,
			isCommitting,
			isHovering,
			isSelected,
			onBlur,
			onClickCommit,
			onContextMenu,
			onFocus,
			onMessageChange,
			setAsCurrentlyHoveredGraphCommit,
			sha,
			style,
			translate,
			value,
			workDirStats,
		} = this.props;

		const wrapperClasses: string = classnames(
			'graph-zone-column',
			'pt3',
			'pb3',
			'pointer',
			'height-100-percent',
			'grow-3',
			'graph-row-wrapper',
			workDirType,
			{
				'is-hovering': isHovering,
			},
		);

		const innerClasses: string = classnames('column-1', 'graph-row', 'flex', {
			'is-selected': isSelected,
		});

		const summary: ReactElement<typeof InlineWorkDirSummary> = (
			<InlineWorkDirSummary diffStats={workDirStats} getExternalIcon={getExternalIcon} translate={translate} />
		);

		const wipMessageInput: ReactElement<'div'> = (
			<div className="work-dir-input" ref={this.wrapperRef}>
				<FormControl
					data-test-class="work-dir-input-field"
					disabled={isCommitting}
					onBlur={(e: any) => onBlur(e)}
					onChange={(e: any) => onMessageChange(e.target.value)}
					onContextMenu={e => onContextMenu(e, graphZoneType, sha)}
					onFocus={(e: any) => onFocus(e)}
					onKeyDown={(e: any) => this.onWipKeyDown(e)}
					placeholder={translate('WorkDirMessageInput-WIPPlaceholder')}
					type="text"
					value={value}
				/>
				<div className="text-width" data-test-class="work-dir-input-message" ref={this.textWidthRef}>
					{value}
				</div>
			</div>
		);

		return (
			<div
				className={wrapperClasses}
				onMouseEnter={e => setAsCurrentlyHoveredGraphCommit(e, graphZoneType, sha, currentlyHoveredCommitSha)}
				onMouseLeave={e => clearCurrentlyHoveredGraphCommit(e, graphZoneType, sha, currentlyHoveredCommitSha)}
				style={style}
			>
				<div
					className={innerClasses}
					data-test-class="work-dir-message-row-div"
					onMouseDown={e => onClickCommit(e, graphZoneType, sha)}
				>
					{wipMessageInput}
					{summary}
				</div>
			</div>
		);
	}
}
