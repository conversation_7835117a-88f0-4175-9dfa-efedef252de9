import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import type { GraphZoneModeConstants } from '../../../domain/graph/GraphTypes';

type BackgroundStreakProps = {
	className?: string;
	marginLeft: number;
};

function BackgroundStreak({ className, marginLeft }: BackgroundStreakProps): ReactElement<'div'> {
	return (
		<div
			className={classnames(
				'app-bg0',
				'absolute',
				'left-0',
				'right-0',
				'bottom-0',
				'top-0',
				'pt3',
				'pb3',
				className,
			)}
			style={{ marginLeft: marginLeft }}
		>
			<div className="commit-bg-color height-100-percent width-100-percent" />
		</div>
	);
}

export default BackgroundStreak;

const getBackgroundOffsetFromColumn = (
	column: number,
	columnWidth: number,
	gutterWidth: number,
	nodeDiameter: number,
): number => gutterWidth + columnWidth * column + nodeDiameter / 2;

export type CommitZoneBackgroundStreakProps = {
	column: number;
	graphZoneModeConstants: GraphZoneModeConstants;
};
export function CommitZoneBackgroundStreak({
	column,
	graphZoneModeConstants: { COMMIT_COLUMN_WIDTH, COMMIT_NODE_DIAMETER, COMMIT_ZONE_GUTTER_WIDTH },
}: CommitZoneBackgroundStreakProps): ReactElement<typeof BackgroundStreak> {
	return (
		<BackgroundStreak
			className="z1"
			marginLeft={getBackgroundOffsetFromColumn(
				column,
				COMMIT_COLUMN_WIDTH,
				COMMIT_ZONE_GUTTER_WIDTH,
				COMMIT_NODE_DIAMETER,
			)}
		/>
	);
}

export function GutterBackgroundStreak(): ReactElement<typeof BackgroundStreak> {
	return <BackgroundStreak marginLeft={0} />;
}
