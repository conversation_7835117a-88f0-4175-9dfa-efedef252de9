// @ts-check
import globals from 'globals';
import { includeIgnoreFile } from '@eslint/compat';
import js from '@eslint/js';
import ts from 'typescript-eslint';
import antiTrojanSource from 'eslint-plugin-anti-trojan-source';
import react from 'eslint-plugin-react';
import reactHooks from 'eslint-plugin-react-hooks';
import jasmine from 'eslint-plugin-jasmine';
import jsxA11y from 'eslint-plugin-jsx-a11y';
import { createTypeScriptImportResolver } from 'eslint-import-resolver-typescript';
import importX from 'eslint-plugin-import-x';
import { fileURLToPath } from 'node:url';

const gitignorePath = fileURLToPath(new URL('./.gitignore', import.meta.url));

export default ts.config(
	includeIgnoreFile(gitignorePath),
	{ ignores: ['*.*', 'scripts', 'test'] },
	js.configs.recommended,
	...ts.configs.strictTypeChecked,
	jsxA11y.flatConfigs.recommended,
	// @ts-ignore
	jasmine.configs.recommended,
	react.configs.flat.recommended,
	{
		...reactHooks.configs.recommended,
		languageOptions: {
			globals: { ...globals.browser, ...globals.jasmine },
			parser: ts.parser,
			parserOptions: {
				ecmaVersion: 2023,
				sourceType: 'module',
				ecmaFeatures: { impliedStrict: true, jsx: true },
				projectService: true,
			},
		},
		linterOptions: { reportUnusedDisableDirectives: true },
		plugins: {
			'import-x': importX,
			'anti-trojan-source': antiTrojanSource,
			jasmine: jasmine,
			react: react,
			'react-hooks': reactHooks,
		},
		rules: {
			'anti-trojan-source/no-bidi': 'error',
			'import-x/consistent-type-specifier-style': ['error', 'prefer-top-level'],
			'import-x/default': 'off',
			'import-x/extensions': 'off',
			'import-x/named': 'off',
			'import-x/namespace': 'off',
			'import-x/newline-after-import': 'warn',
			'import-x/no-absolute-path': 'error',
			'import-x/no-cycle': 'off',
			'import-x/no-deprecated': 'off',
			'import-x/no-default-export': 'off', // Turn off for now, but we should avoid default exports
			'import-x/no-duplicates': ['error', { 'prefer-inline': false }],
			'import-x/no-dynamic-require': 'error',
			'import-x/no-named-as-default': 'off',
			'import-x/no-named-as-default-member': 'off',
			'import-x/no-self-import': 'error',
			'import-x/no-unused-modules': 'off',
			'import-x/no-unresolved': 'off',
			'import-x/no-useless-path-segments': 'error',
			'import-x/order': [
				'warn',
				{
					alphabetize: { order: 'asc', orderImportKind: 'asc', caseInsensitive: true },
					groups: ['builtin', 'external', 'internal', 'parent', 'sibling', 'index'],
					'newlines-between': 'never',
				},
			],
			'jasmine/no-focused-tests': 'error',
			'jsx-a11y/alt-text': 'off',
			'jsx-a11y/anchor-is-valid': 'off',
			'jsx-a11y/click-events-have-key-events': 'off',
			'jsx-a11y/img-has-alt': 'off',
			'jsx-a11y/label-has-associated-control': 'off',
			'jsx-a11y/label-has-for': 'off',
			'jsx-a11y/no-autofocus': 'off',
			'jsx-a11y/no-noninteractive-element-interactions': 'off',
			'jsx-a11y/no-noninteractive-tabindex': 'off',
			'jsx-a11y/no-static-element-interactions': 'off',
			'jsx-quotes': ['error', 'prefer-double'],
			'no-constant-condition': ['warn', { checkLoops: false }],
			'no-constant-binary-expression': 'error',
			'no-caller': 'error',
			'no-debugger': 'off',
			'no-else-return': 'warn',
			'no-empty': ['warn', { allowEmptyCatch: true }],
			'no-eval': 'error',
			'no-ex-assign': 'warn',
			'no-extend-native': 'error',
			'no-extra-bind': 'error',
			'no-extra-semi': 'off',
			'no-floating-decimal': 'error',
			'no-implicit-coercion': 'error',
			'no-implied-eval': 'error',
			'no-inner-declarations': 'off',
			'no-lone-blocks': 'error',
			'no-lonely-if': 'error',
			'no-loop-func': 'error',
			'no-mixed-spaces-and-tabs': 'off',
			'no-return-assign': 'error',
			'no-return-await': 'warn',
			'no-self-compare': 'error',
			'no-sequences': 'error',
			'no-template-curly-in-string': 'warn',
			'no-throw-literal': 'error',
			// Fixes eslint error 'NodeJS' is not defined no-undef. See: https://github.com/Chatie/eslint-config/issues/45#issuecomment-1003990077
			'no-undef': 'off',
			'no-unmodified-loop-condition': 'warn',
			'no-unneeded-ternary': 'error',
			'no-unused-expressions': 'error',
			'no-use-before-define': 'off',
			'no-useless-call': 'error',
			'no-useless-catch': 'error',
			'no-useless-computed-key': 'error',
			'no-useless-concat': 'error',
			'no-useless-rename': 'error',
			'no-useless-return': 'error',
			'no-var': 'error',
			'no-with': 'error',
			'object-shorthand': ['error', 'never'],
			'one-var': ['error', 'never'],
			'prefer-arrow-callback': 'error',
			'prefer-const': ['error', { destructuring: 'all', ignoreReadBeforeAssign: true }],
			'prefer-destructuring': 'off',
			'prefer-numeric-literals': 'error',
			'prefer-object-spread': 'error',
			'prefer-promise-reject-errors': 'error',
			'prefer-rest-params': 'error',
			'prefer-spread': 'error',
			'prefer-template': 'error',
			'react/button-has-type': 'off',
			'react/default-props-match-prop-types': 'off',
			'react/destructuring-assignment': 'off',
			'react/display-name': 'error',
			'reactforbid-prop-types': 'off',
			'react/jsx-filename-extension': [1, { extensions: ['.tsx'] }],
			'react/jsx-closing-bracket-location': 'off',
			'react/jsx-closing-tag-location': 'off',
			'react/jsx-curly-spacing': 'off',
			'react/jsx-equals-spacing': 'off',
			'react/jsx-first-prop-new-line': 'off',
			'react/jsx-indent-props': 'off',
			'react/jsx-max-props-per-line': 'off',
			'react/jsx-no-bind': 'off',
			'react/jsx-no-undef': 'error',
			'react/jsx-one-expression-per-line': 'off',
			'react/jsx-quotes': 'off',
			'react/jsx-sort-props': [
				'error',
				{
					callbacksLast: false,
					shorthandFirst: false,
					shorthandLast: false,
					ignoreCase: true,
					noSortAlphabetically: false,
					reservedFirst: false,
				},
			],
			'react/jsx-space-before-closing': 'off',
			'react/jsx-tag-spacing': [
				'error',
				{ closingSlash: 'never', beforeSelfClosing: 'always', afterOpening: 'never', beforeClosing: 'never' },
			],
			'react/no-is-mounted': 'off',
			'react/no-did-mount-set-state': 'error',
			'react/no-unused-prop-types': 'off',
			'react/prefer-es6-class': 'off',
			'react/prefer-stateless-function': 'off',
			'react/prop-types': 'off',
			'react/require-default-props': 'off',
			'react/sort-prop-types': 'error',
			'require-atomic-updates': 'off',
			'sort-imports': [
				'error',
				{
					ignoreCase: true,
					ignoreDeclarationSort: true,
					ignoreMemberSort: false,
					memberSyntaxSortOrder: ['none', 'all', 'multiple', 'single'],
				},
			],
			yoda: 'error',
			'@typescript-eslint/ban-types': 'off',
			'@typescript-eslint/consistent-type-assertions': [
				'error',
				{ assertionStyle: 'as', objectLiteralTypeAssertions: 'allow-as-parameter' },
			],
			'@typescript-eslint/consistent-type-imports': ['error', { disallowTypeAnnotations: false }],
			'@typescript-eslint/no-confusing-void-expression': [
				'error',
				{ ignoreArrowShorthand: true, ignoreVoidOperator: true },
			],
			'@typescript-eslint/no-explicit-any': 'off',
			'@typescript-eslint/no-inferrable-types': ['warn', { ignoreParameters: true, ignoreProperties: true }],
			'@typescript-eslint/no-invalid-void-type': 'off', // Seems to error on `void` return types
			'@typescript-eslint/no-misused-promises': ['error', { checksVoidReturn: false }],
			'@typescript-eslint/no-non-null-assertion': 'off',
			'@typescript-eslint/no-redundant-type-constituents': 'off',
			'@typescript-eslint/no-unnecessary-condition': 'off',
			'@typescript-eslint/no-unnecessary-boolean-literal-compare': 'off',
			'@typescript-eslint/no-unnecessary-type-arguments': 'off', // Should really turn this back on
			'@typescript-eslint/no-unsafe-argument': 'off',
			'@typescript-eslint/no-unsafe-assignment': 'off',
			'@typescript-eslint/no-unsafe-call': 'off',
			'@typescript-eslint/no-unsafe-enum-comparison': 'off',
			'@typescript-eslint/no-unsafe-member-access': 'off',
			// "@typescript-eslint/no-unsafe-return": "error",
			'@typescript-eslint/no-unused-expressions': ['warn', { allowShortCircuit: true }],
			// "@typescript-eslint/no-unused-vars": [
			// 	"warn",
			// 	{
			// 		"args": "after-used",
			// 		"argsIgnorePattern": "^_",
			// 		"ignoreRestSiblings": true,
			// 		"varsIgnorePattern": "^_$"
			// 	}
			// ],
			'@typescript-eslint/no-use-before-define': ['error', { functions: false, classes: false }],
			'@typescript-eslint/prefer-for-of': 'warn',
			'@typescript-eslint/prefer-includes': 'warn',
			'@typescript-eslint/prefer-literal-enum-member': ['warn', { allowBitwiseExpressions: true }],
			'@typescript-eslint/prefer-optional-chain': 'warn',
			'@typescript-eslint/prefer-promise-reject-errors': ['error', { allowEmptyReject: true }],
			'@typescript-eslint/prefer-reduce-type-parameter': 'warn',
			'@typescript-eslint/restrict-plus-operands': 'error',
			'@typescript-eslint/restrict-template-expressions': 'off',
			// "@typescript-eslint/unbound-method": "off", // Too many bugs right now: https://github.com/typescript-eslint/typescript-eslint/issues?utf8=%E2%9C%93&q=is%3Aissue+is%3Aopen+unbound-method
			'@typescript-eslint/unified-signatures': ['error', { ignoreDifferentlyNamedParameters: true }],
		},
		settings: {
			'import-x/core-modules': ['react-virtualized'],
			'import-x/extensions': ['.ts', '.tsx'],
			'import-x/parsers': { '@typescript-eslint/parser': ['.ts', '.tsx'] },
			'import-x/resolver-next': [createTypeScriptImportResolver()],
			react: { version: 'detect' },
		},
	},
);
