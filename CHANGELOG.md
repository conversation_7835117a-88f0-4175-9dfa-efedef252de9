# CHANGELOG

## 11.0.2
 - Fixed an issue with ref labels sometimes not rendering properly when hovered/unhovered. ([#367](https://github.com/gitkraken/GitKrakenComponents/pull/367))
 - Added some data-test-classes for automated UI tests to find DOM nodes. ([#369](https://github.com/gitkraken/GitKrakenComponents/pull/369))

## 11.0.1
 - Fixed tooltip css so that tooltips on the commit notes and ref label icons are no longer 0 opacity. (CSS classes were modified by previous `react-bootstrap` bump.) ([#366](https://github.com/gitkraken/GitKrakenComponents/pull/366))

## 11.0.0
 - **BREAKING CHANGE:** Upgraded from `react`/`react-dom` 16 to 17. ([#365](https://github.com/gitkraken/GitKrakenComponents/pull/365))
 - **BREAKING CHANGE:** Upgraded `react-bootstrap` from 0.32.4 to 2.10.7 ([#365](https://github.com/gitkraken/GitKrakenComponents/pull/365))
   - The full consequences of this on your project's CSS cannot be fully predicted, so you should test thoroughly. Known concerns which affected at least one project are listed below.
   - The DIV wrapping the ref-creation text input no longer receives the `has-error` class, and the input no longer receives the `is-valid` class. Instead, the input receives the `is-invalid` class.
 - Added `AzureDevopsIssueTrackerType` (in addition to the existing `AzureDevopsIssueTrackerType`) ([262cbf8e](https://github.com/gitkraken/GitKrakenComponents/commit/262cbf8e81c8497676e93fde0d32fdf537fec706))
