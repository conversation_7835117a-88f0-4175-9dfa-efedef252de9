import { expect } from 'chai';

import * as <PERSON><PERSON><PERSON><PERSON> from '../../../../src/domain/date/DateHelper';

describe('Date Helper', function () {
	describe('formatDate', function () {
		const testCases = [
			{ date: 1668627885000, format: 'full', locale: 'EN', cache: true, result: 'Wednesday, November 16, 2022' },
			{ date: 1668627885000, format: 'long', locale: 'EN', cache: true, result: 'November 16, 2022' },
			{ date: 1668627885000, format: 'medium', locale: 'EN', cache: true, result: 'Nov 16, 2022' },
			{ date: 1668627885000, format: 'short', locale: 'EN', cache: true, result: '11/16/22' },
			{
				date: 1668627885000,
				format: 'full',
				locale: 'ES',
				cache: true,
				result: 'miércoles, 16 de noviembre de 2022',
			},
			{ date: 1668627885000, format: 'full', locale: 'EN', cache: false, result: 'Wednesday, November 16, 2022' },
			{ date: 1668627885000, format: 'long', locale: 'EN', cache: false, result: 'November 16, 2022' },
			{ date: 1668627885000, format: 'medium', locale: 'EN', cache: false, result: 'Nov 16, 2022' },
			{ date: 1668627885000, format: 'short', locale: 'EN', cache: false, result: '11/16/22' },
			{
				date: 1668627885000,
				format: 'full',
				locale: 'ES',
				cache: false,
				result: 'miércoles, 16 de noviembre de 2022',
			},
		];

		testCases.forEach(testCase => {
			const { format, date, locale, cache, result } = testCase;
			const input = `date ${date}, format '${format}' and cache ${cache.toString()}`;

			it(`For ${input}; it should return '${result}'`, function () {
				expect(DateHelper.formatDate(date, format, locale, cache)).to.equal(result);
			});
		});
	});

	describe('getDateTimeFormatOptionsFromFormatString', function () {
		const testCases = [
			{ result: { localeMatcher: 'best fit', dateStyle: 'full', timeStyle: 'short' } },
			{ input: 'd', result: { localeMatcher: 'best fit' } },
			{ input: 'dd', result: { localeMatcher: 'best fit', weekday: 'narrow' } },
			{ input: 'ddd', result: { localeMatcher: 'best fit', weekday: 'short' } },
			{ input: 'dddd', result: { localeMatcher: 'best fit', weekday: 'long' } },
			{ input: 'D', result: { localeMatcher: 'best fit', day: 'numeric' } },
			{ input: 'DD', result: { localeMatcher: 'best fit', day: '2-digit' } },
			{ input: 'DDD', result: { localeMatcher: 'best fit', day: 'numeric' } },
			{ input: 'DDDD', result: { localeMatcher: 'best fit', day: '2-digit' } },
			{ input: 'M', result: { localeMatcher: 'best fit', month: 'numeric' } },
			{ input: 'MM', result: { localeMatcher: 'best fit', month: '2-digit' } },
			{ input: 'MMM', result: { localeMatcher: 'best fit', month: 'short' } },
			{ input: 'MMMM', result: { localeMatcher: 'best fit', month: 'long' } },
			{ input: 'h', result: { localeMatcher: 'best fit', hour: 'numeric', hour12: true } },
			{ input: 'hh', result: { localeMatcher: 'best fit', hour: '2-digit', hour12: true } },
			{ input: 'hhh', result: { localeMatcher: 'best fit', hour: 'numeric', hour12: true } },
			{ input: 'hhhh', result: { localeMatcher: 'best fit', hour: '2-digit', hour12: true } },
			{ input: 'H', result: { localeMatcher: 'best fit', hour: 'numeric', hour12: false } },
			{ input: 'HH', result: { localeMatcher: 'best fit', hour: '2-digit', hour12: false } },
			{ input: 'HHH', result: { localeMatcher: 'best fit', hour: 'numeric', hour12: false } },
			{ input: 'HHHH', result: { localeMatcher: 'best fit', hour: '2-digit', hour12: false } },
			{ input: 'm', result: { localeMatcher: 'best fit', minute: 'numeric' } },
			{ input: 'mm', result: { localeMatcher: 'best fit', minute: '2-digit' } },
			{ input: 'mmm', result: { localeMatcher: 'best fit', minute: 'numeric' } },
			{ input: 'mmmm', result: { localeMatcher: 'best fit', minute: '2-digit' } },
			{ input: 's', result: { localeMatcher: 'best fit', second: 'numeric' } },
			{ input: 'ss', result: { localeMatcher: 'best fit', second: '2-digit' } },
			{ input: 'sss', result: { localeMatcher: 'best fit', second: 'numeric' } },
			{ input: 'ssss', result: { localeMatcher: 'best fit', second: '2-digit' } },
			{ input: 'YY', result: { localeMatcher: 'best fit', year: '2-digit' } },
			{ input: 'YYY', result: { localeMatcher: 'best fit', year: '2-digit' } },
			{ input: 'YYYY', result: { localeMatcher: 'best fit', year: 'numeric' } },
			{ input: 'YYYY', result: { localeMatcher: 'best fit', year: 'numeric' } },
			{ input: 'A', result: { localeMatcher: 'best fit', dayPeriod: 'narrow', hour12: true, hourCycle: 'h12' } },
			{
				input: 'MM/DD/YYYY hh:mm:ss',
				result: {
					localeMatcher: 'best fit',
					month: '2-digit',
					day: '2-digit',
					year: 'numeric',
					hour: '2-digit',
					hour12: true,
					minute: '2-digit',
					second: '2-digit',
				},
			},
		];

		testCases.forEach(testCase => {
			const input = testCase.input || 'undefined';
			it(`For input '${input}', it should return '${JSON.stringify(testCase.result)}'`, function () {
				expect(DateHelper.getDateTimeFormatOptionsFromFormatString(testCase.input)).to.eql(testCase.result);
			});
		});
	});

	describe('formatWithOrdinal', function () {
		const testCases = [
			{ number: 1, result: '1st' },
			{ number: 10, result: '10th' },
			{ number: 155, result: '155th' },
			{ number: 2, result: '2nd' },
			{ number: 20, result: '20th' },
			{ number: 255, result: '255th' },
			{ number: 3, result: '3rd' },
			{ number: 30, result: '30th' },
			{ number: 355, result: '355th' },
			{ number: 4, result: '4th' },
			{ number: 40, result: '40th' },
			{ number: 455, result: '455th' },
		];

		testCases.forEach(testCase => {
			it(`For number ${testCase.number}, it should return '${testCase.result}'`, function () {
				expect(DateHelper.formatWithOrdinal(testCase.number)).to.equal(testCase.result);
			});
		});
	});
});
