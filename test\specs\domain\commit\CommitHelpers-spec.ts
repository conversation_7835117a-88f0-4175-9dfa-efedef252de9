import { expect } from 'chai';
import { CommitType } from '../../../../src/domain/commit/CommitTypes';

import * as CommitConstants from '../../../../src/domain/commit/CommitConstants';
import * as CommitHelpers from '../../../../src/domain/commit/CommitHelpers';

function sanitizeTxt(txt: string): string {
	return txt.replace(/\r/g, '\\r').replace(/\n/g, '\\n');
}

describe('Commit Helpers', function () {
	describe('hasPendingChanges', function () {
		const testCases = [
			{ commitNodeType: CommitConstants.commitNodeType, result: false },
			{ commitNodeType: CommitConstants.mergeNodeType, result: false },
			{ commitNodeType: CommitConstants.mergeConflictNodeType, result: true },
			{ commitNodeType: CommitConstants.unsupportedRebaseWarningNodeType, result: true },
			{ commitNodeType: CommitConstants.stashNodeType, result: true },
			{ commitNodeType: CommitConstants.workDirType, result: true },
		];

		testCases.forEach(testCase => {
			it(`Using commit type "${
				testCase.commitNodeType
			}" should return ${testCase.result.toString()}.`, function () {
				expect(CommitHelpers.hasPendingChanges(testCase.commitNodeType as CommitType)).to.equal(
					testCase.result,
				);
			});
		});
	});

	describe('getCommitMessageDescription', function () {
		const testCases = [
			{ msg: '', lineEnding: undefined, result: '' },
			{ msg: 'summary', lineEnding: undefined, result: '' },
			{ msg: 'summary\n', lineEnding: undefined, result: '' },
			{ msg: 'summary\ndescription', lineEnding: undefined, result: 'description' },
			{ msg: 'summary\n\ndescription', lineEnding: undefined, result: 'description' },
			{ msg: 'summary\n\n\ndescription', lineEnding: undefined, result: '\ndescription' },
			{ msg: '\ndescription', lineEnding: undefined, result: 'description' },
			{ msg: '', lineEnding: '\n', result: '' },
			{ msg: 'summary', lineEnding: '\n', result: '' },
			{ msg: 'summary\n', lineEnding: '\n', result: '' },
			{ msg: 'summary\ndescription', lineEnding: '\n', result: 'description' },
			{ msg: 'summary\n\ndescription', lineEnding: '\n', result: 'description' },
			{ msg: 'summary\n\n\ndescription', lineEnding: '\n', result: '\ndescription' },
			{ msg: '\ndescription', lineEnding: '\n', result: 'description' },
		];

		testCases.forEach(testCase => {
			const msg = sanitizeTxt(testCase.msg);
			const lineEnding = sanitizeTxt(testCase.lineEnding || 'undefined');
			const res = sanitizeTxt(testCase.result);

			it(`Commit message "${msg}" and line ending "${lineEnding}" should return "${res}".`, function () {
				expect(CommitHelpers.getCommitMessageDescription(testCase.msg, testCase.lineEnding)).to.equal(
					testCase.result,
				);
			});
		});
	});

	describe('getCommitMessageCoauthors', function () {
		const testCases = [
			{
				msg: 'Test\n',
				result: [],
			},
			{
				msg: 'Co-authored-by: John Smith <<EMAIL>>\n',
				result: [{ name: 'John Smith', email: '<EMAIL>' }],
			},
		];

		testCases.forEach(testCase => {
			const sanitizedResult = JSON.stringify(testCase.result);

			it(`Commit message "${sanitizeTxt(testCase.msg)}" should return ${sanitizedResult}.`, function () {
				expect(CommitHelpers.getCommitMessageCoauthors(testCase.msg)).to.eql(testCase.result);
			});
		});
	});
});
