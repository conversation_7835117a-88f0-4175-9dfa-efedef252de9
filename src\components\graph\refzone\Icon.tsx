import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import { OverlayTrigger, Tooltip } from 'react-bootstrap';
import type { GetExternalIcon, GraphItemContext } from '../../../domain/graph/GraphTypes';
import type { HostingServiceType, IssueTrackerType } from '../../../domain/hostingservice/HostingServiceTypes';
import Avatar from '../shared/Avatar';

type IconProps = {
	icon: ReactElement<any>;
	tooltipClassName?: string;
	tooltipId?: string;
	tooltipText?: string;
};

type IssueProps = {
	id: string;
	issueTrackerType: IssueTrackerType;
	toolTipText: string;
	getExternalIcon: GetExternalIcon;
};

type RefIconProps = {
	avatarClassName?: string;
	avatarUrl?: string;
	context: GraphItemContext | null;
	icon: ReactElement<any>;
	tooltipText?: string;
};

type PullRequestProps = {
	icon: ReactElement<any>;
	hostingServiceType: HostingServiceType;
	id: string | number;
	toolTipText?: string;
};

type UpStreamIndicatorProps = {
	icon: ReactElement<any>;
	type: string;
	upstream: string;
	tooltipText?: string;
};

// For some reason, <Avatar> has to be in a containing component for the tooltip to show properly
const getRefIcon = (
	avatarUrl: string | null | undefined,
	avatarClassName: string | null | undefined,
	icon: ReactElement<any>,
	context: GraphItemContext | null,
): ReactElement<any> => (
	<span className="ref-avatar">
		<Avatar
			avatarClassName={avatarClassName}
			context={context}
			enableTransparentBackground
			size={14}
			url={avatarUrl}
		>
			{icon}
		</Avatar>
	</span>
);

export function Icon({ icon, tooltipClassName = '', tooltipId, tooltipText }: IconProps): ReactElement<any> {
	return tooltipText && tooltipId ? (
		<OverlayTrigger
			delay={400}
			overlay={
				<Tooltip className={classnames('gk-graph', 'bs-tooltip', tooltipClassName)} id={tooltipId}>
					{tooltipText}
				</Tooltip>
			}
			placement="top"
			trigger={['hover', 'focus']}
		>
			{icon || <span />}
		</OverlayTrigger>
	) : (
		<> {icon || <span />} </>
	);
}

export function IssueIcon({
	id,
	toolTipText,
	getExternalIcon,
	issueTrackerType,
}: IssueProps): ReactElement<typeof Icon> {
	return (
		<Icon
			icon={getExternalIcon(`issue-${issueTrackerType}`)}
			tooltipClassName="wide pull-left"
			tooltipId={`issue-tooltip-${id}`}
			tooltipText={toolTipText ? toolTipText.trim() : ''}
		/>
	);
}

export function PullRequestIcon({
	hostingServiceType,
	icon,
	id,
	toolTipText,
}: PullRequestProps): ReactElement<typeof Icon> {
	return (
		<Icon
			icon={icon}
			tooltipClassName="wide pull-left"
			tooltipId={`pr-tooltip-${hostingServiceType}-${id}`}
			tooltipText={toolTipText ? toolTipText.trim() : ''}
		/>
	);
}

export function UpstreamIndicatorIcon({
	icon,
	type,
	upstream,
	tooltipText,
}: UpStreamIndicatorProps): ReactElement<typeof Icon> {
	return (
		<Icon
			icon={icon}
			tooltipClassName="wide pull-left"
			tooltipId={`upstream-indicator-tooltip-${type}-${upstream}`}
			tooltipText={tooltipText}
		/>
	);
}

export function RefIcon({
	avatarClassName,
	avatarUrl,
	context,
	icon,
	tooltipText,
}: RefIconProps): ReactElement<typeof Icon> {
	return (
		<Icon
			icon={getRefIcon(avatarUrl, avatarClassName, icon, context)}
			tooltipId={`remote-icon-${tooltipText || 'tooltipText'}`}
			tooltipText={tooltipText}
		/>
	);
}
