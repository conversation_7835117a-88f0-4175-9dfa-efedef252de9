import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import type { CommitType, Sha } from '../../../domain/commit/CommitTypes';
import { GRAPH_ROW_INNER_HEIGHT } from '../../../domain/graph/GraphConstants';
import { getOrAskForRefMetadata, parseContext } from '../../../domain/graph/GraphHelpers';
import type {
	BaseMetadata,
	ExternalIconKeys,
	GetExternalIcon,
	GetMissingRefMetadata,
	GraphItemContext,
	GraphRef,
	GraphRefGroup,
	IssueMetadata,
	OnClickRef,
	OnClickRefMetadata,
	OnDoubleClickRef,
	OnDoubleClickRefMetadata,
	OnHideRefClick,
	OnRefBeginDrag,
	OnRefCanDrag,
	OnRefCanDrop,
	OnRefDragEnter,
	OnRefDragLeave,
	OnRefDrop,
	OnRefEndDrag,
	OnRefNodeHovered,
	OnRefZoneContextMenu,
	PullRequestMetadata,
	RefDndData,
	RefMetadata,
	RefMetadataById,
	RefMetadataItem,
	RefMetadataType,
	Remote,
	UpstreamMetadata,
} from '../../../domain/graph/GraphTypes';
import { issueMetadataType, pullRequestMetadataType, upstreamMetadataType } from '../../../domain/graph/GraphTypes';
import { getHostingServiceName } from '../../../domain/hostingservice/HostingServiceTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';
import { refIconsPositions, refTypes } from '../../../domain/ref/RefConstants';
import type { RefIconsPosition } from '../../../domain/ref/RefTypes';
import type { OnClick, OnContextMenu, OnDoubleClick } from '../../../domain/ui/UiTypes';
import DndComponent from '../../dnd/DndComponent';
import { Icon, IssueIcon, PullRequestIcon, RefIcon, UpstreamIndicatorIcon } from './Icon';
import RefName from './RefName';

export type RefNodeProps = {
	enableShowHideRefsOptions: boolean;
	getExternalIcon: GetExternalIcon;
	groupIsHovered: boolean;
	hasActive: boolean;
	hasRefs: boolean;
	isActive: boolean;
	isGhostRef: boolean;
	isInUnsupportedRebase: boolean;
	isRefGroupIncluded: boolean;
	key?: string;
	onClick: OnClickRef;
	onContextMenu: OnRefZoneContextMenu;
	onDoubleClick: OnDoubleClickRef;
	onRefCanDrag: OnRefCanDrag;
	onRefCanDrop: OnRefCanDrop;
	onRefDragEnter: OnRefDragEnter;
	onRefDragLeave: OnRefDragLeave;
	onRefDrop: OnRefDrop;
	onRefBeginDrag: OnRefBeginDrag;
	onRefEndDrag: OnRefEndDrag;
	onHovered: OnRefNodeHovered;
	onMissingRefMetadata: GetMissingRefMetadata;
	onHideRefClick: OnHideRefClick;
	refGroup: GraphRefGroup;
	refGroupContext: GraphItemContext | null;
	refIconsPosition: RefIconsPosition;
	refMetadata: RefMetadataById | null;
	sha: Sha;
	showRemoteNamesOnRefs: boolean;
	enabledRefMetadataTypes: RefMetadataType[];
	translate: TranslationFn;
	type: CommitType;
	useColumnHeaderIcons: boolean;
};

function getRefRemoteIconKey(ref: Remote): ExternalIconKeys {
	return ref.hostingServiceType ? `${refTypes.REMOTE}-${ref.hostingServiceType}` : refTypes.REMOTE;
}

export function getRefIconAndTooltipForRef(
	getExternalIcon: GetExternalIcon,
	ref: GraphRef,
	translate: TranslationFn,
	index = 0,
): ReactElement<any> {
	const { annotated, refType, message, name } = ref;

	let avatarUrl: string | undefined;
	let iconKey: ExternalIconKeys;
	let tooltipText: string | undefined;

	switch (refType) {
		case refTypes.REMOTE: {
			const remote: Remote = ref as Remote;
			iconKey = getRefRemoteIconKey(remote);
			avatarUrl = remote.avatarUrl;
			const baseToolTipText = `${name} (${translate('Ref-Remote')})`;
			tooltipText = remote.owner ? `${remote.owner} -> ${baseToolTipText}` : baseToolTipText;
			break;
		}

		case refTypes.TAG: {
			iconKey = refTypes.TAG;
			const tagText: string = annotated && message ? message : name;
			tooltipText = `${tagText} (${translate('Ref-Tag')})`;
			break;
		}

		case refTypes.HEAD:
		default: {
			const isWorktree = ref.worktreeId != null;
			iconKey = isWorktree ? refTypes.WORKTREE : refTypes.HEAD;
			tooltipText = `${name} (${isWorktree ? translate('Ref-Worktree') : translate('Ref-Local')})`;
			break;
		}
	}

	const context: GraphItemContext | undefined = ref.context || undefined;
	const icon = <span data-vscode-context={parseContext(context)}>{getExternalIcon(iconKey)}</span>;

	return (
		<RefIcon
			avatarUrl={avatarUrl || ''}
			context={context}
			icon={icon}
			key={`ref-icon-${index}-${refType}-${name}`}
			tooltipText={tooltipText}
		/>
	);
}

export default class RefNode extends React.PureComponent<RefNodeProps> {
	// If we find a head ref, that ref controls context for the node. Otherwise, we try to find 'origin'.
	// If neither are found, we use the last ref's context.
	getContextForRefGroupIfExists(
		refGroup: GraphRefGroup,
		groupContext: GraphItemContext | null,
		useGroupContext?: boolean,
	): GraphItemContext | undefined {
		let refGroupContext: GraphItemContext | undefined;
		let foundOrigin = false;
		let foundUpstream = false;
		let foundRemote = false;
		for (const ref of refGroup) {
			if (ref.refType === refTypes.HEAD) {
				return useGroupContext ? ref.contextGroup || groupContext : ref.context;
			}

			if (ref.refType === refTypes.REMOTE) {
				if (ref.owner === 'origin') {
					foundOrigin = true;
					refGroupContext = useGroupContext ? ref.contextGroup || groupContext : ref.context;
				} else if (!foundOrigin && ref.owner === 'upstream') {
					foundUpstream = true;
					refGroupContext = useGroupContext ? ref.contextGroup || groupContext : ref.context;
				} else if (!foundOrigin && !foundUpstream) {
					foundRemote = true;
					refGroupContext = useGroupContext ? ref.contextGroup || groupContext : ref.context;
				}
			}

			if (!foundOrigin && !foundUpstream && !foundRemote) {
				refGroupContext = useGroupContext ? ref.contextGroup || groupContext : ref.context;
			}
		}

		return refGroupContext;
	}

	getDecoratedRefIcon(
		key: string,
		icon: ReactElement<any>,
		metadataItem: RefMetadataItem,
		context?: GraphItemContext,
		className?: string,
	): ReactElement<any> {
		return (
			<div
				className={className}
				data-vscode-context={parseContext(context)}
				key={key}
				onClick={event => this.onClickMetadata(event, metadataItem)}
				onDoubleClick={event => this.onDoubleClickMetadata(event, metadataItem)}
			>
				{icon}
			</div>
		);
	}

	getPullRequestIconsAndTooltipsForRef(
		refMetadata: RefMetadataById | null,
		ref: GraphRef,
		existingPullRequestIds: Set<number>,
	): ReactElement<any>[] | null {
		const { getExternalIcon, onMissingRefMetadata, translate } = this.props;

		let metadata: RefMetadata | BaseMetadata[] | BaseMetadata | null | undefined;
		if (ref.refType === refTypes.HEAD) {
			metadata = ref.upstream ? refMetadata?.[ref.upstream.id]?.[pullRequestMetadataType] : null;
		} else {
			metadata = getOrAskForRefMetadata(ref, refMetadata, onMissingRefMetadata, pullRequestMetadataType);
		}

		if (!metadata || (metadata as any).length === 0) {
			return null;
		}

		const pullRequestMetadata: PullRequestMetadata[] = metadata as PullRequestMetadata[];
		const pullRequestIconsAndTooltips: ReactElement<any>[] = [];

		for (const pr of pullRequestMetadata) {
			const { context, hostingServiceType, id: pullRequestId, title } = pr;
			if (existingPullRequestIds.has(pullRequestId)) {
				continue;
			}

			existingPullRequestIds.add(pullRequestId);
			const metadataItem: RefMetadataItem = {
				refId: ref.id,
				type: pullRequestMetadataType,
				data: pr,
			};

			const key = `pr-icon-${ref.refType}-${ref.name}-${pullRequestId}`;
			const icon = this.getDecoratedRefIcon(
				key,
				getExternalIcon('pull-request'),
				metadataItem,
				context,
				classnames('ref-pull-request'),
			);

			pullRequestIconsAndTooltips.push(
				<PullRequestIcon
					hostingServiceType={hostingServiceType}
					icon={icon}
					id={pullRequestId}
					key={key}
					toolTipText={
						!title || title.trim() === ''
							? ''
							: translate(
									'PullRequestIcon-Tooltip',
									pullRequestId,
									title,
									getHostingServiceName(hostingServiceType),
							  )
					}
				/>,
			);
		}

		return pullRequestIconsAndTooltips;
	}

	getUpstreamIndicatorIconsAndTooltipsForRef(
		refMetadata: RefMetadataById | null,
		ref: GraphRef,
	): ReactElement<any> | null {
		const { getExternalIcon, onMissingRefMetadata, translate } = this.props;

		const metadata: RefMetadata | BaseMetadata[] | BaseMetadata | null | undefined = getOrAskForRefMetadata(
			ref,
			refMetadata,
			onMissingRefMetadata,
			upstreamMetadataType,
		);

		if (!metadata || ((metadata as any).ahead === 0 && (metadata as any).behind === 0)) {
			return null;
		}

		const upstreamMetadata: UpstreamMetadata = metadata as UpstreamMetadata;
		const metadataItem: RefMetadataItem = {
			refId: ref.id,
			type: upstreamMetadataType,
			data: upstreamMetadata,
		};

		const isAhead = upstreamMetadata.ahead > 0;
		const isBehind = upstreamMetadata.behind > 0;
		const context = upstreamMetadata.context;
		if (isAhead || isBehind) {
			const ahead = isAhead ? `${upstreamMetadata.ahead > 99 ? '99+' : upstreamMetadata.ahead}` : '';
			const behind = isBehind ? `${upstreamMetadata.behind > 99 ? '99+' : upstreamMetadata.behind}` : '';
			const upstreamName = `${upstreamMetadata.owner}/${upstreamMetadata.name}`;

			let tooltip;
			let type;
			if (isBehind && isAhead) {
				tooltip = translate('UpstreamIndicatorIcon-BehindAndAheadTooltip', behind, ahead, upstreamName);
				type = 'ahead-behind';
			} else if (isBehind) {
				tooltip = translate('UpstreamIndicatorIcon-BehindTooltip', behind, upstreamName);
				type = 'behind';
			} else if (isAhead) {
				tooltip = translate('UpstreamIndicatorIcon-AheadTooltip', ahead, upstreamName);
				type = 'ahead';
			}

			const icon: ReactElement<any> = (
				<>
					{isBehind ? (
						<>
							<span className="ref-upstream-behind">{behind}</span>
							{getExternalIcon('upstream-behind')}
						</>
					) : null}
					{isAhead ? (
						<>
							<span className="ref-upstream-ahead">{ahead}</span>
							{getExternalIcon('upstream-ahead')}
						</>
					) : null}
				</>
			);

			return (
				<UpstreamIndicatorIcon
					icon={this.getDecoratedRefIcon(
						`ref-upstream-${ref.id}`,
						icon,
						metadataItem,
						context,
						'ref-upstream',
					)}
					tooltipText={tooltip}
					type={type}
					upstream={upstreamName}
				/>
			);
		}

		return null;
	}

	getIssueIconsAndTooltipsForRef(
		refMetadata: RefMetadataById | null,
		ref: GraphRef,
		existingIssuesIds: Set<string>,
	): ReactElement<any>[] | null {
		const { getExternalIcon, onMissingRefMetadata, translate } = this.props;

		const metadata: RefMetadata | BaseMetadata[] | BaseMetadata | null | undefined = getOrAskForRefMetadata(
			ref,
			refMetadata,
			onMissingRefMetadata,
			issueMetadataType,
		);

		if (!metadata || (metadata as any).length === 0) {
			return null;
		}

		const issueMetadata: IssueMetadata[] = metadata as IssueMetadata[];
		const issueIconNodes: ReactElement<typeof IssueIcon>[] = [];

		for (const issue of issueMetadata) {
			if (existingIssuesIds.has(issue.id)) {
				continue;
			}

			const metadataItem: RefMetadataItem = {
				refId: ref.id,
				type: issueMetadataType,
				data: issue,
			};

			existingIssuesIds.add(issue.id);

			const icon: ReactElement<any> = (
				<IssueIcon
					getExternalIcon={getExternalIcon}
					id={issue.id}
					issueTrackerType={issue.issueTrackerType}
					key={`issue-icon-${issue.id}`}
					toolTipText={
						!issue.title || issue.title.trim() === ''
							? ''
							: translate('IssueIcon-Tooltip', issue.displayId, issue.title)
					}
				/>
			);

			const context: GraphItemContext | undefined = issue.context || undefined;

			issueIconNodes.push(
				this.getDecoratedRefIcon(`ref-issue-${issue.id}`, icon, metadataItem, context, classnames('ref-issue')),
			);
		}

		return issueIconNodes;
	}

	onContextMenu: OnContextMenu = (event): void => {
		const { onContextMenu, refGroup, sha } = this.props;
		onContextMenu(event, refGroup, sha);
	};

	onClick: OnClick = (event): void => {
		const { onClick, refGroup, sha } = this.props;
		onClick(event, refGroup, sha);
	};

	onClickMetadata: OnClickRefMetadata = (event, metadataItem): void => {
		event.stopPropagation();
		const { onClick, refGroup, sha } = this.props;
		onClick(event, refGroup, sha, metadataItem);
	};

	onDoubleClick: OnDoubleClick = (event): void => {
		const { onDoubleClick, refGroup, sha } = this.props;
		onDoubleClick(event, refGroup, sha);
	};

	onDoubleClickMetadata: OnDoubleClickRefMetadata = (event, metadataItem): void => {
		event.stopPropagation();
		const { onDoubleClick, refGroup, sha } = this.props;
		onDoubleClick(event, refGroup, sha, metadataItem);
	};

	override render(): ReactElement<any | 'span'> {
		const {
			enableShowHideRefsOptions,
			hasActive,
			hasRefs,
			isActive,
			isGhostRef,
			getExternalIcon,
			groupIsHovered,
			isInUnsupportedRebase,
			onRefBeginDrag,
			onRefDragEnter,
			onRefDragLeave,
			onRefDrop,
			onRefCanDrag,
			onRefCanDrop,
			onRefEndDrag,
			isRefGroupIncluded,
			refGroup,
			refGroupContext,
			refIconsPosition,
			refMetadata,
			onHovered,
			onHideRefClick,
			sha,
			showRemoteNamesOnRefs,
			enabledRefMetadataTypes,
			translate,
			type,
			useColumnHeaderIcons,
		} = this.props;

		const shouldShowVisibilityIcon =
			enableShowHideRefsOptions && !hasActive && groupIsHovered && !isRefGroupIncluded;

		const iconsToLeftPosition = refIconsPosition === refIconsPositions.LEFT;
		const iconsToRightPosition = refIconsPosition === refIconsPositions.RIGHT;

		const wrapperClassName: string = classnames('ref-node-wrapper', 'flex');
		const wrapperStyle = { minWidth: 0, marginLeft: 3, height: GRAPH_ROW_INNER_HEIGHT };

		const innerClassName: string = classnames('ref-node', 'flex', 'items-center', 'p1', {
			'has-active': hasActive,
			'dim-ref': !hasRefs && groupIsHovered,
			'is-active': isActive,
		});

		let sharedName = '';
		if (refGroup.length > 0) {
			sharedName = showRemoteNamesOnRefs ? refGroup[0].fullName || refGroup[0].name : refGroup[0].name;
		}

		const labelClassName = classnames({
			'display-none': useColumnHeaderIcons && !groupIsHovered && iconsToRightPosition,
			mr1: iconsToRightPosition,
		});

		const label: ReactElement<typeof RefName> = <RefName className={labelClassName} name={sharedName} />;

		const refIcons: ReactElement<any>[] = [];
		const prIcons: ReactElement<any>[] = [];
		const issueIcons: ReactElement<any>[] = [];
		let maybeUpstreamIndicator: ReactElement<any> | null = null;

		const existingPullRequestIds = new Set<number>();
		const existingIssuesIds = new Set<string>();

		refGroup.forEach((currentRef: GraphRef, i: number) => {
			refIcons.push(getRefIconAndTooltipForRef(getExternalIcon, currentRef, translate, i));

			if (
				enabledRefMetadataTypes.includes(issueMetadataType) &&
				(currentRef.refType === refTypes.HEAD || currentRef.refType === refTypes.REMOTE)
			) {
				const currentRefIssueIcons: ReactElement<any>[] | null = this.getIssueIconsAndTooltipsForRef(
					refMetadata,
					currentRef,
					existingIssuesIds,
				);

				if (currentRefIssueIcons) {
					issueIcons.push(...currentRefIssueIcons);
				}
			}

			if (enabledRefMetadataTypes.includes(pullRequestMetadataType) && currentRef.refType === refTypes.REMOTE) {
				const currentRefPrIcons: ReactElement<any>[] | null = this.getPullRequestIconsAndTooltipsForRef(
					refMetadata,
					currentRef,
					existingPullRequestIds,
				);

				if (currentRefPrIcons) {
					prIcons.push(...currentRefPrIcons);
				}
			}

			if (enabledRefMetadataTypes.includes(upstreamMetadataType) && currentRef.refType === refTypes.HEAD) {
				maybeUpstreamIndicator = this.getUpstreamIndicatorIconsAndTooltipsForRef(refMetadata, currentRef);
			}
		});

		const icons: ReactElement<any> = (
			<span
				className={classnames('flex', 'items-center', 'ref-icons', {
					'is-active': isActive,
					'is-left-position': iconsToLeftPosition,
				})}
			>
				{[...refIcons, ...prIcons, ...issueIcons]}
			</span>
		);

		const currentIcon: ReactElement<any> | null = isActive ? (
			<Icon
				icon={<span className={classnames('ref-icon-current')}>{getExternalIcon('check')}</span>}
				tooltipId="ref-icon-current"
				tooltipText={translate('Ref-Current')}
			/>
		) : null;

		const innerGroupContext: GraphItemContext | undefined = this.getContextForRefGroupIfExists(
			refGroup,
			refGroupContext,
		);
		const outerGroupContext: GraphItemContext | undefined = this.getContextForRefGroupIfExists(
			refGroup,
			refGroupContext,
			true,
		);

		const maybeToggleVisibilityIcon: ReactElement<typeof Icon> | null = shouldShowVisibilityIcon ? (
			<span className="flex ml-auto">
				<Icon
					icon={
						<span
							className={classnames('mr1', 'button')}
							onClick={event => onHideRefClick(event, refGroup, sha)}
						>
							{getExternalIcon('hide')}
						</span>
					}
					tooltipId={`ref-node-toggle-visibility-btn-${sharedName}`}
					tooltipText={translate('Hide')}
				/>
			</span>
		) : null;

		const refDndData: RefDndData = {
			commitType: type,
			isGhostRef: isGhostRef,
			isInUnsupportedRebase: isInUnsupportedRebase,
			refGroup: refGroup,
			sha: sha,
		};

		const refNode: ReactElement<any> = (
			<DndComponent
				canDrag={srcData => onRefCanDrag(srcData)}
				canDrop={(e, srcData, trgData): boolean => onRefCanDrop(e, srcData, trgData)}
				className={wrapperClassName}
				data-vscode-context={parseContext(outerGroupContext)}
				dndData={refDndData}
				dndId={`DndRefNode_${refGroup[0].id}`}
				onBeginDrag={(e, srcData) => onRefBeginDrag(e, srcData)}
				onDragEnter={(e, srcData, trgData) => onRefDragEnter(e, srcData, trgData)}
				onDragLeave={(e, srcData, trgData) => onRefDragLeave(e, srcData, trgData)}
				onDrop={(e, srcData, trgData) => onRefDrop(e, srcData, trgData)}
				onEndDrag={(e, srcData, trgData) => onRefEndDrag(e, srcData, trgData)}
				style={wrapperStyle}
			>
				<span
					className={innerClassName}
					data-test-class="ref-node-span"
					data-vscode-context={parseContext(innerGroupContext)}
					onContextMenu={isInUnsupportedRebase ? undefined : this.onContextMenu}
					onDoubleClick={isInUnsupportedRebase ? undefined : this.onDoubleClick}
					// TODO: I noticed that sometimes we need to click 2 times to trigger
					// the onclick event, so I had to replace onClick with onMouseDown.
					// This is because we are using the onBlur event which sets the state on
					// the GraphContainer component. onMouseDown fixes the problem because
					// MouseDown fires before blur. However, we should take a look at
					// onGraphContainerBlurred and see if we can remove it or do it another
					// way to avoid other similar problems.
					onMouseDown={this.onClick}
					onMouseEnter={event => onHovered(event, refGroup, sha)}
					style={{ minWidth: '100%' }}
				>
					{currentIcon}
					{iconsToLeftPosition && icons}
					{label}
					{iconsToRightPosition && icons}
					{maybeUpstreamIndicator}
					{maybeToggleVisibilityIcon}
				</span>
			</DndComponent>
		);

		return refNode;
	}
}
