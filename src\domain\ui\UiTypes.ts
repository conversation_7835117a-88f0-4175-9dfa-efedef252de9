import type { ReactElement } from 'react';

export type ChildrenElements = string | ReactElement<any> | null;

export interface OnResizeParams {
	height?: number;
	width?: number;
}

export interface OnResizeFromPropChangeParams extends OnResizeParams {
	originalHeight?: number;
	originalWidth?: number;
}

export type ComponentTooltip = string | (() => string);

// Generic UI events
export type OnContextMenu = (event: React.MouseEvent<any>) => void;
export type OnMouseEnter = (event: React.MouseEvent<any>) => void;
export type OnMouseLeave = (event: React.MouseEvent<any>) => void;
export type OnMouseDown = (event: React.MouseEvent<any>) => void;
export type OnWheel = (event: React.MouseEvent<any>) => void;
export type OnClick = (event: React.MouseEvent<any>) => void;
export type OnDoubleClick = (event: React.MouseEvent<any>) => void;
export type OnBlur = (event: React.FocusEvent<any>) => void;
export type OnFocus = (event: React.FocusEvent<any>) => void;
export type OnKeyDown = (event: React.KeyboardEvent<any>) => void;
export type OnKeyPress = (event: React.KeyboardEvent<any>) => void;
export type OnKeyUp = (event: React.KeyboardEvent<any>) => void;
export type OnResize = (dimensions: OnResizeParams) => void;
export type OnResizeEnd = (dimensions: OnResizeParams) => void;
export type OnResizeFromPropChange = (dimensions: OnResizeFromPropChangeParams) => void;
export type OnResizeStart = (dimensions: OnResizeParams) => void;
export type OnCancel = () => void;
export type OnInputChange = (event: React.KeyboardEvent<any>) => void;
