import type { <PERSON><PERSON><PERSON>, <PERSON>rid<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-virtualized';
import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import { formatColor, parseColor } from '../../domain/color/ColorHelpers';
import { commitNodeType, mergeNodeType, stashNodeType, workDirType } from '../../domain/commit/CommitConstants';
import { hasPendingChanges } from '../../domain/commit/CommitHelpers';
import type { CommitType, Sha } from '../../domain/commit/CommitTypes';
import { NUM_COLUMNS_SUPPORTED_PROP_NAME } from '../../domain/cssvariable/CssVariableConstants';
import {
	columnToColorPropName,
	getStyleStringFromCssVariables,
	validateAndMergeCssVariablesWithDefaults,
} from '../../domain/cssvariable/CssVariableHelpers';
import { formatDate } from '../../domain/date/DateHelper';
import { clearXValueAtColumnCache } from '../../domain/edge/EdgeHelpers';
import type { CssVariables } from '../../domain/generic/GenericTypes';
import { NamedKeys } from '../../domain/generic/GenericTypes';
import type {
	GraphColumnMode,
	GraphCommitDescDisplayMode,
	GraphMarkerMetadata,
	GraphMarkerType,
	GraphSearchMode,
	GraphZone,
	GraphZoneType,
	ShiftSelectMode,
	TimelineEntriesByPeriod,
	TimelineEntry,
} from '../../domain/graph/GraphConstants';
import {
	changesZone,
	COMMIT_NODE_MIN_ALPHA,
	commitAuthorZone,
	commitDateTimeZone,
	commitMessageZone,
	commitShaZone,
	commitZone,
	DEFAULT_WORKDIR_STATS,
	GRAPH_HEADER_ROW_HEIGHT,
	GRAPH_ROW_HEIGHT,
	GRAPH_ROW_LAZY_LOAD_COMMITS_OFFSET,
	GRAPH_ROW_PADDING,
	graphCommitDescDisplayModes,
	graphMarkerMetadata,
	graphZoneMetaData,
	numGraphColumnsDefault,
	OPACITY_FACTOR_BY_THEME,
	refZone,
	timelineMsgRowRenderId,
	TIMESTAMP_FORMAT_DATE_TIME,
} from '../../domain/graph/GraphConstants';
import type { ThrottledFn } from '../../domain/graph/GraphHelpers';
import {
	debounceFrame,
	getClampedZoneWidth,
	getFirstExpandableGraphZone,
	getGraphZoneIndexFromGraphZones,
	getGraphZoneModeConstants,
	getHeadRefShaFromGraphRows,
	getHorizontalScrollHeight,
	getLastShrinkableGraphZone,
	getRefIdByBaseRef,
	getRowStatsConstraints,
	getScrollThickness,
	getScrollToAlignment,
	getTimelineEntriesByPeriod,
	getVerticalScrollWidth,
	getZoneWidthsTotal,
	isLastColumn,
	isSingleSelected,
	isWorkDirNodeType,
	last,
	makeGetKeyForCell,
	parseContext,
	throttle,
	workDirStatsHaveChanges,
} from '../../domain/graph/GraphHelpers';
import type {
	AbsoluteNodeLeftByColumn,
	AvatarUrlByEmail,
	AvatarUrlRequestedByEmail,
	ChildRefCollection,
	ChildRefData,
	ChildrenBySha,
	ColumnColorByColumn,
	CommitDateTimeSource,
	DownstreamsByUpstream,
	Edge,
	EdgeByColumn,
	ExcludeByType,
	ExcludeRefsById,
	ExcludeRemotesByName,
	ExternalIconKeys,
	FormatCommitMessage,
	FormatRefShorthand,
	GetExternalIcon,
	GetMissingAvatar,
	GetMissingRefMetadata,
	GraphColumnSetting,
	GraphColumnsSettings,
	GraphContexts,
	GraphMarkerColors,
	GraphMarkerRowIndices,
	GraphPlatform,
	GraphRefGroup,
	GraphRefOptData,
	GraphRow,
	GraphZoneModeConstants,
	GraphZonesByType,
	HasSpecialChildBySha,
	Head,
	HighlightedShas,
	IncludeOnlyRefsById,
	IncludeOnlyRemotesByName,
	IsExcludedBySha,
	IsRefShorthandValid,
	IsSelectedBySha,
	NodeOffsetByColumn,
	NodeOpacityByColumn,
	OnClearCurrentlyHoveredGraphCommit,
	OnClickCommit,
	OnClickGraphRef,
	OnClickGraphRow,
	OnClickRef,
	OnCommitContextMenu,
	OnCommitMessageChange,
	OnCurrentlyHoveredGraphCommit,
	OnDoubleClickCommit,
	OnDoubleClickGraphRef,
	OnDoubleClickGraphRow,
	OnDoubleClickRef,
	OnEmailsMissingAvatarUrls,
	OnFilterColumnClick,
	OnFormatCommitDateTime,
	OnGraphColumnReOrdered,
	OnGraphColumnResized,
	OnGraphColumnsReOrdered,
	OnGraphMouseEnter,
	OnGraphMouseLeave,
	OnGraphRefNodeHovered,
	OnGraphRefNodeUnhovered,
	OnGraphResized,
	OnGraphRowHovered,
	OnGraphRowUnhovered,
	OnGraphScrollForZone,
	OnGraphVisibleRowsChanged,
	OnGraphZoneResize,
	OnGraphZoneResizeEnd,
	OnLoadSelectedGraphRow,
	OnPopupGraphHeaderContextMenu,
	OnRefBeginDrag,
	OnRefCanDrag,
	OnRefCanDrop,
	OnRefContextMenu,
	OnRefCreate,
	OnRefCreateCancel,
	OnRefCreateContextMenu,
	OnRefDragEnter,
	OnRefDragLeave,
	OnRefDrop,
	OnRefEndDrag,
	OnRefNodeHovered,
	OnRefNodeUnhovered,
	OnRefShorthandChange,
	OnRefsMissingMetadata,
	OnRefZoneContextMenu,
	OnRefZoneHovered,
	OnRefZoneUnhovered,
	OnRowContextMenu,
	OnScrollForZone,
	OnSelectGraphRows,
	OnSettingsClick,
	OnShowMoreCommits,
	OnToggleRefNodesShown,
	OnToggleRefsVisibilityClick,
	OnWipMessageBlur,
	OnWipMessageFocus,
	ProcessedGraphRow,
	ProcessedGraphRowBySha,
	RefDndData,
	RefMetadataById,
	RefMetadataItem,
	RefMetadataRequestedById,
	RefMetadataType,
	RefsMissingMetadata,
	Remote,
	ReserverInfo,
	ReserverInfoBySha,
	RowEdge,
	RowEdges,
	RowRenderersByIds,
	RowStats,
	RowStatsConstraints,
	ShaByRefId,
	Tag,
	TopAndBottomVisibleRowIndex,
} from '../../domain/graph/GraphTypes';
import { pullRequestMetadataType } from '../../domain/graph/GraphTypes';
import { ProcessedGraphRowObj } from '../../domain/graph/row/ProcessedGraphRowObj';
import { DEFAULT_TRANSLATIONS } from '../../domain/language/LanguageConstants';
import { formatString } from '../../domain/language/LanguageHelpers';
import type { TranslationFn } from '../../domain/language/LanguageTypes';
import type { GetRealKeyForCellFunc } from '../../domain/reactvirtualized/ReactVirtualizedHelpers';
import { makeSmartCellRangeRenderer } from '../../domain/reactvirtualized/ReactVirtualizedHelpers';
import { refIconsPositions, refTypes } from '../../domain/ref/RefConstants';
import type {
	CreateRefFormData,
	GraphRefType,
	RefFullName,
	RefIconsPosition,
	RefShorthand,
} from '../../domain/ref/RefTypes';
import type { OnBlur, OnKeyDown, OnKeyUp, OnMouseDown, OnResizeParams } from '../../domain/ui/UiTypes';
import type { WorkDirStats } from '../../domain/workdir/WorkDirTypes';
import { clamp } from '../../utils/MathUtils';
import { clearEdgeNodesCache } from './commitzone/edges/EdgeNodes';
import { clearEndingEdgeCacheCache } from './commitzone/edges/EndingEdge';
import { clearPassThroughEdgeCache } from './commitzone/edges/PassThroughEdge';
import { clearStartingEdgeCache } from './commitzone/edges/StartingEdge';
import { GraphColumn } from './GraphColumn';
import GraphHeaderRow from './GraphHeaderRow';
import type { IGraphContainer } from './IGraphContainer';
import makeChangesZoneRowRenderer from './rowRenderers/ChangesZoneRow';
import makeCommitAuthorZoneRowRenderer from './rowRenderers/CommitAuthorZoneRow';
import makeCommitDateTimeZoneRowRenderer from './rowRenderers/CommitDateTimeZoneRow';
import makeCommitMessageZoneRowRenderer from './rowRenderers/CommitMessageZoneRow';
import makeCommitShaZoneRowRenderer from './rowRenderers/CommitShaZoneRow';
import makeCommitZoneRowRenderer from './rowRenderers/CommitZoneRow';
import makeRefZoneRowRenderer from './rowRenderers/RefZoneRow';
import makeTimelineMsgRowRenderer from './rowRenderers/TimelineMsgRow';

type ClearScrollToIndex = () => void;

type OnZoneEnter = (
	e: React.MouseEvent<any>,
	zoneType: GraphZoneType,
	hoveredRefGroup?: GraphRefGroup,
	hoveredSha?: Sha,
) => void;

type OnGraphContainerBlurred = (
	event: React.FocusEvent<any>,
	hoveredRefGroup?: GraphRefGroup,
	hoveredSha?: Sha,
) => void;

interface OwnProps {
	formatCommitDateTime?: OnFormatCommitDateTime;
	formatCommitMessage?: FormatCommitMessage;
	getExternalIcon: GetExternalIcon;
	platform?: GraphPlatform; // TODO: Added this parameter by decision taken but I don't like this way
	shaLength?: number | null;
	nonce?: string;
}

export interface StateProps {
	avatarUrlByEmail?: AvatarUrlByEmail;
	cssVariables?: CssVariables;
	customFooterRow?: ReactElement<any>;
	contexts?: GraphContexts | null;
	createRefFormData?: CreateRefFormData | null;
	dimMergeCommits?: boolean;
	enableShowHideRefsOptions?: boolean;
	downstreamsByUpstream?: DownstreamsByUpstream;
	formatRefShorthand?: FormatRefShorthand;
	highlightRowsOnRefHover?: boolean;
	isContainerWindowFocused?: boolean;
	isRefShorthandValid?: IsRefShorthandValid;
	showGhostRefsOnRowHover?: boolean;
	showRemoteNamesOnRefs?: boolean;
	enabledRefMetadataTypes?: RefMetadataType[];
	enabledScrollMarkerTypes?: GraphMarkerType[];
	scrollRowPadding?: number;
	enableMultiSelection?: boolean;
	graphCommitDescDisplayMode?: GraphCommitDescDisplayMode;
	graphRows: GraphRow[];
	hasMoreCommits?: boolean;
	highlightedShas?: HighlightedShas;
	excludeByType?: ExcludeByType;
	excludeRefsById?: ExcludeRefsById;
	includeOnlyRefsById?: IncludeOnlyRefsById;
	isCommitting?: boolean;
	isSelectedBySha?: IsSelectedBySha;
	isInUnsupportedRebase?: boolean;
	isLoadingRows?: boolean;
	// maybeCreateRefFormSha: ?Sha; TODO: Decide how we want to handle adding refs to the library in v2
	pendingCommitMessageSummary?: string;
	shiftSelectMode?: ShiftSelectMode;
	suppressNonRefRowTooltips?: boolean;
	refMetadataById?: RefMetadataById | null;
	refIconsPosition?: RefIconsPosition | null;
	repoPath?: string;
	searchMode?: GraphSearchMode;
	columnsSettings?: GraphColumnsSettings;
	themeOpacityFactor?: number;
	translate?: TranslationFn;
	useAuthorInitialsForAvatars: boolean;
	rowsStats?: Record<string, RowStats>;
	rowsStatsLoading?: boolean;
	wipMessageEditable?: boolean;
	workDirStats?: WorkDirStats;
}

export interface DispatchProps {
	onDoubleClickGraphRef?: OnDoubleClickGraphRef;
	onGraphRowHovered?: OnGraphRowHovered;
	onGraphRowUnhovered?: OnGraphRowUnhovered;
	onLoadSelectedGraphRow?: OnLoadSelectedGraphRow;
	onRefContextMenu?: OnRefContextMenu;
	onRefBeginDrag?: OnRefBeginDrag;
	onRefCanDrag?: OnRefCanDrag;
	onRefCanDrop?: OnRefCanDrop;
	onRefCreate?: OnRefCreate;
	onRefCreateCancel?: OnRefCreateCancel;
	onRefCreateContextMenu?: OnRefCreateContextMenu;
	onRefDragEnter?: OnRefDragEnter;
	onRefDragLeave?: OnRefDragLeave;
	onRefDrop?: OnRefDrop;
	onRefEndDrag?: OnRefEndDrag;
	onToggleRefsVisibilityClick?: OnToggleRefsVisibilityClick;
	onRowContextMenu?: OnRowContextMenu;
	onGraphRefNodeHovered?: OnGraphRefNodeHovered;
	onGraphRefNodeUnhovered?: OnGraphRefNodeUnhovered;
	onBlurWipNodeInput?: OnWipMessageBlur;
	onGraphColumnsReOrdered?: OnGraphColumnsReOrdered;
	onFilterColumnClick?: OnFilterColumnClick;
	onColumnResized?: OnGraphColumnResized;
	onFocusWipNodeInput?: OnWipMessageFocus;
	onMessageChange?: OnCommitMessageChange;
	onPopupGraphHeaderContextMenu?: OnPopupGraphHeaderContextMenu;
	onClickGraphRef?: OnClickGraphRef;
	onClickGraphRow?: OnClickGraphRow;
	onDoubleClickGraphRow?: OnDoubleClickGraphRow;
	onSettingsClick?: OnSettingsClick;
	onSelectGraphRows?: OnSelectGraphRows;
	onScrollForZone?: OnGraphScrollForZone;
	onScrollToRowCausedUpdateForFirstColumn?: OnGraphScrollForZone;
	onShowMoreCommits?: OnShowMoreCommits;
	onGraphResized?: OnGraphResized;
	onGraphVisibleRowsChanged?: OnGraphVisibleRowsChanged;
	onGraphMouseEnter?: OnGraphMouseEnter;
	onGraphMouseLeave?: OnGraphMouseLeave;
	onEmailsMissingAvatarUrls?: OnEmailsMissingAvatarUrls;
	onRefsMissingMetadata?: OnRefsMissingMetadata;
}

interface Props extends OwnProps, StateProps, DispatchProps {}

export type GraphContainerProps = Props;

type ComponentState = {
	avatarUrlByEmail: AvatarUrlByEmail;
	columnColorByColumn: ColumnColorByColumn;
	contexts?: GraphContexts | null;
	createRefFormData?: CreateRefFormData | null;
	cssVariablesWithDefaults: CssVariables;
	currentlyHoveredCommitSha?: Sha;
	dimMergeCommits: boolean;
	dimRowsOfSelectedCommit: boolean;
	enableShowHideRefsOptions: boolean;
	highlightRowsOnRefHover: boolean;
	showGhostRefsOnRowHover: boolean;
	showRemoteNamesOnRefs: boolean;
	enabledRefMetadataTypes: RefMetadataType[];
	enabledScrollMarkerTypes: GraphMarkerType[];
	graphCommitDescDisplayMode: GraphCommitDescDisplayMode;
	graphZones: GraphZone[];
	hasMoreCommits: boolean;
	height: number;
	highlightedShas?: HighlightedShas;
	hoveredRefGroup?: GraphRefGroup;
	hoveredRefZoneSha?: Sha;
	isLoadingRows: boolean;
	numGraphColumns: number;
	markerRowIndices: GraphMarkerRowIndices;
	pendingCommitMessageSummary: string;
	processedRows: ProcessedGraphRow[];
	refIconsPosition: RefIconsPosition;
	refMetadataById?: RefMetadataById | null;
	rowsStats?: Record<string, RowStats>;
	rowsStatsLoading: boolean;
	useAuthorInitialsForAvatars: boolean;
	scrollLeft: number;
	scrollToAlignment: Alignment;
	// TODO: review "scrollToIndex" property. See if can be removed and use "scrollTop" instead.
	// If it is not possible, this property should be private (available only in the state of the React component).
	scrollToIndex?: number;
	scrollTop: number;
	shaLength?: number | null;
	themeOpacityFactor: number;
	width: number;
	workDirStats: WorkDirStats;
};

type ColumnsToFreeBySha = { [sha: Sha]: number[] };
type ColumnsUsed = { [column: number]: boolean };
type HasMergeNodeChildBySha = { [sha: Sha]: boolean };

class GraphContainer extends React.Component<Props, ComponentState> implements IGraphContainer {
	private graphComponentRef: any = React.createRef();
	private graphContainerRef: any = React.createRef();

	private resizeObserver: ResizeObserver;
	private lastNavSha: Sha | null = null;
	private currentSha: Sha | null = null;

	isRefContextMenuShown: boolean = false;
	isMounted: boolean = false;
	selectedShas: Set<Sha> = new Set();
	selectionAnchorSha?: Sha;
	selectionFocusSha?: Sha;
	processedGraphRowBySha: ProcessedGraphRowBySha = {};
	shaByRefId: ShaByRefId = {};
	childrenBySha: ChildrenBySha = {};
	scrollToSha?: Sha; // TODO: review and refactor that property
	headSha?: Sha;
	headUpstreamSha?: Sha;
	lastLazyLoadHeight = 0;
	lastTopVisibleRowIndex = -1;
	lastBottomVisibleRowIndex = -1;
	maxColumns = 0; // TODO: review and refactor
	orderedGraphRows: ProcessedGraphRow[] = [];
	rowStatsConstraints: RowStatsConstraints = { min: 0, max: 0 };
	shouldFireShowMoreCommits = true;
	columnsToFreeWhenFound: ColumnsToFreeBySha = {};
	hasMergeNodeChildBySha: HasMergeNodeChildBySha = {};
	reserverInfoBySha: ReserverInfoBySha = {};
	columnsUsed: ColumnsUsed = {};
	rowsStats?: Record<string, RowStats>;
	workDirStats: WorkDirStats = DEFAULT_WORKDIR_STATS;
	excludeByType: ExcludeByType = {};
	excludeRefsById: ExcludeRefsById = {};
	includeOnlyRefsById: IncludeOnlyRefsById = {};
	excludeRemotesByName: ExcludeRemotesByName = {};
	includeOnlyRemotesByName: IncludeOnlyRemotesByName = {};
	highlightedShas: HighlightedShas = {};
	searchMode: GraphSearchMode = 'normal';
	scrollLeft = 0;
	scrollTop = 0;
	graphHeight = 585;
	graphWidth = 804;
	graphZoneModeConstants: GraphZoneModeConstants = getGraphZoneModeConstants();
	graphZonesByType: GraphZonesByType = {};
	graphZoneOrdering: GraphZoneType[] = [
		refZone,
		commitZone,
		commitMessageZone,
		commitAuthorZone,
		changesZone,
		commitDateTimeZone,
		commitShaZone,
	];
	clientHeight: number = 0;

	missingAvatarsTimer?: NodeJS.Timeout;
	pendingMissingAvatars: AvatarUrlByEmail = {};
	requestedMissingAvatars: AvatarUrlRequestedByEmail = {};

	missingRefsMetadataTimer?: NodeJS.Timeout;
	pendingMissingRefsMetadata: RefsMissingMetadata = {};
	requestedMissingRefsMetadata: RefMetadataRequestedById = {};

	branchUpstreamRowIndices: number[] = [];

	downstreamsByUpstream: DownstreamsByUpstream = {};

	timelinesInterval?: NodeJS.Timeout;

	constructor(props: Props) {
		super(props);

		this.resizeObserver = new ResizeObserver(entries =>
			entries.forEach((e: ResizeObserverEntry) => this.onGraphResized(e.contentRect.width, e.contentRect.height)),
		);

		for (const [sha, selected] of Object.entries(props.isSelectedBySha || {})) {
			if (selected) {
				this.selectedShas.add(sha);
				this.selectionAnchorSha = sha;
				this.selectionFocusSha = sha;
			}
		}

		this.excludeByType = this.props.excludeByType || {};
		this.excludeRefsById = this.props.excludeRefsById || {};
		this.excludeRemotesByName = this.getExcludeRemotesByName();
		this.includeOnlyRemotesByName = this.getIncludeOnlyRemotesByName();
		this.includeOnlyRefsById = this.props.includeOnlyRefsById || {};
		this.downstreamsByUpstream = this.props.downstreamsByUpstream || {};
		this.highlightedShas = this.props.highlightedShas || {};
		this.searchMode = this.props.searchMode || 'normal';

		// Ordering here is important.
		this.rowsStats = this.props.rowsStats || this.rowsStats;
		this.workDirStats = this.props.workDirStats || this.workDirStats;
		this.processRows(this.props.graphRows);

		this.cleanupSelections();
		this.updateMarkerRowIndices(['selection', 'highlights']);

		const cssVariablesWithDefaults: CssVariables = validateAndMergeCssVariablesWithDefaults(
			this.props.cssVariables,
		);

		this.graphZoneModeConstants = getGraphZoneModeConstants(
			this.props.columnsSettings?.[commitZone]?.mode as GraphColumnMode,
		);

		this.maybeSortGraphZoneOrderingFromSettings(this.props.columnsSettings);

		this.state = {
			avatarUrlByEmail: this.props.avatarUrlByEmail || {},
			contexts: this.props.contexts,
			createRefFormData: this.props.createRefFormData,
			cssVariablesWithDefaults: cssVariablesWithDefaults,
			columnColorByColumn: this.getColumnColorByColumn(cssVariablesWithDefaults),
			currentlyHoveredCommitSha: undefined,
			dimMergeCommits: this.props.dimMergeCommits || false,
			dimRowsOfSelectedCommit: false,
			enableShowHideRefsOptions: this.props.enableShowHideRefsOptions || false,
			highlightRowsOnRefHover: this.props.highlightRowsOnRefHover || false,
			showGhostRefsOnRowHover: this.props.showGhostRefsOnRowHover || false,
			showRemoteNamesOnRefs: this.props.showRemoteNamesOnRefs || false,
			enabledRefMetadataTypes: this.props.enabledRefMetadataTypes || [],
			enabledScrollMarkerTypes: this.props.enabledScrollMarkerTypes || [],
			graphCommitDescDisplayMode: this.props.graphCommitDescDisplayMode || graphCommitDescDisplayModes.ALWAYS,
			graphZones: this.initializeGraphZones(this.props.columnsSettings, this.graphZoneOrdering, 580),
			hasMoreCommits: this.props.hasMoreCommits || false,
			height: this.graphHeight,
			highlightedShas: this.props.highlightedShas,
			hoveredRefGroup: undefined,
			hoveredRefZoneSha: undefined,
			isLoadingRows: this.props.isLoadingRows || false,
			numGraphColumns: this.getNumGraphColumns(cssVariablesWithDefaults),
			markerRowIndices: {},
			pendingCommitMessageSummary: this.props.pendingCommitMessageSummary || '',
			processedRows: [...this.orderedGraphRows],
			refIconsPosition: this.getRefIconsPositionOrDefault(this.props.refIconsPosition),
			refMetadataById: this.props.refMetadataById,
			rowsStats: this.rowsStats,
			rowsStatsLoading: this.props.rowsStatsLoading || false,
			useAuthorInitialsForAvatars: this.props.useAuthorInitialsForAvatars || false,
			scrollLeft: this.getScrollLeft(),
			scrollToAlignment: this.getScrollToAlignment(),
			scrollToIndex: this.getScrollToIndex(),
			scrollTop: this.getScrollTop(),
			shaLength: this.props.shaLength,
			themeOpacityFactor: this.props.themeOpacityFactor || OPACITY_FACTOR_BY_THEME.dark,
			width: this.graphWidth,
			workDirStats: this.workDirStats,
		};
	}

	override componentDidMount(): void {
		this.isMounted = true;

		this.resizeObserver.observe(this.graphComponentRef.current);

		window.addEventListener('keydown', this.onWindowKeyDown as any);
		window.addEventListener('keyup', this.onWindowKeyUp as any);
		window.addEventListener('blur', this.onWindowBlur as any);

		const selectedShas: Sha[] = this.getSelectedShas();
		if (selectedShas.length > 0) {
			this.selectCommits(selectedShas, false, true);
		}
	}

	override UNSAFE_componentWillReceiveProps(nextProps: Props): void {
		let shouldUpdateGraphZones = false;
		let shouldProcessRowsFromFilterListChange = false;
		const updateMarkerRowsMap: Partial<Record<GraphMarkerType, true>> = {};
		let shouldProcessRows = false;

		if (this.props.searchMode !== nextProps.searchMode) {
			this.searchMode = nextProps.searchMode || 'normal';
			shouldProcessRowsFromFilterListChange = true;
			updateMarkerRowsMap['highlights'] = true;
		}

		if (this.props.highlightedShas !== nextProps.highlightedShas) {
			this.setState({
				highlightedShas: nextProps.highlightedShas,
			});
			this.highlightedShas = nextProps.highlightedShas || {};
			if (this.searchMode === 'filter') {
				shouldProcessRowsFromFilterListChange = true;
			}
			updateMarkerRowsMap['highlights'] = true;
		}

		const isSelectedByShaChanged = this.props.isSelectedBySha !== nextProps.isSelectedBySha;
		const searchModeChanged = this.props.searchMode !== nextProps.searchMode;
		if (searchModeChanged) {
			shouldProcessRows = true;
		}

		// this won't select commits when rows haven't been processed yet
		if (
			this.processedGraphRowBySha &&
			(isSelectedByShaChanged ||
				// NOTE: It may happen that the user searches for a commit that has
				// already been selected but it was not in view because he moved
				// the scroll bar after. In that case, we have to select commits
				// also when the `highlightedShas` property changes to force the
				// scrollbar position to move to the searched commit. See issue
				// GK-4478 for more details.
				this.props.highlightedShas !== nextProps.highlightedShas)
		) {
			const newSelectedShas = nextProps.isSelectedBySha ? Object.keys(nextProps.isSelectedBySha) : [];
			this.selectCommits(newSelectedShas, false, true);

			// Update shift selection anchor when selection changes via props
			if (isSelectedByShaChanged && newSelectedShas.length > 0) {
				// Use the last selected SHA as the anchor
				this.selectionAnchorSha = newSelectedShas[newSelectedShas.length - 1];
			} else if (isSelectedByShaChanged && newSelectedShas.length === 0) {
				// Clear anchor if no selection
				this.selectionAnchorSha = undefined;
			}
		}

		if (this.props.excludeByType !== nextProps.excludeByType) {
			this.excludeByType = nextProps.excludeByType || {};
			if (nextProps.graphRows?.length) {
				shouldProcessRowsFromFilterListChange = true;
			}
		}

		if (this.props.excludeRefsById !== nextProps.excludeRefsById) {
			this.excludeRefsById = nextProps.excludeRefsById || {};
			this.excludeRemotesByName = this.getExcludeRemotesByName();
			if (nextProps.graphRows?.length) {
				shouldProcessRowsFromFilterListChange = true;
			}
		}

		if (this.props.includeOnlyRefsById !== nextProps.includeOnlyRefsById) {
			this.includeOnlyRefsById = nextProps.includeOnlyRefsById || {};
			this.includeOnlyRemotesByName = this.getIncludeOnlyRemotesByName();
			if (nextProps.graphRows?.length) {
				shouldProcessRowsFromFilterListChange = true;
			}
		}

		if (this.props.themeOpacityFactor !== nextProps.themeOpacityFactor) {
			this.setState({
				themeOpacityFactor: nextProps.themeOpacityFactor,
			});
		}

		if (this.props.dimMergeCommits !== nextProps.dimMergeCommits) {
			this.setState({ dimMergeCommits: nextProps.dimMergeCommits || false });
		}

		if (this.props.highlightRowsOnRefHover !== nextProps.highlightRowsOnRefHover) {
			this.setState({ highlightRowsOnRefHover: nextProps.highlightRowsOnRefHover || false });
		}

		if (this.props.showGhostRefsOnRowHover !== nextProps.showGhostRefsOnRowHover) {
			this.setState({ showGhostRefsOnRowHover: nextProps.showGhostRefsOnRowHover || false });
		}

		if (this.props.showRemoteNamesOnRefs !== nextProps.showRemoteNamesOnRefs) {
			this.setState({ showRemoteNamesOnRefs: nextProps.showRemoteNamesOnRefs || false });
		}

		if (this.props.enabledRefMetadataTypes !== nextProps.enabledRefMetadataTypes) {
			this.setState({ enabledRefMetadataTypes: nextProps.enabledRefMetadataTypes || [] });
		}

		if (this.props.enabledScrollMarkerTypes !== nextProps.enabledScrollMarkerTypes) {
			this.setState({ enabledScrollMarkerTypes: nextProps.enabledScrollMarkerTypes || [] });
		}

		if (this.props.graphCommitDescDisplayMode !== nextProps.graphCommitDescDisplayMode) {
			this.setState({
				graphCommitDescDisplayMode: nextProps.graphCommitDescDisplayMode || graphCommitDescDisplayModes.ALWAYS,
			});
		}

		// Notes: This fixes issue #153. this property was added to be set from
		// the Gitlens side (or Electron app) to notify the graph component that
		// the application window focus has changed. In this way, the graph will
		// be able to disable the hover of the refs if VSCode (or Electron app)
		// loses the focus. This input property is not needed to be set on a normal
		// case. Default value is true.
		if (this.props.isContainerWindowFocused !== nextProps.isContainerWindowFocused) {
			this.dimRowsOfSelectedCommit(false);
		}

		if (this.props.downstreamsByUpstream !== nextProps.downstreamsByUpstream) {
			this.downstreamsByUpstream = nextProps.downstreamsByUpstream || {};
			if (nextProps.graphRows?.length) {
				shouldProcessRowsFromFilterListChange = true;
			}
		}

		if (
			this.props.graphRows !== nextProps.graphRows ||
			this.props.cssVariables !== nextProps.cssVariables ||
			shouldProcessRowsFromFilterListChange
		) {
			// Bust the caches so we can take in any new CSS variable changes from themes
			this.cleanEdgeCaches();

			const cssVariablesWithDefaults: CssVariables = validateAndMergeCssVariablesWithDefaults(
				nextProps.cssVariables,
			);
			shouldProcessRows = true;
			this.setState({
				columnColorByColumn: this.getColumnColorByColumn(cssVariablesWithDefaults),
				cssVariablesWithDefaults: cssVariablesWithDefaults,
				numGraphColumns: this.getNumGraphColumns(cssVariablesWithDefaults),
			});

			this.cleanupSelections();
			shouldUpdateGraphZones = true;
			if (nextProps.graphRows?.length < this.props.graphRows?.length || shouldProcessRowsFromFilterListChange) {
				// Reset the lazy load cursor if we lost commits, as is the case with resets or new repos.
				this.lastLazyLoadHeight = 0;
			}
		}

		if (this.props.useAuthorInitialsForAvatars !== nextProps.useAuthorInitialsForAvatars) {
			this.setState({ useAuthorInitialsForAvatars: nextProps.useAuthorInitialsForAvatars });
		}

		if (this.props.shaLength !== nextProps.shaLength) {
			this.setState({ shaLength: nextProps.shaLength });
		}

		if (
			this.props.hasMoreCommits !== nextProps.hasMoreCommits ||
			this.props.onShowMoreCommits !== nextProps.onShowMoreCommits
		) {
			this.setState({ hasMoreCommits: nextProps.hasMoreCommits || false }, () =>
				this.loadMoreCommitsIfNecessary(this.graphHeight, this.state.hasMoreCommits),
			);
		}

		if (this.props.isLoadingRows !== nextProps.isLoadingRows) {
			this.setState({ isLoadingRows: nextProps.isLoadingRows || false });
		}

		if (this.props.rowsStats !== nextProps.rowsStats) {
			this.rowsStats = nextProps.rowsStats;

			this.updateRowStatsConstraints();
			this.setState({
				rowsStats: nextProps.rowsStats,
			});
		}

		if (this.props.rowsStatsLoading !== nextProps.rowsStatsLoading) {
			this.setState({ rowsStatsLoading: nextProps.rowsStatsLoading || false });
		}

		if (this.props.workDirStats !== nextProps.workDirStats) {
			this.workDirStats = nextProps.workDirStats || DEFAULT_WORKDIR_STATS;
			if (
				(!workDirStatsHaveChanges(this.props.workDirStats) &&
					workDirStatsHaveChanges(nextProps.workDirStats)) ||
				(workDirStatsHaveChanges(this.props.workDirStats) && !workDirStatsHaveChanges(nextProps.workDirStats))
			) {
				shouldProcessRows = true;
				this.setState({
					workDirStats: this.workDirStats,
				});
			} else {
				this.setState({ workDirStats: this.workDirStats });
			}
		}

		if (this.props.avatarUrlByEmail !== nextProps.avatarUrlByEmail) {
			this.setState({ avatarUrlByEmail: nextProps.avatarUrlByEmail || {} });
			this.requestedMissingAvatars = {};
		}

		if (this.props.refIconsPosition !== nextProps.refIconsPosition) {
			this.setState({ refIconsPosition: this.getRefIconsPositionOrDefault(nextProps.refIconsPosition) });
		}

		if (this.props.refMetadataById !== nextProps.refMetadataById) {
			this.setState({ refMetadataById: nextProps.refMetadataById });
			this.requestedMissingRefsMetadata = {};
			updateMarkerRowsMap['pullRequests'] = true;
		}

		if (this.props.columnsSettings !== nextProps.columnsSettings) {
			if (nextProps.columnsSettings?.[commitZone]?.mode !== this.props.columnsSettings?.[commitZone]?.mode) {
				this.cleanEdgeCaches();
				this.graphZoneModeConstants = getGraphZoneModeConstants(
					nextProps.columnsSettings?.[commitZone]?.mode as GraphColumnMode,
				);
				this.updateCommitZoneContentWidthFromChange();
				const zone = this.graphZonesByType[commitZone];
				zone.currentWidth = zone.contentWidth;
				zone.preferredWidth = zone.contentWidth;
				zone.minimumWidth = this.graphZoneModeConstants.COMMIT_ZONE_VIEWPORT_WIDTH_MIN;
				zone.showIconWidth = this.graphZoneModeConstants.COMMIT_ZONE_SHOW_ICON_WIDTH;

				this.loadEdgesBySha();
				shouldUpdateGraphZones = true;
			}

			if (nextProps.columnsSettings) {
				this.updateZonesFromSettings(nextProps.columnsSettings);
				shouldUpdateGraphZones = true;
			}
		}

		if (this.props.contexts !== nextProps.contexts) {
			this.setState({ contexts: nextProps.contexts });
		}

		if (this.props.pendingCommitMessageSummary !== nextProps.pendingCommitMessageSummary) {
			this.setState({ pendingCommitMessageSummary: nextProps.pendingCommitMessageSummary || '' });
		}

		if (this.props.enableShowHideRefsOptions !== nextProps.enableShowHideRefsOptions) {
			this.setState({ enableShowHideRefsOptions: nextProps.enableShowHideRefsOptions });
		}

		if (this.props.createRefFormData !== nextProps.createRefFormData) {
			if (nextProps.createRefFormData?.sha) {
				this.selectCommits([nextProps.createRefFormData?.sha], false, true);
				// Update anchor when selection changes for create ref form
				this.selectionAnchorSha = nextProps.createRefFormData.sha;
			}
			this.setState({ createRefFormData: nextProps.createRefFormData });
		}

		if (shouldProcessRows) {
			this.processRows(nextProps.graphRows);
			// Node: processRows can change maxColumns, so we need to recalculate columnColorByColumn.
			// TODO: Refactor maxColumns logic to avoid this ordering issue.
			const cssVariablesWithDefaults: CssVariables = validateAndMergeCssVariablesWithDefaults(
				nextProps.cssVariables,
			);

			if (searchModeChanged || isSelectedByShaChanged) {
				this.selectCommits(
					nextProps.isSelectedBySha ? Object.keys(nextProps.isSelectedBySha) : [],
					false,
					true,
				);
			}

			this.setState({
				processedRows: [...this.orderedGraphRows],
				columnColorByColumn: this.getColumnColorByColumn(cssVariablesWithDefaults),
			});
		}

		if (shouldUpdateGraphZones) {
			this.updateCommitZoneContentWidthFromChange();
			const graphZones = this.getOrderedActiveGraphZones();
			this.expandLastZoneMinWidthForScrollbar(graphZones);
			this.ensureZoneWidthsMatchGraphWidth(graphZones, this.graphWidth);
			this.setState({ graphZones: graphZones });
		}

		if (Object.keys(updateMarkerRowsMap).length) {
			this.updateMarkerRowIndices(Object.keys(updateMarkerRowsMap) as GraphMarkerType[], nextProps);
		}
	}

	override componentWillUnmount(): void {
		this.isMounted = false;

		this.clearTimelinesInterval();

		window.removeEventListener('keydown', this.onWindowKeyDown as any);
		window.removeEventListener('keyup', this.onWindowKeyUp as any);
		window.removeEventListener('blur', this.onWindowBlur as any);

		this.resizeObserver.disconnect();
	}

	focus(): void {
		this.graphContainerRef?.current?.focus();
	}

	getRefIconsPositionOrDefault(refIconsPosition?: RefIconsPosition | null): RefIconsPosition {
		return refIconsPosition || refIconsPositions.LEFT;
	}

	// @axosoft-ramint do we really need to throttle it? it works pretty fine without throttling
	onGraphVisibleRowsUpdatedThrottled: ThrottledFn = throttle(() => this.onGraphVisibleRowsUpdated(), 250, 20);

	onBlurWipNodeInput: OnWipMessageBlur = (event?: React.FocusEvent<HTMLInputElement>): void => {
		if (this.props.onBlurWipNodeInput) {
			this.props.onBlurWipNodeInput(event);
		}
	};

	onDoubleClickRef: OnDoubleClickRef = (
		event: React.MouseEvent<any>,
		refGroup: GraphRefGroup,
		sha: Sha,
		metadataItem?: RefMetadataItem,
	): void => {
		if (this.props.onDoubleClickGraphRef) {
			this.props.onDoubleClickGraphRef(event, refGroup, this.processedGraphRowBySha[sha], metadataItem);
		}
	};

	onFocusWipNodeInput: OnWipMessageFocus = (event: React.FocusEvent<HTMLInputElement>): void => {
		if (this.props.onFocusWipNodeInput) {
			this.props.onFocusWipNodeInput(event);
		}
	};

	onFilterColumnClick: OnFilterColumnClick = (event: React.MouseEvent<any>, graphZoneType: GraphZoneType): void => {
		if (this.props.onFilterColumnClick) {
			this.props.onFilterColumnClick(event, graphZoneType);
		}
	};

	onCurrentlyHoveredGraphCommit: OnCurrentlyHoveredGraphCommit = (
		event: React.MouseEvent<any>,
		graphZoneType: GraphZoneType,
		sha: Sha,
		currentlyHoveredCommitSha?: Sha,
	): void => {
		if (currentlyHoveredCommitSha !== sha) {
			if (this.props.onGraphRowHovered) {
				this.props.onGraphRowHovered(event, graphZoneType, this.processedGraphRowBySha[sha]);
			}

			this.setState({ currentlyHoveredCommitSha: sha });
		}
	};

	onClearCurrentlyHoveredGraphCommit: OnClearCurrentlyHoveredGraphCommit = (
		event: React.MouseEvent<any>,
		graphZoneType: GraphZoneType,
		sha: Sha,
		currentlyHoveredCommitSha?: Sha,
	): void => {
		if (currentlyHoveredCommitSha) {
			if (this.props.onGraphRowUnhovered) {
				this.props.onGraphRowUnhovered(event, graphZoneType, this.processedGraphRowBySha[sha]);
			}

			this.setState({ currentlyHoveredCommitSha: undefined });
		}
	};

	onGraphColumnReOrdered: OnGraphColumnReOrdered = (
		sourceGraphZoneType: GraphZoneType,
		targetGraphZoneType?: GraphZoneType,
	): void => {
		const sourceIndex: number = this.graphZoneOrdering.indexOf(sourceGraphZoneType);

		let targetIndex: number = targetGraphZoneType
			? this.graphZoneOrdering.indexOf(targetGraphZoneType)
			: this.graphZoneOrdering.length - 1;

		if (targetGraphZoneType && sourceIndex < targetIndex) {
			targetIndex -= 1;
		}

		this.graphZoneOrdering.splice(sourceIndex, 1);
		this.graphZoneOrdering.splice(targetIndex, 0, sourceGraphZoneType);

		const columnsSettingsByType: GraphColumnsSettings = {};

		this.graphZoneOrdering.forEach((graphZoneType: GraphZoneType, i: number) => {
			this.graphZonesByType[graphZoneType].order = i;
			columnsSettingsByType[graphZoneType] = this.getGraphColumnSettingFromZoneType(
				this.graphZonesByType[graphZoneType],
			);
		});

		this.setState({
			graphZones: this.initializeGraphZones(columnsSettingsByType, this.graphZoneOrdering, this.graphWidth),
		});

		if (this.props.onGraphColumnsReOrdered) {
			this.props.onGraphColumnsReOrdered(columnsSettingsByType);
		}
	};

	onGraphZoneResize: OnGraphZoneResize = debounceFrame((graphZone: GraphZone, dimensions: OnResizeParams): void => {
		if (dimensions?.width) {
			const graphZones = this.getOrderedActiveGraphZones();
			this.adjustResizedGraphZone(graphZones, graphZone, dimensions, false);
			this.setState({ graphZones: graphZones });
		}
	});

	onGraphZoneResizeEnd: OnGraphZoneResizeEnd = debounceFrame(
		(graphZone: GraphZone, dimensions: OnResizeParams): void => {
			if (dimensions?.width) {
				const graphZones = this.getOrderedActiveGraphZones();
				this.adjustResizedGraphZone(graphZones, graphZone, dimensions, true);
				this.setState({ graphZones: graphZones });
			}

			if (this.props.onColumnResized) {
				this.props.onColumnResized(
					graphZone.type,
					this.getGraphColumnSettingFromZoneType(this.graphZonesByType[graphZone.type]),
				);
			}
		},
	);

	onPopupGraphHeaderContextMenu: OnPopupGraphHeaderContextMenu = (
		event: React.MouseEvent<any>,
		width: number,
	): void => {
		if (this.props.onPopupGraphHeaderContextMenu) {
			this.props.onPopupGraphHeaderContextMenu(event, width);
		}
	};

	onSettingsClick: OnSettingsClick = (event: React.MouseEvent<any>, width: number): void => {
		if (this.props.onSettingsClick) {
			event.stopPropagation();
			this.props.onSettingsClick(event, width);
		}
	};

	onMessageChange: OnCommitMessageChange = (message: string): void => {
		this.setState({ pendingCommitMessageSummary: message });

		if (this.props.onMessageChange) {
			this.props.onMessageChange(message);
		}
	};

	onRefBeginDrag: OnRefBeginDrag = (event: React.DragEvent, sourceRefData?: RefDndData): void => {
		if (this.props.onRefBeginDrag) {
			this.props.onRefBeginDrag(event, sourceRefData);
		}
	};

	onRefCanDrag: OnRefCanDrag = (sourceRefData?: RefDndData): boolean => {
		if (this.props.onRefCanDrag) {
			return this.props.onRefCanDrag(sourceRefData);
		}

		return false;
	};

	onRefCanDrop: OnRefCanDrop = (
		event: React.DragEvent,
		sourceRefData?: RefDndData,
		targetRefData?: RefDndData,
	): boolean => {
		if (this.props.onRefCanDrop) {
			return this.props.onRefCanDrop(event, sourceRefData, targetRefData);
		}

		return false;
	};

	onRefCreate: OnRefCreate = (fullName: RefFullName, sha: Sha, refType: GraphRefType, data?: any | null): void => {
		if (this.props.onRefCreate) {
			this.props.onRefCreate(fullName, sha, refType, data);
		}
	};

	onRefCreateCancel: OnRefCreateCancel = (
		fullName: RefFullName,
		sha: Sha,
		refType: GraphRefType,
		data?: any | null,
	): void => {
		if (this.props.onRefCreateCancel) {
			this.props.onRefCreateCancel(fullName, sha, refType, data);
		}
	};

	onRefCreateContextMenu: OnRefCreateContextMenu = (
		event: React.MouseEvent<any>,
		fullName: RefFullName,
		sha: Sha,
		refType: GraphRefType,
		data?: any | null,
	): void => {
		if (this.props.onRefCreateContextMenu) {
			this.props.onRefCreateContextMenu(event, fullName, sha, refType, data);
		}
	};

	onRefDragEnter: OnRefDragEnter = (
		event: React.DragEvent,
		sourceRefData?: RefDndData,
		targetRefData?: RefDndData,
	): void => {
		if (this.props.onRefDragEnter) {
			this.props.onRefDragEnter(event, sourceRefData, targetRefData);
		}
	};

	onRefDragLeave: OnRefDragLeave = (
		event: React.DragEvent,
		sourceRefData?: RefDndData,
		targetRefData?: RefDndData,
	): void => {
		if (this.props.onRefDragLeave) {
			this.props.onRefDragLeave(event, sourceRefData, targetRefData);
		}
	};

	onRefDrop: OnRefDrop = (event: React.DragEvent, sourceRefData?: RefDndData, targetRefData?: RefDndData): void => {
		if (this.props.onRefDrop) {
			this.props.onRefDrop(event, sourceRefData, targetRefData);
		}
	};

	onRefEndDrag: OnRefEndDrag = (
		event: React.DragEvent,
		sourceRefData?: RefDndData,
		targetRefData?: RefDndData,
	): void => {
		if (this.props.onRefEndDrag) {
			this.props.onRefEndDrag(event, sourceRefData, targetRefData);
		}
	};

	onZoneRowClick(event: React.MouseEvent<any>, graphZoneType: GraphZoneType, sha: Sha): void {
		// Left Click only
		if (event.button !== 0) {
			return;
		}

		const { enableMultiSelection, shiftSelectMode } = this.props;

		if (enableMultiSelection) {
			const isMacOS: boolean = this.isMacOSPlatform();
			if ((!isMacOS && event.ctrlKey) || (isMacOS && event.metaKey)) {
				// Command commit selection
				if (shiftSelectMode === 'topological' && this.selectionAnchorSha && this.selectionAnchorSha !== sha) {
					// In topological mode, validate that this commit is topologically connected to the anchor
					const directPath = this.getDirectTopologicalPathBetween(this.selectionAnchorSha, sha);
					if (!directPath || (directPath.length === 1 && directPath[0] === sha)) {
						// No direct topological connection found, fall back to single selection
						this.selectionAnchorSha = sha;
						this.selectCommits([sha], false, false);
						return;
					}
				}

				this.selectionAnchorSha = sha;
				this.selectCommits([sha], true, false);
				return;
			}

			if (event.shiftKey) {
				// Shift commit selection
				this.selectionAnchorSha ??= this.selectionFocusSha;
				if (!this.selectionAnchorSha) {
					// First shift selection, fall back to single selection
					this.selectionAnchorSha = sha;
					this.selectCommits([sha], false, false);

					return;
				}

				if (this.selectionAnchorSha !== sha) {
					// Override shiftSelectMode to 'simple' when we are filtering commits
					const shas = this.getShiftSelectRange(
						sha,
						this.isCommitListFiltered() ? 'simple' : shiftSelectMode,
					);
					if (shas?.length) {
						// Replace selection entirely with the range
						this.selectCommits(shas, false, false);
						return;
					}
				}
			}
		}

		// Basic commit selection
		this.selectionFocusSha = sha;
		this.selectionAnchorSha = sha;
		this.selectCommits([sha], false, false);
	}

	getShiftSelectRange(currentSha: Sha, shiftSelectMode: ShiftSelectMode): Sha[] | undefined {
		if (!this.selectionAnchorSha) {
			return [currentSha];
		}

		// Validate that both SHAs exist in the processed graph rows
		if (!this.processedGraphRowBySha[this.selectionAnchorSha] || !this.processedGraphRowBySha[currentSha]) {
			return [currentSha];
		}

		if (shiftSelectMode === 'simple') {
			// Simple mode: select all commits in row range (inclusive)
			const anchorRowIndex = this.processedGraphRowBySha[this.selectionAnchorSha].rowIndex;
			const currentRowIndex = this.processedGraphRowBySha[currentSha].rowIndex;
			const shas: Sha[] = [];

			// Determine the range boundaries (inclusive)
			const startIndex = Math.min(anchorRowIndex, currentRowIndex);
			const endIndex = Math.max(anchorRowIndex, currentRowIndex);

			// Select all commits in the range (inclusive of both boundaries)
			for (let i = startIndex; i <= endIndex; i++) {
				if (this.orderedGraphRows[i]) {
					shas.push(this.orderedGraphRows[i].sha);
				}
			}

			return shas;
		}

		// Topological mode: follow parent/child relationships in the graph
		return this.getTopologicalPathBetween(this.selectionAnchorSha, currentSha);
	}

	private getTopologicalPathBetween(startSha: Sha, endSha: Sha): Sha[] | undefined {
		// First try to find a direct path
		const directPath = this.getDirectTopologicalPathBetween(startSha, endSha);
		if (directPath && (directPath.length > 1 || (directPath.length === 1 && directPath[0] !== endSha))) {
			return directPath;
		}

		// If no direct topological path found, try to find the nearest reachable commit ahead
		const nearestReachableCommit = this.findNearestTopologicallyReachableCommitAhead(startSha, endSha);
		if (nearestReachableCommit) {
			// Return the path to the nearest reachable commit instead
			return this.getDirectTopologicalPathBetween(startSha, nearestReachableCommit);
		}

		// If no reachable commit found ahead, don't change the selection
		return undefined;
	}

	private getDirectTopologicalPathBetween(startSha: Sha, endSha: Sha): Sha[] | undefined {
		if (startSha === endSha) {
			return [startSha];
		}

		const visited = new Set<Sha>();
		let foundPath: Sha[] = [];

		// Try to find a path from startSha to endSha following parent/child relationships
		const findPathToTarget = (currentSha: Sha, targetSha: Sha, currentPath: Sha[]): boolean => {
			if (currentSha === targetSha) {
				foundPath = [...currentPath, targetSha];
				return true;
			}

			if (visited.has(currentSha)) {
				return false;
			}
			visited.add(currentSha);

			const currentRow = this.processedGraphRowBySha[currentSha];
			if (!currentRow) {
				return false;
			}

			const newPath = [...currentPath, currentSha];

			// Try following parents (going back in history)
			const parents = currentRow.parents || [];
			for (const parentSha of parents) {
				if (findPathToTarget(parentSha, targetSha, newPath)) {
					return true;
				}
			}

			// Try following children (going forward in history)
			const children = this.childrenBySha[currentSha] || [];
			for (const childSha of children) {
				if (findPathToTarget(childSha, targetSha, newPath)) {
					return true;
				}
			}

			return false;
		};

		// Validate that the path only has 2 endpoints (start and end)
		if (findPathToTarget(startSha, endSha, []) && this.hasOnlyTwoEndpoints(foundPath, startSha, endSha)) {
			return foundPath;
		}

		// If no topological path found or path has too many endpoints, don't change the selection
		return undefined;
	}

	/**
	 * Finds the nearest commit ahead in time (newer commits, earlier in orderedGraphRows array) that is topologically
	 * reachable from the anchor commit. This is used as a fallback when the clicked commit
	 * is not directly topologically reachable.
	 */
	private findNearestTopologicallyReachableCommitAhead(anchorSha: Sha, targetSha: Sha): Sha | undefined {
		const targetRowIndex = this.processedGraphRowBySha[targetSha]?.rowIndex;
		if (targetRowIndex === undefined) {
			return undefined;
		}

		// Search backward through the array (toward newer commits) from the target commit's position
		for (let i = targetRowIndex - 1; i >= 0; i--) {
			const candidateSha = this.orderedGraphRows[i].sha;

			// Check if this candidate is topologically reachable from the anchor
			const path = this.getDirectTopologicalPathBetween(anchorSha, candidateSha);

			// If we found a valid path (more than just the candidate itself), this commit is reachable
			if ((path?.length ?? 0) > 1 || (path?.length === 1 && path[0] === anchorSha)) {
				return candidateSha;
			}
		}

		return undefined;
	}

	/**
	 * Validates that a path has only 2 endpoints - the start and end commits.
	 * An endpoint is a commit that has no children or parents within the selected path
	 * (excluding the explicit start and end points).
	 */
	private hasOnlyTwoEndpoints(path: Sha[], startSha: Sha, endSha: Sha): boolean {
		// Two or fewer commits always have only 2 endpoints
		if (path.length <= 2) return true;

		const pathSet = new Set(path);
		let endpointCount = 0;

		for (const sha of path) {
			const isExplicitEndpoint = sha === startSha || sha === endSha;

			// Check if this commit is an endpoint within the path context
			const currentRow = this.processedGraphRowBySha[sha];
			if (!currentRow) continue;

			const parents = currentRow.parents || [];
			const children = this.childrenBySha[sha] || [];

			// Count parents and children that are within our path
			const parentsInPath = parents.filter(parentSha => pathSet.has(parentSha)).length;
			const childrenInPath = children.filter(childSha => pathSet.has(childSha)).length;

			// A commit is an endpoint if it has no parents OR no children within the path
			// (meaning it's at one end of the selected chain)
			const isEndpoint = parentsInPath === 0 || childrenInPath === 0;

			if (isEndpoint) {
				endpointCount++;

				// If this is an endpoint but not one of our explicit endpoints, the selection is invalid
				if (!isExplicitEndpoint) return false;
			}
		}

		// We should have exactly 2 endpoints (the start and end)
		return endpointCount === 2;
	}

	onClickRef: OnClickRef = (
		event: React.MouseEvent<any>,
		refGroup: GraphRefGroup,
		sha: Sha,
		metadataItem?: RefMetadataItem,
	): void => {
		this.onZoneRowClick(event, refZone, sha);

		if (this.props.onClickGraphRef) {
			this.props.onClickGraphRef(event, refGroup, this.processedGraphRowBySha[sha], metadataItem);
		}

		this.currentSha = sha;
		this.loadSelectedGraphRow(this.currentSha);
		this.lastNavSha = null;
	};

	onClickCommit: OnClickCommit = (event: React.MouseEvent<any>, graphZoneType: GraphZoneType, sha: Sha): void => {
		this.onZoneRowClick(event, graphZoneType, sha);

		if (this.props.onClickGraphRow) {
			const graphRow: GraphRow = this.processedGraphRowBySha[sha];
			this.props.onClickGraphRow(event, graphZoneType, graphRow);
		}

		this.currentSha = sha;
		this.loadSelectedGraphRow(this.currentSha);
		this.lastNavSha = null;
	};

	onDoubleClickCommit: OnDoubleClickCommit = (
		event: React.MouseEvent<any>,
		graphZoneType: GraphZoneType,
		sha: Sha,
	): void => {
		if (this.props.onDoubleClickGraphRow) {
			const graphRow: GraphRow = this.processedGraphRowBySha[sha];
			this.props.onDoubleClickGraphRow(event, graphZoneType, graphRow);
		}
	};

	onWindowKeyUp: OnKeyUp = (event: React.KeyboardEvent<HTMLInputElement>): void => {
		switch (event.key) {
			case NamedKeys.metaKey: // Down cmd Key
				if (this.isMacOSPlatform()) {
					this.dimRowsOfSelectedCommit(false);
				}

				break;
			case NamedKeys.controlKey: // Down control key
				if (!this.isMacOSPlatform()) {
					this.dimRowsOfSelectedCommit(false);
				}

				break;
			default:
				break;
		}
	};

	onWindowBlur: OnBlur = (event: React.FocusEvent<any>): void => {
		const { hoveredRefGroup, hoveredRefZoneSha } = this.state;

		if (hoveredRefGroup && hoveredRefZoneSha) {
			this.onRefNodeUnhovered(event, hoveredRefGroup, hoveredRefZoneSha);
		}

		this.onRefZoneUnhovered();

		this.dimRowsOfSelectedCommit(false);
	};

	onWindowKeyDown: OnKeyDown = (event: React.KeyboardEvent<HTMLInputElement>): void => {
		switch (event.key) {
			case NamedKeys.metaKey: // Down cmd Key
				if (this.isMacOSPlatform()) {
					this.dimRowsOfSelectedCommit(true);
				}

				break;
			case NamedKeys.controlKey: // Down control key
				if (!this.isMacOSPlatform()) {
					this.dimRowsOfSelectedCommit(true);
				}

				break;
			default:
				if (this.state.dimRowsOfSelectedCommit) {
					this.dimRowsOfSelectedCommit(false);
				}

				break;
		}
	};

	onKeyUp: OnKeyUp = (event: React.KeyboardEvent<HTMLInputElement>): void => {
		if (this.lastNavSha && this.isNavigationKey(event.key) && this.currentSha !== this.lastNavSha) {
			this.loadSelectedGraphRow(this.currentSha);
			this.lastNavSha = null;
		}
	};

	onKeyDown: OnKeyDown = (event: React.KeyboardEvent<HTMLInputElement>): void => {
		if (this.lastNavSha === null && this.isNavigationKey(event.key)) {
			this.lastNavSha = this.currentSha;
		}

		switch (event.key) {
			case NamedKeys.arrowUpKey: // Up arrow key
				this.selectPrevious(event.shiftKey);
				break;
			case NamedKeys.arrowDownKey: // Down arrow key
				this.selectNext(event.shiftKey);
				break;
			// TODO: shortcuts keys should never be defined in the graph component itself
			// as they can break functionality of the application that consumes that component.
			// That is, the app is the one that knows how the graph will be implemented and
			// how the "h" key should work. If we want the graph component to be shareable
			// between different applications, we should avoid doing so and provide a way
			// to add shortcuts from the application to the component.
			case 'H':
			case 'h': // H key
				this.selectHead(event.shiftKey);
				break;
			default:
				break;
		}
	};

	isNavigationKey(key: string): boolean {
		const navigationKeys = [NamedKeys.arrowUpKey, NamedKeys.arrowDownKey];
		return navigationKeys.some(curKey => curKey === key);
	}

	loadSelectedGraphRow(sha: Sha): void {
		const currentRow: ProcessedGraphRow | undefined = this.processedGraphRowBySha[sha];
		if (currentRow && this.props.onLoadSelectedGraphRow) {
			this.props.onLoadSelectedGraphRow(currentRow);
		}
	}

	onRefNodeHovered: OnRefNodeHovered = (event: React.MouseEvent<any>, refGroup: GraphRefGroup, sha: Sha): void => {
		// If this is a ghost ref and it's not single-selected, we don't consider it hovered
		if (
			this.props.showGhostRefsOnRowHover &&
			!isSingleSelected(this.selectedShas, sha) &&
			!this.processedGraphRowBySha[sha]?.hasRefs
		) {
			return;
		}

		if (this.props.onGraphRefNodeHovered) {
			this.props.onGraphRefNodeHovered(event, refGroup, this.processedGraphRowBySha[sha]);
		}

		this.setState({
			hoveredRefGroup: [...refGroup],
		});
	};

	onRefNodeUnhovered: OnRefNodeUnhovered = (
		event: React.MouseEvent<any> | React.FocusEvent<any> | null,
		refGroup: GraphRefGroup,
		sha: Sha,
	): void => {
		if (this.isRefContextMenuShown) {
			return;
		}

		if (this.props.onGraphRefNodeUnhovered) {
			this.props.onGraphRefNodeUnhovered(event, refGroup, this.processedGraphRowBySha[sha]);
		}

		this.setState({
			hoveredRefGroup: undefined,
		});
	};

	onRefShorthandChange: OnRefShorthandChange = (currentShorthand: RefShorthand): void => {
		const { createRefFormData } = this.state;
		this.setState({ createRefFormData: { ...createRefFormData, shorthand: currentShorthand } });
	};

	onRefZoneHovered: OnRefZoneHovered = (sha: Sha): void => {
		this.setState({ hoveredRefZoneSha: sha });
	};

	onRefZoneUnhovered: OnRefZoneUnhovered = (): void => {
		if (!this.isRefContextMenuShown) {
			this.setState({ hoveredRefZoneSha: undefined });
		}
	};

	// TODO: Improvements: instead of using a timeout here to avoid multiple
	// calls to the `onShowMoreCommits` input property, we could modify
	// `onShowMoreCommits` to return a promise. Then, we can wait for the
	// promise to be resolved here.
	onShowMoreCommits: OnShowMoreCommits = (): void => {
		if (this.props.onShowMoreCommits && this.shouldFireShowMoreCommits) {
			this.props.onShowMoreCommits();
			this.shouldFireShowMoreCommits = false;
			setTimeout(() => {
				this.shouldFireShowMoreCommits = true;
			}, 250);
		}
	};

	onScrollForZone: OnScrollForZone = (
		graphZoneType: GraphZoneType,
		scroll: ScrollParams,
		graphWidth: number,
		graphHeight: number,
		hasMoreCommits: boolean,
	): void => {
		this.setScroll(graphZoneType, scroll);

		if (this.props.onScrollForZone) {
			this.props.onScrollForZone(graphZoneType, scroll);
		}

		this.loadMoreCommitsIfNecessary(graphHeight, hasMoreCommits);
	};

	loadMoreCommitsIfNecessary(graphHeight: number, hasMoreCommits: boolean) {
		// Lazy load more commits while scrolling when sufficiently close to the bottom
		if (
			!this.props.onShowMoreCommits ||
			!hasMoreCommits ||
			!graphHeight ||
			graphHeight < 0 ||
			!this.scrollTop ||
			this.scrollTop < 0 ||
			!this.orderedGraphRows?.length
		) {
			return;
		}

		// Must calculate from the bottom of our scroll, as graph height may vary
		const scrollBottom: number = this.scrollTop + graphHeight;
		const totalRowsHeight: number = this.orderedGraphRows.length * GRAPH_ROW_HEIGHT;
		const scrollLazyLoadThreshold: number = totalRowsHeight - GRAPH_ROW_LAZY_LOAD_COMMITS_OFFSET;

		if (scrollBottom > scrollLazyLoadThreshold && scrollBottom > this.lastLazyLoadHeight) {
			this.lastLazyLoadHeight = Math.min(scrollBottom, totalRowsHeight - 1);
			this.onShowMoreCommits();
		}
	}

	// Copied from see "onScrollToRowCausedUpdateForRefZone" in "connectedGraphContainer.jsx")
	onScrollToRowCausedUpdateForFirstColumn: OnScrollForZone = (
		graphZoneType: GraphZoneType,
		scroll: ScrollParams,
	): void => {
		const newScroll: ScrollParams = { ...scroll };

		// Adjust scrollTop when first column is the commit zone to
		// take into account the horizontal scroll height
		if (graphZoneType === commitZone && scroll.scrollTop) {
			const horizontalScrollHeight = this.getHorizontalScrollHeight(
				graphZoneType,
				this.state.cssVariablesWithDefaults,
			);
			newScroll.scrollTop -= horizontalScrollHeight + GRAPH_ROW_PADDING;
		}

		this.setScroll(graphZoneType, newScroll);
		this.props.onScrollToRowCausedUpdateForFirstColumn?.(graphZoneType, newScroll);
	};

	onCommitContextMenu: OnCommitContextMenu = (
		event: React.MouseEvent<any>,
		graphZoneType: GraphZoneType,
		sha: Sha,
	): void => {
		if (this.props.onRowContextMenu) {
			const graphRow: GraphRow = this.processedGraphRowBySha[sha];
			this.props.onRowContextMenu(event, graphZoneType, graphRow);
		}
	};

	onRefContextMenu: OnRefZoneContextMenu = (
		event: React.MouseEvent<any>,
		refGroup: GraphRefGroup,
		sha: Sha,
	): void => {
		if (this.props.onRefContextMenu) {
			this.isRefContextMenuShown = true;

			this.onRefZoneHovered(sha);

			const graphRow: GraphRow = this.processedGraphRowBySha[sha];
			this.props.onRefContextMenu(event, refGroup, graphRow);

			// Note: this is done in that way to fix issue #137 in Windows OS and Gitlens.
			// I have not found a better solution. VS Code does not provide a way to know
			// when the context menu is hidden to be able to disable/enable the hover mechanism
			// of the grouped ref and then avoid the problem with the focus in Windows.
			// This also fixes GK-3292 issue.
			setTimeout(() => {
				this.isRefContextMenuShown = false;
			}, 250);
		}
	};

	selectCommits(shas: Sha[], includeToPrevSel: boolean, isAutoOrKeyScroll: boolean): void {
		if (!includeToPrevSel) {
			for (const sha of this.selectedShas) {
				if (!shas.includes(sha)) {
					this.selectedShas.delete(sha);
				}
			}
		}

		for (const sha of shas) {
			if (includeToPrevSel && this.selectedShas.has(sha)) {
				this.selectedShas.delete(sha);
			} else if (this.processedGraphRowBySha?.[sha]) {
				this.selectedShas.add(sha);
			}
		}

		this.selectionFocusSha =
			shas.length && this.selectedShas.has(shas[shas.length - 1])
				? shas[shas.length - 1]
				: last(this.selectedShas);

		if (this.selectionFocusSha) {
			this.selectSha(this.selectionFocusSha, isAutoOrKeyScroll);
		}

		if (this.props.onSelectGraphRows) {
			const selectedShas: Sha[] = this.getSelectedShas();
			const selectedRows: GraphRow[] = selectedShas.map(
				(selectedSha: Sha) => this.processedGraphRowBySha[selectedSha],
			);
			this.props.onSelectGraphRows(selectedRows);
		}

		this.updateMarkerRowIndices(['selection']);
		this.setState({ processedRows: [...this.orderedGraphRows] });
	}

	// Copied logic from "getCommitZoneContentWidthBetweenGutters" of "GraphSelectors.js".
	getCommitZoneContentWidthBetweenGutters(): number {
		const commitZoneWidth: number = this.getCurrentWidthByZone(commitZone);
		const leftGutterWidth: number = this.getLeftGutterWidth();
		const rightGutterWidth: number = this.getRightGutterWidth();
		return commitZoneWidth - leftGutterWidth - rightGutterWidth;
	}

	getSelectedShas(): Sha[] {
		return [...this.selectedShas];
	}

	getExcludeRemotesByName(): ExcludeRemotesByName {
		const excludeRemotesByName: ExcludeRemotesByName = {};
		Object.values(this.excludeRefsById).forEach((hiddenRef: any) => {
			if (hiddenRef.type === refTypes.REMOTE && hiddenRef.owner && hiddenRef.name === '*') {
				excludeRemotesByName[hiddenRef.owner] = true;
			}
		});

		return excludeRemotesByName;
	}

	getIncludeOnlyRemotesByName(): IncludeOnlyRemotesByName {
		const includeOnlyRemotesByName: IncludeOnlyRemotesByName = {};
		Object.values(this.includeOnlyRefsById).forEach((includeRef: any) => {
			if (includeRef.type === refTypes.REMOTE && includeRef.owner && includeRef.name === '*') {
				includeOnlyRemotesByName[includeRef.owner] = true;
			}
		});

		return includeOnlyRemotesByName;
	}

	// TODO: Review that as I'm not sure if this code is needed
	// Copied logic from "GraphSagas.js" and "FileNodeListScrollSync.jsx".
	// We have to wait for the scroll to be fully consumed by the graph before we clear it out.
	// It would be "better" if we could handle the `scrollTop` ourselves instead of using `ScrollSync`
	clearScrollToIndex: ClearScrollToIndex = debounceFrame((): void => {
		if (this.isMounted) {
			this.setState({ scrollToIndex: undefined });
		}
	});

	// Returns the index of the first and last visible (partially visible counts) rows in the graph view.
	getTopAndBottomVisibleRowIndex(): TopAndBottomVisibleRowIndex {
		const { graphHeight, orderedGraphRows, scrollTop } = this;
		const height: number = graphHeight || 0;
		if (!height || !orderedGraphRows?.length) {
			return { top: -1, bottom: -1 };
		}

		// The graph height from state includes the header, so we subtract it to get the content height.
		const graphContentHeight = height - GRAPH_HEADER_ROW_HEIGHT;
		// Divide by the height of each row to get the height in rows.
		const graphContenttHeightInRows = graphContentHeight / GRAPH_ROW_HEIGHT;
		const scrollTopRowIndex: number = scrollTop / GRAPH_ROW_HEIGHT;
		const topVisibleRowIndex = Math.max(Math.floor(scrollTopRowIndex), 0);
		// We add the content height, in rows, minus 1 (otherwise we would get the first row outside of the viewport)
		const scrollBottomRowIndex = scrollTopRowIndex + (graphContenttHeightInRows - 1);
		const bottomVisibleRowIndex = Math.min(Math.ceil(scrollBottomRowIndex), orderedGraphRows.length - 1);

		return {
			top: topVisibleRowIndex,
			bottom: bottomVisibleRowIndex,
		};
	}

	getOffsetSha(sha: Sha): Sha {
		const height: number = this.graphHeight || 0;
		const scrollRowPadding: number = this.props.scrollRowPadding || 0;
		if (!scrollRowPadding || !height) {
			return sha;
		}

		let rowPadding: number = scrollRowPadding;
		if (rowPadding <= 0) {
			return sha;
		}

		const topAndBottomVisibleRowIndex: TopAndBottomVisibleRowIndex = this.getTopAndBottomVisibleRowIndex();
		const topVisibleRowIndex = topAndBottomVisibleRowIndex.top;
		const bottomVisibleRowIndex = topAndBottomVisibleRowIndex.bottom;

		if (topVisibleRowIndex === -1 || bottomVisibleRowIndex === -1) {
			return sha;
		}

		// The scroll padding should never be more than half the viewport, in rows.
		const maxRowPadding: number = Math.floor((bottomVisibleRowIndex - topVisibleRowIndex) / 2);
		if (maxRowPadding < 1) {
			return sha;
		}

		rowPadding = Math.min(rowPadding, maxRowPadding);

		const numGraphRows = this.orderedGraphRows.length;
		const rowIndex: number = this.orderedGraphRows.findIndex((graphRow: ProcessedGraphRow) => graphRow.sha === sha);
		if (!rowIndex || !this.orderedGraphRows.length) {
			return sha;
		}

		const isWithinTopEdgePadding: boolean = topVisibleRowIndex + rowPadding > rowIndex;
		const isWithinBottomEdgePadding: boolean = bottomVisibleRowIndex - rowPadding < rowIndex;

		if (isWithinTopEdgePadding) {
			return this.orderedGraphRows[Math.max(rowIndex - rowPadding, 0)].sha || sha;
		}

		if (isWithinBottomEdgePadding) {
			return this.orderedGraphRows[Math.min(rowIndex + rowPadding, numGraphRows - 1)].sha || sha;
		}

		return sha;
	}

	// Copied logic from "selectSha" of "GraphSagas.js".
	selectSha(sha: Sha, isAutoOrKeyScroll = false): void {
		const columns: number = this.processedGraphRowBySha[sha]?.column || 0;
		const scrollLeft: number = this.getScrollLeft();
		const commitZoneWidthBetweenGutters: number = this.getCommitZoneContentWidthBetweenGutters();
		const commitZoneWidth: number = this.getCurrentWidthByZone(commitZone);
		const commitZoneContentWidth: number = this.getCommitZoneContentWidth();

		const columnX: number = columns * this.graphZoneModeConstants.COMMIT_COLUMN_WIDTH;
		const columnIsLeftOfLeftGutter: boolean =
			scrollLeft - this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH / 3 <= columnX;
		const columnIsRightOfRightGutter: boolean = columnX <= scrollLeft + commitZoneWidthBetweenGutters;
		const maxScrollLeft: number = Math.max(0, commitZoneContentWidth - commitZoneWidth);

		const nextScrollLeft: number =
			columnIsLeftOfLeftGutter && columnIsRightOfRightGutter
				? scrollLeft
				: clamp(columnX - commitZoneWidthBetweenGutters / 2, 0, maxScrollLeft);

		this.setScrollToSha(isAutoOrKeyScroll ? this.getOffsetSha(sha) : sha, nextScrollLeft);

		// Copied logic from "selectSha" of "GraphSagas.js" and "FileNodeListScrollSync.jsx".
		this.setState(
			{
				scrollToAlignment: this.getScrollToAlignment(),
				scrollToIndex: this.getScrollToIndex(),
			},
			this.clearScrollToIndex,
		);
	}

	updateWidthByZone(
		widthToAssignForZone: number,
		graphZoneType: GraphZoneType,
		updatePreferredWidth?: boolean,
	): void {
		const graphZones: GraphZone[] = this.getOrderedActiveGraphZones();

		const updatedZone = this.graphZonesByType[graphZoneType];
		const updatedWidth = getClampedZoneWidth(updatedZone, graphZones, widthToAssignForZone);
		updatedZone.currentWidth = updatedWidth;

		if (graphZoneType === commitZone) {
			const maxScrollLeft: number = Math.max(0, this.getCommitZoneContentWidth() - updatedZone.currentWidth);
			if (this.getScrollLeft() > maxScrollLeft) {
				this.setScrollLeft(maxScrollLeft);
			}
		}

		if (updatePreferredWidth) {
			updatedZone.preferredWidth = updatedWidth;
		}
	}

	updateCommitZoneContentWidthFromChange(): void {
		const graphZone = this.graphZonesByType[commitZone];

		if (!graphZone.isHidden) {
			const graphZones: GraphZone[] = this.getOrderedActiveGraphZones();

			const contentWidth: number = this.getCommitZoneContentWidth();
			graphZone.contentWidth = contentWidth;
			graphZone.maximumWidth = contentWidth;
			// When changing repos or other edge cases, the content width can sometimes shrink, which means
			// that the commit zone can be too large and needs to shrink to its new max.
			if (graphZone.currentWidth > contentWidth) {
				graphZone.currentWidth = getClampedZoneWidth(graphZone, graphZones, contentWidth);
			}

			this.setState({ graphZones: graphZones });
		}
	}

	// TODO: Refactor and unify code of "getCurrentWidthByZone", "getCommitZoneWidth",
	// "graphHelper.getZoneWidth and "graphHelper.getZoneWidthWithVerticalScrollbar"
	// to avoid mistakes when changing the code in the future.
	getCurrentWidthByZone(graphZoneType: GraphZoneType, withoutVerticalScrollWidth: boolean = true): number {
		const verticalScrollWidth: number = withoutVerticalScrollWidth
			? this.getVerticalScrollWidth(commitZone, this.props.cssVariables, true)
			: 0;
		const graphZone = this.graphZonesByType[graphZoneType];
		return graphZone && !graphZone.isHidden ? graphZone.currentWidth - verticalScrollWidth : 0;
	}

	// Copied same logic than "getCommitZoneContentWidth" in "GraphSelector.js".
	// TODO: optimize this code by using a global variable that will change its
	// value only when there is a "graphRows" change (instead of calculate each
	// time we render the component)
	getCommitZoneContentWidth(): number {
		return (
			(this.maxColumns || 1) * this.graphZoneModeConstants.COMMIT_COLUMN_WIDTH +
			this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH * 2 +
			this.graphZoneModeConstants.COMMIT_ZONE_PADDING_RIGHT * 2
		);
	}

	// TODO: Refactor and unify code of "getCurrentWidthByZone", "getCommitZoneWidth",
	// "graphHelper.getZoneWidth and "graphHelper.getZoneWidthWithVerticalScrollbar"
	// to avoid mistakes when changing the code in the future.
	getCommitZoneWidth(withoutVerticalScrollWidth: boolean = true): number {
		const verticalScrollWidth: number = withoutVerticalScrollWidth
			? this.getVerticalScrollWidth(commitZone, this.props.cssVariables, true)
			: 0;

		if (this.graphZonesByType[commitZone]) {
			return this.graphZonesByType[commitZone].currentWidth - verticalScrollWidth;
		}

		const { columnsSettings } = this.props;
		if (columnsSettings?.[commitZone]) {
			return columnsSettings[commitZone].width - verticalScrollWidth;
		}

		return graphZoneMetaData[commitZone].minimumWidth - verticalScrollWidth;
	}

	// Copied same logic than "getSingleColumnModeFactor" in "GraphSelector.js".
	// Fundamentally the display is different when the graph is squeezed to one column. To make the transition smooth as
	// the user resizes, this variable lies in the range [0,1] which linearly grows as the viewport width shrinks from
	// 2 columns to 1.
	getSingleColumnModeFactor(): number {
		const maxColumn: number = this.maxColumns;
		const commitZoneWidth: number = this.getCurrentWidthByZone(commitZone);
		return maxColumn === 1
			? 0
			: clamp(
					1 -
						(commitZoneWidth -
							this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH * 2 -
							this.graphZoneModeConstants.COMMIT_ZONE_PADDING_RIGHT) /
							this.graphZoneModeConstants.COMMIT_COLUMN_WIDTH,
					0,
					1,
				);
	}

	setClientHeight(clientHeight: number): void {
		this.clientHeight = clientHeight;
	}

	getClientHeight(): number {
		return this.clientHeight;
	}

	setScroll(graphZoneType: GraphZoneType, scroll: ScrollParams): void {
		this.setClientHeight(scroll.clientHeight);
		if (scroll.scrollTop != null) {
			this.setScrollTop(scroll.scrollTop);
		}

		if (graphZoneType === commitZone) {
			if (scroll.scrollLeft != null) {
				this.setScrollLeft(scroll.scrollLeft);
			}
		}
	}

	// Copied same logic than "getNonAdjustedScrollLeft" in "GraphSelector.js".
	getNonAdjustedScrollLeft(): number {
		return this.scrollLeft;
	}

	setScrollLeft(scrollLeft: number): void {
		if (this.scrollLeft !== scrollLeft) {
			this.scrollLeft = scrollLeft;
			this.setState({ scrollLeft: scrollLeft });
		}
	}

	// Copied same logic than "getScrollLeft" in "GraphSelector.js".
	getScrollLeft(): number {
		const nonAdjustedScrollLeft: number = this.getNonAdjustedScrollLeft();
		const commitZoneContentWidth: number = this.getCommitZoneContentWidth();
		const commitZoneWidth: number = this.getCommitZoneWidth();
		return commitZoneContentWidth !== commitZoneWidth ? nonAdjustedScrollLeft : 0;
	}

	setScrollTop(scrollTop: number): void {
		if (this.scrollTop !== scrollTop) {
			this.scrollTop = scrollTop;
			this.setState({ scrollTop: scrollTop });
			this.onGraphVisibleRowsUpdatedThrottled();
		}
	}

	// Copied same logic than "getScrollTop" in "GraphSelector.js".
	getScrollTop(): number {
		return this.scrollTop;
	}

	getScrollToSha(): Sha | undefined {
		return this.scrollToSha;
	}

	setScrollToSha(scrollToSha: Sha, scrollLeft: number): void {
		this.scrollToSha = scrollToSha;
		this.setScrollLeft(scrollLeft);
		this.onGraphVisibleRowsUpdated();
	}

	// Copied same logic than "getScrollToIndex" in "GraphSelector.js".
	getScrollToIndex(): number {
		const scrollToSha: Sha | undefined = this.getScrollToSha();
		if (scrollToSha) {
			const rowIndex: number | undefined = this.processedGraphRowBySha[scrollToSha]?.rowIndex;
			return rowIndex === undefined
				? this.orderedGraphRows.findIndex(row => row.sha === scrollToSha) // This should never happen
				: rowIndex;
		}

		return -1;
	}

	// Copied same logic than "getScrollToAlignment" in "GraphSelector.js".
	getScrollToAlignment(): Alignment {
		const clientHeight: number = this.getClientHeight();
		const scrollTop: number = this.getScrollTop();
		const scrollToIndex: number = this.getScrollToIndex();
		return getScrollToAlignment(GRAPH_ROW_HEIGHT, clientHeight, scrollTop, scrollToIndex);
	}

	// Copied same logic than "getMinNodeOffset" in "GraphSelector.js".
	getMinNodeOffset(): number {
		const scrollLeft: number = this.getScrollLeft();
		const singleColumnModeFactor: number = this.getSingleColumnModeFactor();
		return (
			(1 - singleColumnModeFactor) * (scrollLeft + 2 * this.graphZoneModeConstants.COMMIT_ZONE_PADDING_LEFT) +
			singleColumnModeFactor * (scrollLeft + this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH)
		);
	}

	getHorizontalScrollHeight(
		graphZoneType: GraphZoneType,
		cssVariables: CssVariables,
		mergeDefaultCssVariables?: boolean,
	): number {
		if (!this.graphZonesByType[graphZoneType]) {
			return 0;
		}

		return getHorizontalScrollHeight(
			graphZoneType,
			this.getOrderedActiveGraphZones(),
			this.orderedGraphRows,
			cssVariables,
			mergeDefaultCssVariables,
		);
	}

	getVerticalScrollWidth(
		graphZoneType: GraphZoneType,
		cssVariables: CssVariables,
		mergeDefaultCssVariables?: boolean,
	): number {
		if (!this.graphZonesByType[graphZoneType]) {
			return 0;
		}

		return getVerticalScrollWidth(
			graphZoneType,
			this.getOrderedActiveGraphZones(),
			this.props.hasMoreCommits,
			this.props.isLoadingRows,
			this.graphHeight,
			this.orderedGraphRows,
			this.props.enabledScrollMarkerTypes?.length > 0,
			cssVariables,
			mergeDefaultCssVariables,
		);
	}

	// Copied same logic than "getMaxNodeOffset" in "GraphSelector.js".
	getMaxNodeOffset(): number {
		const commitZoneWidth: number = this.getCurrentWidthByZone(commitZone);
		const scrollLeft: number = this.getScrollLeft();
		const singleColumnModeFactor: number = this.getSingleColumnModeFactor();
		return (
			(1 - singleColumnModeFactor) *
				(commitZoneWidth +
					scrollLeft +
					(this.graphZoneModeConstants.COMMIT_ZONE_PADDING_RIGHT -
						this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH)) +
			singleColumnModeFactor * (scrollLeft + this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH)
		);
	}

	// Copied same logic than "getAbsoluteNodeLeftByColumn" in "GraphSelector.js".
	getAbsoluteNodeLeftByColumn(): AbsoluteNodeLeftByColumn {
		const absoluteNodeLeftByColumn: AbsoluteNodeLeftByColumn = {};
		for (let column = 0; column <= this.maxColumns; column += 1) {
			absoluteNodeLeftByColumn[column] =
				this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH +
				this.graphZoneModeConstants.COMMIT_COLUMN_WIDTH * column;
		}

		return absoluteNodeLeftByColumn;
	}

	// Copied same logic than "getAbsoluteNodeLeftByColumn" in "GraphSelector.js".
	getNodeOffsetByColumn(): NodeOffsetByColumn {
		const minNodeOffset: number = this.getMinNodeOffset();
		const maxNodeOffset: number = this.getMaxNodeOffset();
		const absoluteNodeLeftByColumn: AbsoluteNodeLeftByColumn = this.getAbsoluteNodeLeftByColumn();

		const nodeOffsetByColumn: NodeOffsetByColumn = {};

		Object.keys(absoluteNodeLeftByColumn).forEach((key: any) => {
			const absoluteNodeLeft: number = absoluteNodeLeftByColumn[key];
			nodeOffsetByColumn[key] = clamp(absoluteNodeLeft, minNodeOffset, maxNodeOffset);
		});

		return nodeOffsetByColumn;
	}

	// Copied same logic than "getMaxNodeLeft" in "GraphSelector.js".
	getMaxNodeLeft(): number {
		const scrollLeft: number = this.getScrollLeft();
		const viewportWidth: number = this.getCurrentWidthByZone(commitZone);
		return (
			scrollLeft +
			(viewportWidth -
				this.graphZoneModeConstants.COMMIT_COLUMN_WIDTH -
				this.graphZoneModeConstants.COMMIT_ZONE_PADDING_RIGHT)
		);
	}

	// Copied same logic than "getMinNodeLeft" in "GraphSelector.js".
	getMinNodeLeft(): number {
		const scrollLeft: number = this.getScrollLeft();
		const singleColumnModeFactor: number = this.getSingleColumnModeFactor();
		return (
			scrollLeft +
			this.graphZoneModeConstants.COMMIT_ZONE_PADDING_LEFT +
			singleColumnModeFactor * this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH
		);
	}

	// Copied same logic than "getRightGutterSlideOutFactor" in "GraphSelector.js".
	// The right gutter slides outside of view as the graph size approaches the edges of the tree.
	// While the getCommitZoneWidth width is less than commitZoneContentWidth - (gutterWidth + rightPadding), the right
	// gutter is fully visible; past the max range it slides out linearly over the next COMMIT_COLUMN_WIDTH. As the commit
	// zone goes into single column mode, the right gutter slides to half a commit column width to make room for the left
	// gutter sliding towards it. When single column mode factor is 1 the left and right gutter will meet in the middle of
	// the single column. This slide-out is measured in the range [0,1].
	getRightGutterSlideOutFactor(): number {
		const commitZoneWidth: number = this.getCurrentWidthByZone(commitZone);
		const commitZoneContentWidth: number = this.getCommitZoneContentWidth();
		const singleColumnModeFactor: number = this.getSingleColumnModeFactor();
		return (
			clamp(
				(commitZoneWidth -
					(commitZoneContentWidth -
						(this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH +
							this.graphZoneModeConstants.COMMIT_ZONE_PADDING_RIGHT))) /
					this.graphZoneModeConstants.COMMIT_COLUMN_WIDTH,
				0,
				1,
			) +
			singleColumnModeFactor / 2
		);
	}

	// Copied same logic than "getNodeOpacityByColumn" in "GraphSelector.js".
	getNodeOpacityByColumn(): NodeOpacityByColumn {
		const minNodeLeft: number = this.getMinNodeLeft();
		const maxNodeLeft: number = this.getMaxNodeLeft();
		const absoluteNodeLeftByColumn: AbsoluteNodeLeftByColumn = this.getAbsoluteNodeLeftByColumn();
		const rightGutterSlideOutFactor: number = this.getRightGutterSlideOutFactor();
		const singleColumnModeFactor: number = this.getSingleColumnModeFactor();

		const nodeOpacityByColumn: NodeOpacityByColumn = {};

		Object.keys(absoluteNodeLeftByColumn).forEach((colNumber: any) => {
			const nodeLeft: number = absoluteNodeLeftByColumn[colNumber];
			const distanceToLeftEdge =
				nodeLeft -
				(minNodeLeft - singleColumnModeFactor * this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH);
			const distanceToRightEdge =
				maxNodeLeft +
				this.graphZoneModeConstants.COMMIT_COLUMN_WIDTH -
				(nodeLeft + this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH * (1 - rightGutterSlideOutFactor));
			const nearestGutterDistance = Math.min(distanceToLeftEdge, distanceToRightEdge);

			nodeOpacityByColumn[colNumber] =
				COMMIT_NODE_MIN_ALPHA +
				(1 - COMMIT_NODE_MIN_ALPHA) *
					Math.max(
						nearestGutterDistance / (this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH / 2),
						singleColumnModeFactor,
					);
		});

		return nodeOpacityByColumn;
	}

	// Copied same logic than "getLeftGutterWidth" in "GraphSelector.js"
	getLeftGutterWidth(): number {
		const singleColumnModeFactor: number = this.getSingleColumnModeFactor();
		return (
			this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH +
			singleColumnModeFactor * (this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH / 2)
		);
	}

	// Copied same logic than "getRightGutterWidth" in "GraphSelector.js"
	getRightGutterWidth(): number {
		const rightGutterSlideOutFactor: number = this.getRightGutterSlideOutFactor();
		return (1 - rightGutterSlideOutFactor) * this.graphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH;
	}

	getBoxShadowAlphaForGutter(singleColumnModeFactor: number, gutterFactor: number): number {
		return 0.4 * (1 - Math.max(singleColumnModeFactor, gutterFactor));
	}

	// Copied same logic than "getLeftGutterBoxShadowAlpha" in "GraphSelector.js"
	getLeftGutterBoxShadowAlpha(themeOpacityFactor: number): number {
		const scrollLeft: number = this.getScrollLeft();
		const singleColumnModeFactor: number = this.getSingleColumnModeFactor();
		return (
			themeOpacityFactor *
			this.getBoxShadowAlphaForGutter(
				singleColumnModeFactor,
				clamp(1 - scrollLeft / this.graphZoneModeConstants.COMMIT_COLUMN_WIDTH, 0, 1),
			)
		);
	}

	// Copied same logic than "getRightGutterBoxShadowAlpha" in "GraphSelector.js"
	getRightGutterBoxShadowAlpha(themeOpacityFactor: number): number {
		const singleColumnModeFactor: number = this.getSingleColumnModeFactor();
		const rightGutterSlideOutFactor: number = this.getRightGutterSlideOutFactor();
		return themeOpacityFactor * this.getBoxShadowAlphaForGutter(singleColumnModeFactor, rightGutterSlideOutFactor);
	}

	updateMarkerRowIndices(types: GraphMarkerType[], nextProps?: GraphContainerProps): void {
		if (!this.isMounted) {
			return;
		}

		this.setState(({ markerRowIndices }) => {
			types.forEach(type => {
				markerRowIndices[type] = [];
			});

			if (types.includes('selection')) {
				const selectedShas = this.getSelectedShas();
				selectedShas.forEach((sha: string) => {
					if (this.processedGraphRowBySha[sha]?.rowIndex !== undefined) {
						markerRowIndices['selection'].push(this.processedGraphRowBySha[sha].rowIndex);
					}
				});
			}

			const searchMode = nextProps?.searchMode ?? this.props.searchMode;
			if (types.includes('highlights') && searchMode !== 'filter') {
				const highlightedShas =
					nextProps.highlightedShas || this.state?.highlightedShas || this.props.highlightedShas || {};
				Object.keys(highlightedShas).forEach((sha: string) => {
					if (this.processedGraphRowBySha[sha]?.rowIndex !== undefined) {
						markerRowIndices['highlights'].push(this.processedGraphRowBySha[sha].rowIndex);
					}
				});
			}

			if (types.includes('pullRequests')) {
				const refMetadataById =
					nextProps?.refMetadataById || this.state?.refMetadataById || this.props.refMetadataById || {};
				Object.keys(refMetadataById).forEach((refId: string) => {
					if (refMetadataById[refId]?.[pullRequestMetadataType]?.length > 0) {
						const sha = this.shaByRefId[refId];
						if (sha != null && this.processedGraphRowBySha[sha]?.rowIndex !== undefined) {
							markerRowIndices['pullRequests'].push(this.processedGraphRowBySha[sha].rowIndex);
						}
					}
				});
			}
		});
	}

	getMarkerColors(): GraphMarkerColors {
		const computedStyle = getComputedStyle(document.documentElement);
		const markerColors: GraphMarkerColors = {};
		Object.values(graphMarkerMetadata).forEach((marker: GraphMarkerMetadata) => {
			const color: string | undefined = computedStyle.getPropertyValue(marker.colorCssKey).trim();
			if (color) {
				markerColors[marker.type] = color;
			}
		});

		return markerColors;
	}

	initializeGraphZones(
		zoneSettings: GraphColumnsSettings | undefined,
		zoneOrdering: GraphZoneType[],
		width: number,
	): GraphZone[] {
		this.graphZonesByType = {};
		const activeGraphZones: GraphZone[] = [];
		let totalWidth = 0;

		zoneOrdering.forEach((zoneType: GraphZoneType, i: number) => {
			const zoneSettingsForType: GraphColumnSetting | undefined = zoneSettings
				? zoneSettings[zoneType]
				: undefined;
			const zoneMetaDataForType = graphZoneMetaData[zoneType];
			const initialZoneWidth: number = zoneSettingsForType?.width
				? zoneSettingsForType.width
				: zoneMetaDataForType.minimumWidth;

			const zone: GraphZone = {
				...zoneMetaDataForType,
				type: zoneType,
				currentWidth: initialZoneWidth,
				preferredWidth: initialZoneWidth,
				isHidden: zoneSettingsForType?.isHidden || false,
				mode: zoneSettingsForType?.mode as GraphColumnMode | undefined,
				order:
					zoneMetaDataForType.isCustomizable && zoneSettingsForType?.order !== undefined
						? zoneSettingsForType?.order
						: i,
			};

			if (!zoneSettingsForType?.isHidden) {
				totalWidth += zone.currentWidth;
			}

			if (zoneType === commitZone) {
				zone.contentWidth = this.getCommitZoneContentWidth();
				zone.minimumWidth = this.graphZoneModeConstants.COMMIT_ZONE_VIEWPORT_WIDTH_MIN;
				zone.showIconWidth = this.graphZoneModeConstants.COMMIT_ZONE_SHOW_ICON_WIDTH;
				if (this.maxColumns > 0) {
					zone.maximumWidth = zone.contentWidth;
				}
			}

			this.graphZonesByType[zoneType] = zone;

			if (!zone?.isHidden) {
				activeGraphZones.push(zone);
			}
		});

		const lastVisibleZone = activeGraphZones[activeGraphZones.length - 1];
		if (lastVisibleZone) {
			const scrollbarThickness: number = getScrollThickness(this.props.cssVariables, true);
			const paddedMinWidth = lastVisibleZone.minimumWidth + scrollbarThickness;
			if (lastVisibleZone.maximumWidth == null || lastVisibleZone.maximumWidth >= paddedMinWidth) {
				lastVisibleZone.minimumWidth = paddedMinWidth;
				if (lastVisibleZone.showIconWidth != null) {
					lastVisibleZone.showIconWidth += scrollbarThickness;
				}
				activeGraphZones[activeGraphZones.length - 1] = lastVisibleZone;
			}
		}

		// The widths from settings may be over the maximum width, so we need to adjust them.
		activeGraphZones.forEach((zone: GraphZone) => {
			const clampedZoneWidth = getClampedZoneWidth(zone, activeGraphZones, zone.currentWidth);
			if (clampedZoneWidth !== zone.currentWidth) {
				totalWidth -= zone.currentWidth;
				zone.currentWidth = clampedZoneWidth;
				totalWidth += clampedZoneWidth;
			}
		});

		// Adjust widths that came in from settings so that we fit in the viewport, to the best of our ability.
		while (totalWidth > width) {
			const lastShrinkableGraphZone: GraphZone | undefined = getLastShrinkableGraphZone(activeGraphZones);
			if (!lastShrinkableGraphZone) {
				break;
			}

			const shrinkableZoneOldWidth: number = lastShrinkableGraphZone.currentWidth;
			const excessWidth: number = totalWidth - width;
			const shrinkWidth: number = Math.max(
				lastShrinkableGraphZone.minimumWidth,
				shrinkableZoneOldWidth - excessWidth,
			);

			const widthDelta: number = shrinkableZoneOldWidth - shrinkWidth;
			this.graphZonesByType[lastShrinkableGraphZone.type].currentWidth = shrinkWidth;
			totalWidth -= widthDelta;
		}

		// Extend the last column to fit the viewport if necessary.
		if (totalWidth < width) {
			// Adjust width for final zone based on graph width
			const lastZoneType = zoneOrdering[zoneOrdering.length - 1];
			const remainingWidth: number = width - getZoneWidthsTotal(activeGraphZones, lastZoneType);
			this.graphZonesByType[lastZoneType].currentWidth = remainingWidth;
		}

		return activeGraphZones;
	}

	maybeSortGraphZoneOrderingFromSettings(zoneSettings?: GraphColumnsSettings) {
		const allZoneOrdersInSettings = this.graphZoneOrdering.every((zoneType: GraphZoneType) => {
			return zoneSettings?.[zoneType]?.order !== undefined;
		});

		if (!allZoneOrdersInSettings) {
			return;
		}

		this.graphZoneOrdering.sort((a: GraphZoneType, b: GraphZoneType) => {
			if (zoneSettings[a].order < zoneSettings[b].order) {
				return -1;
			}

			if (zoneSettings[a].order > zoneSettings[b].order) {
				return 1;
			}

			return 0;
		});
	}

	updateZonesFromSettings(zoneSettings?: GraphColumnsSettings): void {
		let newlyVisibleZone = false;
		this.graphZoneOrdering.forEach((graphZoneType: GraphZoneType, i: number) => {
			const graphZone: GraphZone = this.graphZonesByType[graphZoneType];
			const updatedSettingsForType = zoneSettings?.[graphZone.type];
			if (updatedSettingsForType) {
				if (updatedSettingsForType.isHidden !== graphZone.isHidden) {
					newlyVisibleZone = true;

					if (graphZone.type === changesZone && !updatedSettingsForType.isHidden) {
						this.updateRowStatsConstraints();
					}
				}

				graphZone.isHidden = updatedSettingsForType.isHidden;
				graphZone.order =
					graphZone.isCustomizable && updatedSettingsForType?.order !== undefined
						? updatedSettingsForType?.order
						: i;

				graphZone.mode = updatedSettingsForType.mode as GraphColumnMode | undefined;

				graphZone.preferredWidth = updatedSettingsForType.width ?? graphZone.preferredWidth;
				graphZone.currentWidth = graphZone.preferredWidth;
			}
		});

		// If a zone was made visible, we should shrink all zones to minimum width and later have them expand to fit
		// rather than shrinking, as this would minimize the width of the new column if it is the last one.
		if (newlyVisibleZone) {
			this.graphZoneOrdering.forEach((graphZoneType: GraphZoneType) => {
				const zone: GraphZone = this.graphZonesByType[graphZoneType];
				zone.currentWidth = zone.minimumWidth;
			});
		}
	}

	// Copied from "GraphSelector.js"
	getEdgeColumnMaxes(sha: Sha): number {
		let max = 0;
		const rowEdges: RowEdges = this.processedGraphRowBySha[sha]?.edges || {};

		// edges _must_ be a plain object, take care with immutability
		Object.keys(rowEdges).forEach((key: string) => {
			max = Math.max(max, parseInt(key, 10));
		});

		return max;
	}

	// Copied from "GraphSelector.js"

	getEndingAndPassThroughEdgesByColumnFromPrevRow(graphRow: ProcessedGraphRow, prevRowEdges: RowEdges): RowEdges {
		const result: RowEdges = {};

		let edges: RowEdge;
		let originalPassThrough: Edge | undefined;
		let originalStarting: Edge | undefined;
		let nextEdge: Edge | undefined;

		// prevRow _must_ be a plain object, take care with immutability
		Object.keys(prevRowEdges).forEach((column: any) => {
			edges = prevRowEdges[column];
			originalPassThrough = edges.passThrough;
			originalStarting = edges.starting;

			if (originalPassThrough && !hasPendingChanges(originalPassThrough.type)) {
				nextEdge = originalPassThrough;
			} else if (originalStarting && !hasPendingChanges(originalStarting.type)) {
				nextEdge = originalStarting;
			} else {
				nextEdge = originalPassThrough || originalStarting || undefined;
			}

			if (nextEdge) {
				if (nextEdge.parentSha === graphRow.sha) {
					result[column] = {
						ending: nextEdge,
					};
				} else {
					result[column] = {
						passThrough: nextEdge,
					};
				}
			}
		});

		return result;
	}

	// Copied from "GraphSelector.js"
	getFinalEdgeStateForGraphAndRow(graphRow: ProcessedGraphRow, prevRowEdges: RowEdges): RowEdges {
		const endingAndPassThroughEdgesByColumn: RowEdges = this.getEndingAndPassThroughEdgesByColumnFromPrevRow(
			graphRow,
			prevRowEdges,
		);
		const startingEdgesByColumn: EdgeByColumn = this.getStartingEdgesByColumn(graphRow);
		let endingAndPassThroughEdges: RowEdge;

		// This merges all of the starting edges for this row into the endingAndPassThroughEdgesByColumn object at the
		// key of `[column].starting`
		// startingEdgesByColumn _must_ be a plain object, take care with immutability
		Object.keys(startingEdgesByColumn).forEach((key: any) => {
			if (!endingAndPassThroughEdgesByColumn[key]) {
				endingAndPassThroughEdgesByColumn[key] = {
					ending: undefined,
					passThrough: undefined,
					starting: undefined,
				};
			}

			endingAndPassThroughEdges = endingAndPassThroughEdgesByColumn[key];
			endingAndPassThroughEdges.starting = startingEdgesByColumn[key];
		});

		return endingAndPassThroughEdgesByColumn;
	}

	// Copied "getEdgesBySha" from "GraphSelector.js"
	loadEdgesBySha(): void {
		this.maxColumns = 0;
		if (this.isCommitListFiltered()) {
			return;
		}

		this.orderedGraphRows.forEach((currentRow: ProcessedGraphRow, i: number) => {
			const prevRow: ProcessedGraphRow | typeof undefined = this.orderedGraphRows[i - 1];
			const prevRowEdges: RowEdges =
				prevRow && this.processedGraphRowBySha[prevRow.sha]?.edges
					? this.processedGraphRowBySha[prevRow.sha].edges
					: {};
			this.processedGraphRowBySha[currentRow.sha].edges = this.getFinalEdgeStateForGraphAndRow(
				currentRow,
				prevRowEdges,
			);
			const currentMax: number = this.getEdgeColumnMaxes(currentRow.sha);
			this.processedGraphRowBySha[currentRow.sha].edgeColumnMaxes = currentMax;
			if (currentMax > this.maxColumns) {
				this.maxColumns = currentMax;
			}
		});
	}

	processRows(graphRows?: GraphRow[]): void {
		// IMPORTANT: The library assumes the graph rows are date-ordered with WIP rows coming in first
		// from the client, or its rendering of graph nodes will fail.
		// The loading order here is important
		this.loadRowsbySha(graphRows || []);
		this.loadEdgesBySha();
	}

	// Copied from "GraphSelector.js"
	getStartingEdgesByColumn(currentRow: ProcessedGraphRow): EdgeByColumn {
		const result: EdgeByColumn = {};
		const parentShas: Sha[] = currentRow.parents;

		if (parentShas.length > 0) {
			const type: CommitType = currentRow.type;
			const childColumn: number | typeof undefined = this.processedGraphRowBySha[currentRow.sha]?.column;

			if (childColumn !== undefined) {
				result[childColumn] = {
					parentSha: parentShas[0],
					type: type,
				};
			}

			for (let i = 1; i < parentShas.length; i += 1) {
				const parentSha: Sha = parentShas[i];
				const parentColumn: number | typeof undefined = this.processedGraphRowBySha[parentSha]?.column;

				if (parentColumn !== undefined) {
					result[parentColumn] = {
						parentSha: parentSha,
						type: type,
					};
				}
			}
		}

		return result;
	}

	formatCommitDateTimeCallback: OnFormatCommitDateTime = (
		commitDateTime: number,
		source?: CommitDateTimeSource,
	): string => {
		if (this.props.formatCommitDateTime) {
			return this.props.formatCommitDateTime(commitDateTime, source);
		}

		return formatDate(commitDateTime, TIMESTAMP_FORMAT_DATE_TIME);
	};

	formatCommitMessageCallback: FormatCommitMessage = (commitMessage: string): string => {
		return this.props.formatCommitMessage ? this.props.formatCommitMessage(commitMessage) : commitMessage;
	};

	formatRefShorthandCallback: FormatRefShorthand = (
		shorthand: RefShorthand,
		sha: Sha,
		refType: GraphRefType,
		data?: any | null,
	): RefFullName => {
		if (this.props.formatRefShorthand) {
			return this.props.formatRefShorthand(shorthand, sha, refType, data);
		}

		return shorthand.replace(/\s/g, '');
	};

	isRefShorthandValidCallback: IsRefShorthandValid = (
		shorthand: RefShorthand,
		sha: Sha,
		refType: GraphRefType,
		data?: any | null,
	): boolean => {
		if (this.props.isRefShorthandValid) {
			return this.props.isRefShorthandValid(shorthand, sha, refType, data);
		}

		return true;
	};

	translateCallback: TranslationFn = (key: string, ...formatArgs: any): string => {
		if (this.props.translate) {
			return this.props.translate(key, ...formatArgs);
		}

		return DEFAULT_TRANSLATIONS[key] ? formatString(DEFAULT_TRANSLATIONS[key], formatArgs) : key; // No translation found
	};

	getIconCallback: GetExternalIcon = (key: ExternalIconKeys): ReactElement<any> => {
		const icon = this.props.getExternalIcon?.(key);
		if (icon) {
			return icon;
		}

		// TODO: Add default icon
		return <span />;
	};

	onMissingAvatar: GetMissingAvatar = (email: string, sha: string): void => {
		if (!this.props.onEmailsMissingAvatarUrls || this.requestedMissingAvatars[email]) return;

		this.pendingMissingAvatars[email] = sha;

		if (this.missingAvatarsTimer) {
			clearTimeout(this.missingAvatarsTimer);
			this.missingAvatarsTimer = undefined;
		}

		this.missingAvatarsTimer = setTimeout(() => {
			if (!this.isMounted) {
				return;
			}

			const missing = { ...this.pendingMissingAvatars };
			this.pendingMissingAvatars = {};

			const emails = Object.keys(missing);
			if (emails.length) {
				for (const email of emails) {
					this.requestedMissingAvatars[email] = true;
				}

				this.props.onEmailsMissingAvatarUrls?.(missing);
			}
		}, 100);
	};

	onMissingRefMetadata: GetMissingRefMetadata = (id: string, types: RefMetadataType[]): void => {
		if (!this.props.onRefsMissingMetadata || this.requestedMissingRefsMetadata[id]) return;

		// Append the types to the existing list of types in this.pendingMissingRefsMetadata for this id,
		// taking care not to have duplicates.
		const existingTypes = this.pendingMissingRefsMetadata[id] || [];
		const newTypes = types.filter(type => !existingTypes.includes(type));
		this.pendingMissingRefsMetadata[id] = [...existingTypes, ...newTypes];

		if (this.missingRefsMetadataTimer) {
			clearTimeout(this.missingRefsMetadataTimer);
			this.missingRefsMetadataTimer = undefined;
		}

		this.missingRefsMetadataTimer = setTimeout(() => {
			if (!this.isMounted) {
				return;
			}

			const missing = { ...this.pendingMissingRefsMetadata };
			this.pendingMissingRefsMetadata = {};

			const ids = Object.keys(missing);
			if (ids.length) {
				for (const id of ids) {
					this.requestedMissingRefsMetadata[id] = true;
				}

				this.props.onRefsMissingMetadata?.(missing);
			}
		}, 100);
	};

	// Copied from "CommitSagas.js" file
	selectNext(topological?: boolean): void {
		// TODO: Not needed for version 1. But we will have to investigate that to see if want this
		// functionality in the future.
		// if ((yield select(getIsInteractiveRebaseInProgress)) && selectedShas.length === 1) {
		//   const shas = yield select(getPendingInteractiveRebaseCommitShas);
		//   const currentIndex = fp.indexOf(fp.first(selectedShas), shas);
		//   const nextIndex = Math.max(0, currentIndex - 1);
		//   return yield context.call(selectCommit, shas[nextIndex]);
		// }

		let shaToSelect: Sha | undefined;
		if (topological && this.searchMode === 'normal' && this.selectionFocusSha) {
			const graphRow: ProcessedGraphRow = this.processedGraphRowBySha[this.selectionFocusSha];
			if (graphRow?.parents?.length) {
				// Choose the first parent, i.e. the one most likely to be in the same lane.
				shaToSelect = graphRow.parents[0];
			}
		} else {
			const currentIndex: number = this.selectionFocusSha
				? this.orderedGraphRows.findIndex(
						(graphRow: ProcessedGraphRow) => graphRow.sha === this.selectionFocusSha,
					)
				: -1;
			const nextIndex = Math.min(currentIndex + 1, this.orderedGraphRows.length - 1);
			shaToSelect = this.orderedGraphRows[nextIndex]?.sha;
		}

		this.currentSha = shaToSelect || null;

		if (shaToSelect) {
			this.selectCommits([shaToSelect], false, true);
			// Reset shift selection anchor for keyboard navigation
			this.selectionAnchorSha = shaToSelect;
		}
	}

	// Copied from "CommitSagas.js" file
	selectPrevious(topological?: boolean): void {
		// TODO: Not needed for version 1. But we will have to investigate that to see if want this
		// functionality in the future.
		// if ((yield select(getIsInteractiveRebaseInProgress)) && selectedShas.length === 1) {
		//   const shas = yield select(getPendingInteractiveRebaseCommitShas);
		//   const currentIndex = fp.indexOf(fp.first(selectedShas), shas);
		//   const nextIndex = Math.min(currentIndex + 1, shas.length - 1);
		//   return yield context.call(selectCommit, shas[nextIndex]);
		// }

		let shaToSelect: Sha | undefined;
		if (topological && this.searchMode === 'normal' && this.selectionFocusSha) {
			const children = this.childrenBySha[this.selectionFocusSha];
			if (children?.length) {
				// Choose first non-stash child if available, i.e. the one most likely to be in the same lane.
				// If none available, just choose the first child.
				for (let i = 0; i < children.length; i += 1) {
					if (i === children.length - 1) {
						shaToSelect = children[i];
					} else if (this.processedGraphRowBySha[children[i]]?.type !== stashNodeType) {
						shaToSelect = children[i];
						break;
					}
				}
			}
		} else {
			const currentIndex: number = this.selectionFocusSha
				? this.orderedGraphRows.findIndex(
						(graphRow: ProcessedGraphRow) => graphRow.sha === this.selectionFocusSha,
					)
				: -1;
			const nextIndex: number = Math.max(-1, currentIndex - 1);
			shaToSelect = this.orderedGraphRows[nextIndex]?.sha;
			if (nextIndex < 0) {
				shaToSelect = this.orderedGraphRows[0]?.sha;
				// TODO: Not needed for version 1. But we will have to investigate that to see if want this
				// functionality in the future.
				// if (yield select(getHasUnresolvedConflicts)) {
				//   shaToSelect = mergeConflictNodeType;
				// } else if (yield select(getHasChanges)) {
				//   shaToSelect = workDirType;
				// }
			}
		}

		this.currentSha = shaToSelect || null;

		if (shaToSelect) {
			this.selectCommits([shaToSelect], false, true);
			// Reset shift selection anchor for keyboard navigation
			this.selectionAnchorSha = shaToSelect;
		}
	}

	selectHead(upstream?: boolean): void {
		let shaToSelect: Sha | null = null;

		if (upstream && this.headUpstreamSha) {
			shaToSelect = this.headUpstreamSha;
		} else if (this.headSha) {
			shaToSelect = this.headSha;
		}

		this.currentSha = shaToSelect || null;

		if (shaToSelect) {
			this.selectCommits([shaToSelect], false, true);
			// Reset shift selection anchor for keyboard navigation
			this.selectionAnchorSha = shaToSelect;
		}
	}

	getAvailableColumnAndUseIt(): number {
		let column = 0;
		while (this.columnsUsed[column]) {
			column += 1;
		}

		this.columnsUsed[column] = true;
		return column;
	}

	// Copied function "loadColumnsBySha" from "GraphSelector.js"
	getColumns(graphRow: GraphRow): number {
		if (this.hasMergeNodeChildBySha[graphRow.sha]) {
			// eslint-disable-next-line @typescript-eslint/no-dynamic-delete
			delete this.hasMergeNodeChildBySha[graphRow.sha];
		}

		const parents: Sha[] = graphRow.parents || [];

		const lastColumnsToFree: number[] | undefined = this.columnsToFreeWhenFound[graphRow.sha];
		if (lastColumnsToFree !== undefined) {
			// We use for of here for performance reasons, take care with immutability
			// eslint-disable-next-line @typescript-eslint/prefer-for-of
			for (let i = 0; i < lastColumnsToFree.length; i += 1) {
				// TODO@eamodio we should avoid `delete` here -- it will hurt performance
				// eslint-disable-next-line @typescript-eslint/no-dynamic-delete
				delete this.columnsUsed[lastColumnsToFree[i]];
			}
		}

		let column = 0;

		// if this commit has already had a column reserved use the reserved column
		const reserverInfo = this.reserverInfoBySha[graphRow.sha];
		if (reserverInfo?.column !== undefined) {
			column = reserverInfo.column;
			// eslint-disable-next-line @typescript-eslint/no-dynamic-delete
			delete this.reserverInfoBySha[graphRow.sha];
		} else {
			// otherwise get the next available column
			column = this.getAvailableColumnAndUseIt();
		}

		let parentSha: Sha;
		let reserverParentInfo: ReserverInfo | undefined;

		for (let index = 0; index < parents.length; index += 1) {
			parentSha = parents[index];
			if (graphRow.type === mergeNodeType) {
				this.hasMergeNodeChildBySha[parentSha] = true;
			}
			// If it's the first parent and it's been reserved a column and the column is different from the child's column
			reserverParentInfo = this.reserverInfoBySha[parentSha];
			if (index === 0 && reserverParentInfo?.column !== undefined && reserverParentInfo?.column !== column) {
				// If a stash node reserved the column, and we are on a commit chain older than that stash node,
				// overwrite the reservation with the current commit's column.
				const newColumnsToFree: number[] = this.columnsToFreeWhenFound[parentSha] || [];
				const stashReserved =
					reserverParentInfo?.type === stashNodeType &&
					graphRow.type !== stashNodeType &&
					reserverInfo?.newestDate > reserverParentInfo?.newestDate;
				if ((reserverParentInfo?.column > column || stashReserved) && !this.hasMergeNodeChildBySha[parentSha]) {
					this.reserverInfoBySha[parentSha] = {
						type: graphRow.type,
						newestDate: reserverInfo?.newestDate,
						column: column,
					};

					newColumnsToFree.push(reserverParentInfo.column);
					// Otherwise, be sure to free the column when the parent is processed
				} else {
					newColumnsToFree.push(column);
				}

				this.columnsToFreeWhenFound[parentSha] = newColumnsToFree;
			} else if (reserverParentInfo?.column === undefined) {
				// if we haven't reserved a column for the parent first parent gets the current column, second
				// gets the next available column.
				// Pass the reserver date down the chain if we were reserved this column.
				this.reserverInfoBySha[parentSha] = {
					type: graphRow.type,
					newestDate: reserverInfo?.column === column ? reserverInfo?.newestDate : graphRow.date,
					column: index === 0 ? column : this.getAvailableColumnAndUseIt(),
				};
			}
		}

		return column;
	}

	getFilteredHeadsForGraphRow(row: GraphRow, hasIncludes: boolean): Head[] {
		// We always show the current head, even if excluded, so check that first.
		// If we have an include only list, it overrides all other filter lists. Simply check if the id is in the list.
		// If we have no include only list, check first if we are excluding all heads as a type.
		// Otherwise, check if the id is in the exclude list.
		return row.heads.filter(
			ref =>
				ref.isCurrentHead ||
				(hasIncludes
					? this.includeOnlyRefsById[getRefIdByBaseRef(refTypes.HEAD, ref)] !== undefined
					: !this.excludeByType.heads &&
						this.excludeRefsById[getRefIdByBaseRef(refTypes.HEAD, ref)] === undefined),
		);
	}

	getFilteredRemotesForGraphRow(row: GraphRow, hasIncludes: boolean): Remote[] {
		// If we have an include only list, it overrides all other filter lists. Simply check if the id is in the list.
		// The include only list can include * values - use includeOnlyRemotesByName to check the remote owner.
		// If we have no include only list, check first if we are excluding all remotes as a type.
		// Otherwise, check if the id is in the exclude list.
		// The exclude list can include * values - use excludeRemotesByName to check the remote owner.
		return row.remotes.filter(ref =>
			hasIncludes
				? this.includeOnlyRefsById[getRefIdByBaseRef(refTypes.REMOTE, ref)] !== undefined ||
					!ref.owner ||
					this.includeOnlyRemotesByName[ref.owner] !== undefined
				: (!this.excludeByType.remotes ||
						(ref.owner && this.downstreamsByUpstream[`${ref.owner}/${ref.name}`]?.length > 0)) &&
					this.excludeRefsById[getRefIdByBaseRef(refTypes.REMOTE, ref)] === undefined &&
					(!ref.owner || this.excludeRemotesByName[ref.owner] === undefined),
		);
	}

	getFilteredTagsForGraphRow(row: GraphRow, hasIncludes: boolean, rowHasVisibleChild: boolean): Tag[] {
		// 1. If there is an include-only list which has at least one tag, check if the id is in the list.
		// 2. Otherwise, if tags are excluded, return an empty array.
		// 3. Otherwise, check if the id is not in the exclude list and that there are no includes or
		//    that there are includes and the row has at least one filtered branch or remote.
		const hasIncludesWithTags =
			hasIncludes &&
			Object.values(this.includeOnlyRefsById).some((refOptData: any) => refOptData.type === refTypes.TAG);
		const hasIncludesWithFilteredBranches =
			hasIncludes &&
			(this.getFilteredHeadsForGraphRow(row, hasIncludes).length > 0 ||
				this.getFilteredRemotesForGraphRow(row, hasIncludes).length > 0);

		return row.tags.filter(ref =>
			hasIncludesWithTags
				? this.includeOnlyRefsById[getRefIdByBaseRef(refTypes.TAG, ref)] !== undefined
				: !this.excludeByType.tags &&
					this.excludeRefsById[getRefIdByBaseRef(refTypes.TAG, ref)] === undefined &&
					(!hasIncludes || hasIncludesWithFilteredBranches || rowHasVisibleChild),
		);
	}

	loadRowsbySha(graphRows: GraphRow[]): void {
		this.orderedGraphRows = [];
		this.processedGraphRowBySha = {};
		const isFilteredCommitList = this.isCommitListFiltered();
		this.childrenBySha = {};
		this.shaByRefId = {};

		// Following variables are used in "getColumns" function
		this.reserverInfoBySha = {};
		this.columnsToFreeWhenFound = {};
		this.columnsUsed = {};

		const childRefCollectionBySha: Map<Sha, ChildRefCollection> = new Map();
		const isExcludedBySha: IsExcludedBySha = {};

		const headMarkerRowIndices: number[] = [];
		const upstreamMarkerRowIndices: number[] = [];
		this.branchUpstreamRowIndices = [];
		const localBranchMarkerRowIndices: number[] = [];
		const remoteBranchMarkerRowIndices: number[] = [];
		const tagMarkerRowIndices: number[] = [];
		const stashMarkerRowIndices: number[] = [];

		// Used to indicate that a node has children that cannot be hidden (stashes, for example)
		const hasSpecialChildBySha: HasSpecialChildBySha = {};

		if (
			workDirStatsHaveChanges(this.workDirStats) &&
			graphRows.length > 0 &&
			!isWorkDirNodeType(graphRows[0].type)
		) {
			const headRefSha: Sha | undefined = getHeadRefShaFromGraphRows(graphRows);
			const workDirRow: GraphRow = {
				sha: workDirType,
				parents: headRefSha ? [headRefSha] : [],
				author: '',
				email: '',
				date: new Date().getTime(),
				message: this.translateCallback('Graph-WorkInProgress'),
				type: workDirType,
				heads: [],
				remotes: [],
				tags: [],
			};

			if (this.workDirStats.context) {
				workDirRow.contexts = { row: this.workDirStats.context };
			}

			graphRows.unshift(workDirRow);
		} else if (
			!workDirStatsHaveChanges(this.workDirStats) &&
			graphRows.length > 0 &&
			isWorkDirNodeType(graphRows[0].type)
		) {
			graphRows.shift();
		}

		const hasIncludes = Object.keys(this.includeOnlyRefsById).length > 0;

		graphRows.forEach((row: GraphRow, i: number) => {
			const hasChildren: boolean = this.childrenBySha[row.sha]?.length > 0;
			const heads = row.heads?.length > 0 ? this.getFilteredHeadsForGraphRow(row, hasIncludes) : [];
			const remotes = row.remotes?.length > 0 ? this.getFilteredRemotesForGraphRow(row, hasIncludes) : [];
			const tags =
				row.tags?.length > 0
					? this.getFilteredTagsForGraphRow(
							row,
							hasIncludes,
							Boolean(hasSpecialChildBySha[row.sha] || hasChildren),
						)
					: [];

			const parents = row.parents?.length > 0 ? row.parents.filter(sha => !isExcludedBySha[sha]) : [];

			const hasRefs: boolean = heads.length > 0 || remotes.length > 0 || tags.length > 0;
			const hasChildRefsForParents: boolean = heads.length > 0 || remotes.length > 0;

			if ((row.type !== commitNodeType && row.type !== mergeNodeType) || hasSpecialChildBySha[row.sha]) {
				if (row.type === stashNodeType && this.excludeByType.stashes) {
					isExcludedBySha[row.sha] = true;
				}

				if (!isExcludedBySha[row.sha] && parents.length > 0) {
					let parentSha;
					for (parentSha of parents) {
						hasSpecialChildBySha[parentSha] = true;
					}
				}
			} else if (
				(row.type === commitNodeType || row.type === mergeNodeType) &&
				!hasSpecialChildBySha[row.sha] &&
				!hasRefs &&
				!hasChildren
			) {
				isExcludedBySha[row.sha] = true;
			}

			const isRowHidden = isExcludedBySha[row.sha];
			let column = isRowHidden ? 0 : this.getColumns(row);
			let columnForColoring = undefined;
			if (isFilteredCommitList) {
				columnForColoring = column;
				column = 0;
			}

			const processedGraphRow: ProcessedGraphRow = new ProcessedGraphRowObj(
				this,
				{
					...row,
					heads: heads,
					remotes: remotes,
					tags: tags,
					parents: parents,
				},
				column,
				undefined,
				undefined,
				undefined,
				undefined,
				columnForColoring,
			);

			// if the row is hidden, we don't need to calculate child refs nor add the processed row to the ordered rows array
			if (!isRowHidden) {
				let bestChildRefData: ChildRefData | undefined;
				const childRefCollection = childRefCollectionBySha.get(row.sha);
				if (childRefCollection?.length) {
					// Sort this row's childRefCollection based on parentIndexScore, lowest-first
					childRefCollection.sort((a: ChildRefData, b: ChildRefData) => {
						if (a.parentIndexScore < b.parentIndexScore) {
							return -1;
						}

						if (a.parentIndexScore > b.parentIndexScore) {
							return 1;
						}

						if (a.parentIndexScore === b.parentIndexScore) {
							// Break ties by choosing the child ref data further up
							if (a.rowEntered < b.rowEntered) {
								return -1;
							}

							if (a.rowEntered > b.rowEntered) {
								return 1;
							}
						}

						return 0;
					});

					bestChildRefData = childRefCollection[0];

					processedGraphRow.childRefs = {
						heads: [...(bestChildRefData?.heads || [])],
						remotes: [...(bestChildRefData?.remotes || [])],
						tags: [...(bestChildRefData?.tags || [])],
					};

					childRefCollectionBySha.delete(row.sha);
				}

				for (let j = 0; j < parents.length; j += 1) {
					const parentSha: Sha = parents[j];
					if (!this.childrenBySha[parentSha]) {
						this.childrenBySha[parentSha] = [];
					}

					this.childrenBySha[parentSha].push(row.sha);

					let parentChildRefCollection = childRefCollectionBySha.get(parentSha);

					// Pass the child ref data down to the row's parents
					if (hasChildRefsForParents || bestChildRefData) {
						if (parentChildRefCollection === undefined) {
							parentChildRefCollection = [];
							childRefCollectionBySha.set(parentSha, parentChildRefCollection);
						}
					}

					if (hasChildRefsForParents) {
						parentChildRefCollection?.unshift({
							sha: row.sha,
							parentIndexScore: j,
							rowEntered: i,
							heads: heads,
							remotes: remotes,
							tags: tags,
						});
					} else if (bestChildRefData) {
						parentChildRefCollection?.unshift({
							...bestChildRefData,
							parentIndexScore: j,
							rowEntered: i,
						});
					}
				}

				const rowIndex = this.orderedGraphRows.length;
				processedGraphRow.rowIndex = rowIndex;
				if (heads.length > 0) {
					localBranchMarkerRowIndices.push(rowIndex);
					if (heads.some(head => head.isCurrentHead)) {
						this.headSha = processedGraphRow.sha;
						headMarkerRowIndices.push(rowIndex);
					}
					for (const head of heads) {
						this.shaByRefId[getRefIdByBaseRef(refTypes.HEAD, head)] = processedGraphRow.sha;
					}
				}

				if (remotes.length > 0) {
					remoteBranchMarkerRowIndices.push(rowIndex);
					for (const remote of remotes) {
						if (remote.current) {
							upstreamMarkerRowIndices.push(rowIndex);
							this.headUpstreamSha = processedGraphRow.sha;
						}

						if (this.downstreamsByUpstream[`${remote.owner}/${remote.name}`]?.length > 0) {
							this.branchUpstreamRowIndices.push(rowIndex);
						}

						this.shaByRefId[getRefIdByBaseRef(refTypes.REMOTE, remote)] = processedGraphRow.sha;
					}
				}

				if (tags.length > 0) {
					tagMarkerRowIndices.push(rowIndex);
					for (const tag of tags) {
						this.shaByRefId[getRefIdByBaseRef(refTypes.TAG, tag)] = processedGraphRow.sha;
					}
				}

				if (row.type === stashNodeType) {
					stashMarkerRowIndices.push(rowIndex);
				}

				if (!isFilteredCommitList || this.highlightedShas[row.sha]) {
					this.orderedGraphRows.push(processedGraphRow);
				}
			}

			this.processedGraphRowBySha[row.sha] = processedGraphRow;
		});

		if (this.isMounted) {
			this.setState(state => ({
				markerRowIndices: {
					...state.markerRowIndices,
					head: headMarkerRowIndices,
					upstream: upstreamMarkerRowIndices,
					localBranches: localBranchMarkerRowIndices,
					remoteBranches: remoteBranchMarkerRowIndices,
					tags: tagMarkerRowIndices,
					stashes: stashMarkerRowIndices,
				},
			}));
		} else {
			// eslint-disable-next-line react/no-direct-mutation-state
			this.state = {
				...this.state,
				markerRowIndices: {
					...this.state?.markerRowIndices,
					head: headMarkerRowIndices,
					upstream: upstreamMarkerRowIndices,
					localBranches: localBranchMarkerRowIndices,
					remoteBranches: remoteBranchMarkerRowIndices,
					tags: tagMarkerRowIndices,
					stashes: stashMarkerRowIndices,
				},
			};
		}

		this.clearTimelinesInterval();
		this.updateTimelines();
		this.timelinesInterval = setInterval(this.onTimelinesRefreshInterval, 900000); // 15 min.

		this.onGraphVisibleRowsUpdated();
	}

	updateRowStatsConstraints(): void {
		this.rowStatsConstraints = getRowStatsConstraints(
			this.rowsStats != null ? Object.values(this.rowsStats).map(s => s.additions + s.deletions) : [],
		);
	}

	updateTimelines(): void {
		const timelineEntriesByPeriod: TimelineEntriesByPeriod = getTimelineEntriesByPeriod();
		Object.values(timelineEntriesByPeriod).forEach((timelineEntries: TimelineEntry[]) => {
			timelineEntries.forEach((timelineEntry: TimelineEntry) => {
				const { date } = timelineEntry;
				const foundGraphRowForEntry: ProcessedGraphRow | undefined = this.orderedGraphRows.find(
					(row: ProcessedGraphRow) => row.date < date,
				);
				if (foundGraphRowForEntry && this.processedGraphRowBySha[foundGraphRowForEntry.sha]) {
					this.processedGraphRowBySha[foundGraphRowForEntry.sha].timeLineEntry = timelineEntry;
				}
			});
		});
	}

	// Remove all timeLineEntry properties from all graph rows in this.processedGraphRowBySha, if they exist
	clearTimelines(): void {
		Object.keys(this.processedGraphRowBySha).forEach((sha: Sha) => {
			if (this.processedGraphRowBySha[sha].timeLineEntry) {
				delete this.processedGraphRowBySha[sha].timeLineEntry;
			}
		});
	}

	clearTimelinesInterval(): void {
		if (this.timelinesInterval) {
			clearInterval(this.timelinesInterval);
			this.timelinesInterval = undefined;
		}
	}

	onTimelinesRefreshInterval = (): void => {
		if (!this.isMounted) {
			return;
		}

		this.clearTimelines();
		this.updateTimelines();

		this.setState({ processedRows: [...this.orderedGraphRows] });
	};

	cleanupSelections(): void {
		// When the processed rows change, we may lose rows in cases such as repo change.
		// We need to clean up the selected shas to ensure it doesn't include any old shas.

		for (const sha of this.selectedShas) {
			if (!this.processedGraphRowBySha[sha]) {
				this.selectedShas.delete(sha);
				if (this.selectionFocusSha === sha) {
					this.selectionFocusSha = undefined;
				}
			}
		}

		// Clean up shift selection anchor if it no longer exists
		if (this.selectionAnchorSha && !this.processedGraphRowBySha[this.selectionAnchorSha]) {
			this.selectionAnchorSha = undefined;
		}
	}

	getColumnColorByColumn(cssVariables: CssVariables): ColumnColorByColumn {
		const numGraphColumns: number = this.getNumGraphColumns(cssVariables);
		const max: number = this.maxColumns > numGraphColumns ? this.maxColumns : numGraphColumns;
		const columnColorByColumn: ColumnColorByColumn = {};

		for (let column = 0; column <= max; column += 1) {
			if (column < numGraphColumns) {
				// Initial column colors of the graph (default is 10. See "numGraphColumnsDefault" constant).
				const colorKey: string = columnToColorPropName(column);
				const colorValue: string = cssVariables[colorKey];

				columnColorByColumn[column] = formatColor(parseColor(colorValue));
			} else {
				// This code is to calculate the column colors based on the first defined colors of the graph.
				// This is because we may have more columns than what we initialy defined. This will be
				// depending of the quantity of loaded commits and the amount of refs of the repository itself.
				const color = columnColorByColumn[column % numGraphColumns];
				columnColorByColumn[column] = color;
			}
		}

		return columnColorByColumn;
	}

	getNumGraphColumns(cssVariables: CssVariables): number {
		const colorValue: string = cssVariables[NUM_COLUMNS_SUPPORTED_PROP_NAME];

		const maybeNumGraphColumns = Number.parseInt(colorValue, numGraphColumnsDefault);
		return Number.isNaN(maybeNumGraphColumns) ? numGraphColumnsDefault : maybeNumGraphColumns;
	}

	expandLastZoneMinWidthForScrollbar(orderedActiveGraphZones: GraphZone[]): void {
		const lastZoneType: string | undefined = orderedActiveGraphZones[orderedActiveGraphZones.length - 1]?.type;
		if (!lastZoneType) return;

		const scrollbarThickness: number = getScrollThickness(this.props.cssVariables, true);
		const paddedMinWidth = graphZoneMetaData[lastZoneType].minimumWidth + scrollbarThickness;

		const lastZone = this.graphZonesByType[lastZoneType];
		if (!lastZone || (lastZone.maximumWidth != null && paddedMinWidth > lastZone.maximumWidth)) return;

		lastZone.minimumWidth = paddedMinWidth;
		const iconWidth = graphZoneMetaData[lastZoneType].showIconWidth;
		if (iconWidth != null) {
			lastZone.showIconWidth = iconWidth + scrollbarThickness;
		}

		if (lastZone.currentWidth < lastZone.minimumWidth) {
			lastZone.currentWidth = lastZone.minimumWidth;
		}

		if (lastZone.preferredWidth < lastZone.minimumWidth) {
			lastZone.preferredWidth = lastZone.minimumWidth;
		}
	}

	ensureZoneWidthsMatchGraphWidth(orderedActiveGraphZones: GraphZone[], width: number): void {
		const totalGraphZoneWidths: number = getZoneWidthsTotal(orderedActiveGraphZones);

		if (totalGraphZoneWidths > width) {
			this.shrinkZoneWidthsToFitWidth(orderedActiveGraphZones, width);
		}

		if (totalGraphZoneWidths < width) {
			this.expandZoneWidthsToFitWidth(orderedActiveGraphZones, width);
		}
	}

	shrinkZoneWidthsToFitWidth(orderedActiveGraphZones: GraphZone[], width: number, stoppingIndex?: number): void {
		let totalZoneWidths: number = getZoneWidthsTotal(orderedActiveGraphZones);
		while (totalZoneWidths > width) {
			const lastShrinkableGraphZone = getLastShrinkableGraphZone(orderedActiveGraphZones, stoppingIndex);
			if (!lastShrinkableGraphZone) {
				break;
			}

			const remainingWidthsTotal = getZoneWidthsTotal(orderedActiveGraphZones, lastShrinkableGraphZone.type);
			if (remainingWidthsTotal + lastShrinkableGraphZone.currentWidth > width) {
				const newWidth: number = getClampedZoneWidth(
					lastShrinkableGraphZone,
					orderedActiveGraphZones,
					width - remainingWidthsTotal,
				);
				this.updateWidthByZone(newWidth, lastShrinkableGraphZone.type);
			}

			totalZoneWidths = getZoneWidthsTotal(orderedActiveGraphZones);
		}
	}

	getOrderedActiveGraphZones(): GraphZone[] {
		return this.graphZoneOrdering
			.sort(
				(a: GraphZoneType, b: GraphZoneType) => this.graphZonesByType[a].order - this.graphZonesByType[b].order,
			)
			.filter((graphZoneType: GraphZoneType) => !this.graphZonesByType[graphZoneType].isHidden)
			.map((graphZoneType: GraphZoneType) => this.graphZonesByType[graphZoneType]);
	}

	expandZoneWidthsToFitWidth(orderedActiveGraphZones: GraphZone[], width: number, startingIndex?: number): void {
		let totalZoneWidths: number = getZoneWidthsTotal(orderedActiveGraphZones);
		while (totalZoneWidths < width) {
			const firstExpandableGraphZone = getFirstExpandableGraphZone(orderedActiveGraphZones, startingIndex);
			if (!firstExpandableGraphZone) {
				break;
			}

			const remainingWidthsTotal = getZoneWidthsTotal(orderedActiveGraphZones, firstExpandableGraphZone.type);
			const expansionMaximumWidth: number = firstExpandableGraphZone.maximumWidth
				? Math.min(firstExpandableGraphZone.maximumWidth, firstExpandableGraphZone.preferredWidth || Infinity)
				: firstExpandableGraphZone.preferredWidth || 0;
			if (remainingWidthsTotal + firstExpandableGraphZone.currentWidth < width) {
				let newWidth: number = width - remainingWidthsTotal;
				if (
					!isLastColumn(firstExpandableGraphZone.type, orderedActiveGraphZones) &&
					newWidth > expansionMaximumWidth
				) {
					newWidth = expansionMaximumWidth;
				}

				newWidth = getClampedZoneWidth(firstExpandableGraphZone, orderedActiveGraphZones, newWidth);

				this.updateWidthByZone(newWidth, firstExpandableGraphZone.type);
			}

			totalZoneWidths = getZoneWidthsTotal(orderedActiveGraphZones);
		}
	}

	onGraphHeaderRowMouseDown: OnMouseDown = (event: React.MouseEvent<any>): void => {
		// Code to ignore middle mouse down
		if (event.button === 1) {
			event.preventDefault();
		}
	};

	onToggleRefNodesShown: OnToggleRefNodesShown = (
		event: React.MouseEvent<any>,
		refs: GraphRefOptData[],
		refsVisibles: boolean,
		sha?: Sha,
	): void => {
		refs.forEach((ref: GraphRefOptData) => {
			if (refsVisibles && this.excludeRefsById[ref.id]) {
				// Show the ref into the graph
				// eslint-disable-next-line @typescript-eslint/no-dynamic-delete
				delete this.excludeRefsById[ref.id];
			} else if (!refsVisibles) {
				// Hide the ref into the graph
				this.excludeRefsById[ref.id] = ref;
			}
		});

		this.excludeRemotesByName = this.getExcludeRemotesByName();

		if (this.props.onToggleRefsVisibilityClick) {
			this.props.onToggleRefsVisibilityClick(
				event,
				refs,
				refsVisibles,
				sha ? this.processedGraphRowBySha[sha] : undefined,
			);
		}

		this.processRows(this.props.graphRows);
		this.setState({ processedRows: [...this.orderedGraphRows] });
	};

	cleanEdgeCaches(): void {
		clearEdgeNodesCache();
		clearStartingEdgeCache();
		clearPassThroughEdgeCache();
		clearEndingEdgeCacheCache();
		clearXValueAtColumnCache();
	}

	onZoneEnter: OnZoneEnter = (
		e: React.MouseEvent<any>,
		zoneType: GraphZoneType,
		hoveredRefGroup?: GraphRefGroup,
		hoveredSha?: Sha,
	): void => {
		if (zoneType !== refZone) {
			if (hoveredRefGroup && hoveredSha) {
				this.onRefNodeUnhovered(e, hoveredRefGroup, hoveredSha);
			}

			this.onRefZoneUnhovered();
		}
	};

	adjustResizedGraphZone(
		graphZones: GraphZone[],
		graphZone: GraphZone,
		dimensions: OnResizeParams,
		updatePreferredWidth: boolean,
	): void {
		if (dimensions?.width) {
			const oldWidth = graphZone.currentWidth;
			this.updateWidthByZone(dimensions.width, graphZone.type, updatePreferredWidth);

			const zoneIndex = getGraphZoneIndexFromGraphZones(graphZone.type, graphZones);

			const currentGraphZone = this.graphZonesByType[graphZone.type];
			if (oldWidth > currentGraphZone.currentWidth) {
				this.expandZoneWidthsToFitWidth(graphZones, this.graphWidth, zoneIndex + 1);
			}

			if (oldWidth < currentGraphZone.currentWidth) {
				this.shrinkZoneWidthsToFitWidth(graphZones, this.graphWidth, zoneIndex - 1);
			}
		}
	}

	dimRowsOfSelectedCommit(dimRows: boolean) {
		this.setState({ dimRowsOfSelectedCommit: dimRows });
	}

	onGraphContainerBlurred: OnGraphContainerBlurred = (
		event: React.FocusEvent<any>,
		hoveredRefGroup?: GraphRefGroup,
		hoveredSha?: Sha,
	): void => {
		// NOTE: added timeout here so, the `onClick` and `onDoubleClick` events of the
		// ref node (or any other child component) can be executed before the `onBlur`
		// mutates the state and cause all the consequences (re-rendering + add or remove
		// DOM elements). Fixes the issue GK-4466.
		// TODO: investigate/re-think the focus/blur mechanism of the graph to see if it
		// could be done in a better way to avoid those problems.
		setTimeout(() => {
			if (!this.isMounted) {
				return;
			}

			// Blurring the graph container should cause any hovered item to unhover
			if (hoveredRefGroup && hoveredSha) {
				this.onRefNodeUnhovered(event, hoveredRefGroup, hoveredSha);
			}

			this.onRefZoneUnhovered();
		}, 250);
	};

	onGraphContainerMouseEnter: OnGraphMouseEnter = (event: React.MouseEvent<any>): void => {
		if (this.props.onGraphMouseEnter) {
			this.props.onGraphMouseEnter(event);
		}
	};

	onGraphContainerMouseLeave: OnGraphMouseLeave = (event: React.MouseEvent<any>): void => {
		if (this.props.onGraphMouseLeave) {
			this.props.onGraphMouseLeave(event);
		}
	};

	onGraphResized: OnGraphResized = debounceFrame((contentRectWidth: number, contentRectHeight: number): void => {
		if (!this.isMounted) {
			return;
		}

		const newHeight = Math.floor(contentRectHeight);
		const newWidth = Math.floor(contentRectWidth);

		const heightChanged: boolean = newHeight !== this.graphHeight;
		const widthChanged: boolean = newWidth !== this.graphWidth;

		const newState: any = {};

		if (heightChanged) {
			this.graphHeight = newHeight;
			newState.height = newHeight;
		}

		if (widthChanged) {
			this.graphWidth = newWidth;
			const graphZones = this.getOrderedActiveGraphZones();
			this.ensureZoneWidthsMatchGraphWidth(graphZones, this.graphWidth);
			newState.width = this.graphWidth;
			newState.graphZones = graphZones;
		}

		if (heightChanged || widthChanged) {
			this.setState(newState);
			if (heightChanged) {
				this.onGraphVisibleRowsUpdatedThrottled();
			}

			if (this.props.onGraphResized) {
				this.props.onGraphResized(this.graphWidth, this.graphHeight);
			}
		}
	});

	// This should be called any time one of the three properties changes:
	// 1. orderedGraphRows
	// 2. scrollTop
	// 3. graphHeight
	onGraphVisibleRowsUpdated(): void {
		if (!this.isMounted || !this.props.onGraphVisibleRowsChanged || !this.orderedGraphRows?.length) {
			return;
		}

		const { top, bottom } = this.getTopAndBottomVisibleRowIndex();
		if (
			top === -1 ||
			bottom === -1 ||
			top >= this.orderedGraphRows.length ||
			bottom >= this.orderedGraphRows.length ||
			(top === this.lastTopVisibleRowIndex && bottom === this.lastBottomVisibleRowIndex)
		) {
			return;
		}

		this.lastTopVisibleRowIndex = top;
		this.lastBottomVisibleRowIndex = bottom;
		if (this.props.onGraphVisibleRowsChanged) {
			this.props.onGraphVisibleRowsChanged(this.orderedGraphRows[top], this.orderedGraphRows[bottom]);
		}
	}

	getCurrentPlatform(): string {
		return this.props.platform || 'darwin';
	}

	isMacOSPlatform(): boolean {
		return this.getCurrentPlatform() === 'darwin';
	}

	isCommitListFiltered(): boolean {
		return this.searchMode === 'filter' && Object.values(this.highlightedShas).length > 0;
	}

	getGraphColumnSettingFromZoneType(graphZone: GraphZone): GraphColumnSetting {
		return {
			width: graphZone.preferredWidth || 0,
			isHidden: graphZone.isHidden,
			mode: graphZone.mode,
			order: graphZone.order,
		};
	}

	decorateWithHelmet = (container: ReactElement<'div'>, cssVariables: CssVariables, nonce?: string) => {
		const styleString = getStyleStringFromCssVariables(cssVariables);
		return (
			<>
				<style nonce={nonce}>{styleString}</style>
				<div className="gk-graph">{container}</div>
			</>
		);
	};

	override render() {
		const {
			columnsSettings,
			customFooterRow,
			isInUnsupportedRebase,
			isCommitting,
			repoPath,
			nonce,
			suppressNonRefRowTooltips,
			wipMessageEditable,
		} = this.props;

		const {
			avatarUrlByEmail,
			columnColorByColumn,
			contexts,
			createRefFormData,
			cssVariablesWithDefaults,
			currentlyHoveredCommitSha,
			dimMergeCommits,
			dimRowsOfSelectedCommit,
			enableShowHideRefsOptions,
			highlightRowsOnRefHover,
			showGhostRefsOnRowHover,
			showRemoteNamesOnRefs,
			enabledRefMetadataTypes,
			enabledScrollMarkerTypes,
			graphCommitDescDisplayMode,
			graphZones,
			hasMoreCommits,
			height,
			highlightedShas,
			hoveredRefGroup,
			hoveredRefZoneSha,
			themeOpacityFactor,
			isLoadingRows,
			rowsStatsLoading,
			numGraphColumns,
			pendingCommitMessageSummary,
			processedRows,
			refIconsPosition,
			refMetadataById,
			useAuthorInitialsForAvatars,
			scrollToAlignment,
			scrollToIndex,
			scrollTop,
			scrollLeft,
			shaLength,
			width,
			workDirStats,
		} = this.state;

		const graphContainerClasses: string = classnames('flex', 'graph-container');

		const getKeyForCell: GetRealKeyForCellFunc = makeGetKeyForCell(processedRows);

		const smartCellRangeRenderer: GridCellRangeRenderer = makeSmartCellRangeRenderer((rowIndex: number) =>
			getKeyForCell(rowIndex),
		);

		const leftGutterWidth = this.getLeftGutterWidth();
		const rightGutterWidth = this.getRightGutterWidth();
		const rightGutterBoxShadowAlpha = this.getRightGutterBoxShadowAlpha(themeOpacityFactor);
		const leftGutterBoxShadowAlpha = this.getLeftGutterBoxShadowAlpha(themeOpacityFactor);
		const nodeOffsetByColumn = this.getNodeOffsetByColumn();
		const nodeOpacityByColumn = this.getNodeOpacityByColumn();

		const refZoneIndex = getGraphZoneIndexFromGraphZones(refZone, graphZones);
		const commitZoneIndex = getGraphZoneIndexFromGraphZones(commitZone, graphZones);
		const shouldShowRefLine = refZoneIndex !== -1 && commitZoneIndex !== -1 && commitZoneIndex - refZoneIndex === 1;

		const enableColumnsResizer = !hoveredRefGroup;

		const rowProps = {
			alwaysShowTimelines: this.isCommitListFiltered(),
			avatarUrlByEmail: avatarUrlByEmail,
			createRefFormData: createRefFormData,
			dimMergeCommits: dimMergeCommits,
			dimRowsOfSelectedCommit: dimRowsOfSelectedCommit,
			enableShowHideRefsOptions: enableShowHideRefsOptions,
			highlightRowsOnRefHover: highlightRowsOnRefHover,
			showGhostRefsOnRowHover: showGhostRefsOnRowHover,
			showRemoteNamesOnRefs: showRemoteNamesOnRefs,
			enabledRefMetadataTypes: enabledRefMetadataTypes,
			enabledScrollMarkerTypes: enabledScrollMarkerTypes,
			isInUnsupportedRebase: isInUnsupportedRebase,
			isCommitting: isCommitting || false,
			pendingCommitMessageSummary: pendingCommitMessageSummary,
			workDirStats: workDirStats,
			repoPath: repoPath,
			columnColorByColumn: columnColorByColumn,
			currentlyHoveredCommitSha: currentlyHoveredCommitSha,
			cssVariables: cssVariablesWithDefaults,
			graphCommitDescDisplayMode: graphCommitDescDisplayMode,
			graphZoneModeConstants: this.graphZoneModeConstants,
			graphZones: graphZones,
			hasMoreCommits: hasMoreCommits,
			height: height,
			highlightedShas: highlightedShas,
			hoveredRefGroup: hoveredRefGroup,
			hoveredRefZoneSha: hoveredRefZoneSha,
			includeOnlyRefsById: this.includeOnlyRefsById,
			includeOnlyRemotesByName: this.includeOnlyRemotesByName,
			isLoadingRows: isLoadingRows,
			selectedShas: this.selectedShas,
			leftGutterBoxShadowAlpha: leftGutterBoxShadowAlpha,
			leftGutterWidth: leftGutterWidth,
			nodeOffsetByColumn: nodeOffsetByColumn,
			nodeOpacityByColumn: nodeOpacityByColumn,
			nonce: nonce,
			numGraphColumns: numGraphColumns,
			processedRows: processedRows,
			processedGraphRowBySha: this.processedGraphRowBySha,
			refIconsPosition: refIconsPosition,
			refMetadataById: refMetadataById,
			rightGutterBoxShadowAlpha: rightGutterBoxShadowAlpha,
			rightGutterWidth: rightGutterWidth,
			rowStatsConstraints: this.rowStatsConstraints,
			rowsStats: this.rowsStats,
			suppressNonRefRowTooltips: suppressNonRefRowTooltips,
			useAuthorInitialsForAvatars: useAuthorInitialsForAvatars,
			scrollToAlignment: scrollToAlignment,
			scrollLeft: scrollLeft,
			scrollTop: scrollTop,
			shaLength: shaLength,
			shouldShowRefLine: shouldShowRefLine,
			width: width,
			wipMessageEditable: wipMessageEditable || false,
			// Events
			onMissingAvatar: this.onMissingAvatar,
			onMissingRefMetadata: this.onMissingRefMetadata,
			clearCurrentlyHoveredGraphCommit: this.onClearCurrentlyHoveredGraphCommit,
			setAsCurrentlyHoveredGraphCommit: this.onCurrentlyHoveredGraphCommit,
			onClickRef: this.onClickRef,
			onClickCommit: this.onClickCommit,
			onDoubleClickCommit: this.onDoubleClickCommit,
			onBlurWipNodeInput: this.onBlurWipNodeInput,
			onFocusWipNodeInput: this.onFocusWipNodeInput,
			onDoubleClickRef: this.onDoubleClickRef,
			onMessageChange: this.onMessageChange,
			onRefBeginDrag: this.onRefBeginDrag,
			onRefCanDrag: this.onRefCanDrag,
			onRefCanDrop: this.onRefCanDrop,
			onRefCreate: this.onRefCreate,
			onRefCreateCancel: this.onRefCreateCancel,
			onRefCreateContextMenu: this.onRefCreateContextMenu,
			onRefDragEnter: this.onRefDragEnter,
			onRefDragLeave: this.onRefDragLeave,
			onRefDrop: this.onRefDrop,
			onRefEndDrag: this.onRefEndDrag,
			onRefNodeHovered: this.onRefNodeHovered,
			onRefNodeUnhovered: this.onRefNodeUnhovered,
			onRefShorthandChange: this.onRefShorthandChange,
			onRefZoneHovered: this.onRefZoneHovered,
			onRefZoneUnhovered: this.onRefZoneUnhovered,
			onToggleRefNodesShown: this.onToggleRefNodesShown,
			onCommitContextMenu: this.onCommitContextMenu,
			onShowContextMenuForGroupedRef: this.onRefContextMenu,
			// Callbacks
			getExternalIcon: this.getIconCallback,
			formatCommitDateTime: this.formatCommitDateTimeCallback,
			formatRefShorthand: this.formatRefShorthandCallback,
			isRefShorthandValid: this.isRefShorthandValidCallback,
			translate: this.translateCallback,
		};

		const rowRenderersByIds: RowRenderersByIds = {
			[refZone]: makeRefZoneRowRenderer(rowProps, getKeyForCell), // Branch/tag column
			[commitZone]: makeCommitZoneRowRenderer(rowProps, getKeyForCell), // Graph column
			[commitMessageZone]: makeCommitMessageZoneRowRenderer(rowProps, getKeyForCell), // Commit message column
			[commitAuthorZone]: makeCommitAuthorZoneRowRenderer(rowProps, getKeyForCell), // Author column
			[commitDateTimeZone]: makeCommitDateTimeZoneRowRenderer(rowProps, getKeyForCell), // Date/time column
			[commitShaZone]: makeCommitShaZoneRowRenderer(rowProps, getKeyForCell), // Sha column
			[changesZone]: makeChangesZoneRowRenderer(rowProps, getKeyForCell), // Changes column
			[timelineMsgRowRenderId]: makeTimelineMsgRowRenderer(rowProps, getKeyForCell), // Special timeline message column
		};

		console.log('this.state.markerRowIndices', this.state.markerRowIndices['highlights']);
		const graphColumns: ReactElement<typeof GraphColumn>[] = graphZones.map(
			(graphZone: GraphZone, columnIndex: number) => {
				const isFirstColumn = columnIndex === 0;
				return (
					<GraphColumn
						branchUpstreamRowIndices={this.branchUpstreamRowIndices}
						cellRenderersByIds={rowRenderersByIds}
						columnIndex={columnIndex}
						customFooterRow={customFooterRow}
						enabledScrollMarkerTypes={enabledScrollMarkerTypes}
						enableResizer={enableColumnsResizer}
						getExternalIcon={this.getIconCallback}
						getKeyForCell={getKeyForCell}
						graphHeight={height - GRAPH_HEADER_ROW_HEIGHT}
						graphWidth={width}
						graphZones={graphZones}
						graphZoneType={graphZone.type}
						hasMoreCommits={hasMoreCommits}
						horizontalScrollHeight={this.getHorizontalScrollHeight(
							graphZone.type,
							cssVariablesWithDefaults,
						)}
						isLoadingRows={isLoadingRows}
						key={`${graphZone.type}ColumnContainer`}
						markerColors={this.getMarkerColors()}
						markerRowIndices={this.state.markerRowIndices}
						onResize={this.onGraphZoneResize}
						onResizeEnd={this.onGraphZoneResizeEnd}
						onScroll={this.onScrollForZone}
						onScrollToRowCausedUpdate={
							isFirstColumn ? this.onScrollToRowCausedUpdateForFirstColumn : undefined
						}
						onZoneEnter={event =>
							this.onZoneEnter(event, graphZone.type, hoveredRefGroup, hoveredRefZoneSha)
						}
						rowCount={processedRows.length}
						scrollLeft={scrollLeft}
						scrollToAlignment={isFirstColumn ? scrollToAlignment : undefined}
						scrollToIndex={isFirstColumn ? scrollToIndex : undefined}
						scrollTop={scrollTop}
						smartCellRangeRenderer={smartCellRangeRenderer}
						translate={this.translateCallback}
						verticalScrollWidth={this.getVerticalScrollWidth(graphZone.type, cssVariablesWithDefaults)}
					/>
				);
			},
		);

		const maybeHeaderRow: ReactElement<GraphHeaderRow> | false = this.graphComponentRef?.current && (
			<GraphHeaderRow
				columnsSettings={columnsSettings}
				dragAppendToContainer={this.graphComponentRef?.current}
				enableResizer={enableColumnsResizer}
				enableShowHideRefsOptions={enableShowHideRefsOptions}
				excludeRefsById={this.excludeRefsById}
				getExternalIcon={this.getIconCallback}
				graphZones={graphZones}
				headerContext={contexts?.header}
				height={height}
				includeOnlyRefsById={this.includeOnlyRefsById}
				onColumnReOrdered={this.onGraphColumnReOrdered}
				onFilterColumnClick={this.onFilterColumnClick}
				onGraphZoneResize={this.onGraphZoneResize}
				onGraphZoneResizeEnd={this.onGraphZoneResizeEnd}
				onPopupGraphHeaderContextMenu={this.onPopupGraphHeaderContextMenu}
				onSettingsClick={this.onSettingsClick}
				onToggleRefNodesShown={this.onToggleRefNodesShown}
				repoPath={repoPath}
				rowsStatsLoading={rowsStatsLoading}
				settingsContext={contexts?.settings}
				showRemoteNamesOnRefs={showRemoteNamesOnRefs}
				translate={this.translateCallback}
				width={width}
			/>
		);

		const graphContainerDom = (
			<div
				className="graph-component"
				data-vscode-context={parseContext(contexts?.graph)}
				onBlur={e => this.onGraphContainerBlurred(e, hoveredRefGroup, hoveredRefZoneSha)}
				onMouseEnter={this.onGraphContainerMouseEnter}
				onMouseLeave={this.onGraphContainerMouseLeave}
				ref={this.graphComponentRef}
				style={{ height: '100%', width: '100%', position: 'absolute' }}
				tabIndex={-1}
			>
				{maybeHeaderRow}
				<div
					className={
						highlightedShas && Object.keys(highlightedShas).length
							? classnames(graphContainerClasses, 'graph-highlighted')
							: graphContainerClasses
					}
					id="graph-container"
					onKeyDown={this.onKeyDown}
					onKeyUp={this.onKeyUp}
					onMouseDown={this.onGraphHeaderRowMouseDown}
					ref={this.graphContainerRef}
					style={{ height: height, width: width }}
					tabIndex={-1}
				>
					{graphColumns}
				</div>
			</div>
		);

		return this.decorateWithHelmet(graphContainerDom, cssVariablesWithDefaults, nonce);
	}
}

export default GraphContainer;
