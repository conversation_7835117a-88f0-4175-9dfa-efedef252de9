import type { CSSProperties } from 'react';

export type DateInMilliseconds = number;
export type ETag = string;
export type Guid = string;
export type HexColor = string;
export type HexColorWithoutOctothorpe = string;
export type Path = string;
export type SecStore = any;
export type Style = CSSProperties;

export type Url = string;
export type Hostname = Url;
export type GitHttpsUrl = Url;
export type GitSshUrl = Url;
// eslint-disable-next-line @typescript-eslint/no-duplicate-type-constituents
export type GitRemoteUrl = GitHttpsUrl | GitSshUrl;

export type Platform = 'darwin' | 'linux' | 'win32';

// https://www.w3.org/TR/uievents-key/#named-key-attribute-values
export enum NamedKeys {
	arrowDownKey = 'ArrowDown',
	arrowUpKey = 'ArrowUp',
	controlKey = 'Control',
	metaKey = 'Meta',
	shiftKey = 'Shift',
}

/*
 * The idea here is to limit cacheQueue to a certain size, and as items fall off the queue they get deleted from the
 * data. In this way recent items remain in the cache, but after not being inserted for a while, they get freed up.
 */
export type MapWithCacheQueue<T, U> = {
	data: { [key: string /* T */]: U };
	cacheQueue: T[];
};

/**
 * This represents a pair of synchronous getter and setter callbacks for a value.
 */
export type ManipulationCallbacks<T> = {
	get: () => T;
	set: (newValue: T) => void;
};

export type CssVariables = { [variable: string]: string };
export type HashMap = { [hash: string]: string };
