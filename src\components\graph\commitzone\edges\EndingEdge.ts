import type { CommitType } from '../../../../domain/commit/CommitTypes';
import {
	buildStartingOrEndingEdgeHash,
	getLineElement,
	getSvgStyles,
	getXValueAtColumn,
} from '../../../../domain/edge/EdgeHelpers';
import {
	COMMIT_ZONE_EDGE_ARC_RADIUS,
	COMMIT_ZONE_MARGIN_TOP,
	COMMIT_ZONE_ROW_HEIGHT,
} from '../../../../domain/graph/GraphConstants';
import type {
	ColumnColor,
	ColumnColorByColumn,
	EdgeCache,
	LineSvgProps,
	SvgElement,
	SvgStyles,
} from '../../../../domain/graph/GraphTypes';
import getArc from './Arc';

function getVerticalEndingEdge(
	color: ColumnColor,
	column: number,
	type: CommitType,
	columnWidth: number,
	gutterWidth: number,
	strokeWidth: number,
	isCompact?: boolean,
): SvgElement {
	const svgProps: LineSvgProps = getSvgStyles(type, color, strokeWidth, isCompact) as LineSvgProps;
	svgProps.x1 = getXValueAtColumn(column, columnWidth, gutterWidth, isCompact);
	svgProps.x2 = getXValueAtColumn(column, columnWidth, gutterWidth, isCompact);
	svgProps.y1 = 0;
	svgProps.y2 = COMMIT_ZONE_ROW_HEIGHT / 2;
	return getLineElement(svgProps);
}

function getCurvedEndingEdge(
	color: ColumnColor,
	nodeColumn: number,
	edgeColumn: number,
	type: CommitType,
	columnWidth: number,
	gutterWidth: number,
	strokeWidth: number,
	isCompact?: boolean,
): SvgElement {
	const svgStyles: SvgStyles = getSvgStyles(type, color, strokeWidth, isCompact);
	const arcStartOffset: number = nodeColumn < edgeColumn ? -COMMIT_ZONE_EDGE_ARC_RADIUS : COMMIT_ZONE_EDGE_ARC_RADIUS;

	// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
	const svgProps1: LineSvgProps = { ...svgStyles } as LineSvgProps;
	svgProps1.x1 = getXValueAtColumn(edgeColumn, columnWidth, gutterWidth, isCompact);
	svgProps1.x2 = getXValueAtColumn(edgeColumn, columnWidth, gutterWidth, isCompact);
	svgProps1.y1 = 0;
	svgProps1.y2 = COMMIT_ZONE_MARGIN_TOP;

	// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
	const svgProps2: LineSvgProps = { ...svgStyles } as LineSvgProps;
	svgProps2.x1 = getXValueAtColumn(edgeColumn, columnWidth, gutterWidth, isCompact) + arcStartOffset;
	svgProps2.x2 = getXValueAtColumn(nodeColumn, columnWidth, gutterWidth, isCompact);
	svgProps2.y1 = COMMIT_ZONE_ROW_HEIGHT / 2;
	svgProps2.y2 = COMMIT_ZONE_ROW_HEIGHT / 2;

	return `<g>${getLineElement(svgProps1)}${getArc(
		color,
		nodeColumn > edgeColumn ? 0 : 90,
		nodeColumn > edgeColumn ? 90 : 180,
		type,
		getXValueAtColumn(edgeColumn, columnWidth, gutterWidth, isCompact) + arcStartOffset,
		COMMIT_ZONE_MARGIN_TOP,
		strokeWidth,
		isCompact,
	)}${getLineElement(svgProps2)}</g>`;
}

function isEndingEdgeVertical(nodeColumn: number, edgeColumn: number): boolean {
	return nodeColumn === edgeColumn;
}

let endingEdgeCache: EdgeCache = {};

export function clearEndingEdgeCacheCache() {
	endingEdgeCache = {};
}

export function getEndingEdge(
	edgeColumn: number,
	nodeColumn: number,
	type: CommitType,
	columnColorByColumn: ColumnColorByColumn,
	columnWidth: number,
	gutterWidth: number,
	strokeWidth: number,
	isCompact?: boolean,
): SvgElement {
	const hash: string = buildStartingOrEndingEdgeHash(edgeColumn, nodeColumn, type, isCompact);

	let endingEdge: SvgElement | undefined = endingEdgeCache[hash];
	if (endingEdge) {
		return endingEdge;
	}

	endingEdge = isEndingEdgeVertical(nodeColumn, edgeColumn)
		? getVerticalEndingEdge(
				columnColorByColumn[edgeColumn],
				nodeColumn,
				type,
				columnWidth,
				gutterWidth,
				strokeWidth,
				isCompact,
		  )
		: getCurvedEndingEdge(
				columnColorByColumn[edgeColumn],
				nodeColumn,
				edgeColumn,
				type,
				columnWidth,
				gutterWidth,
				strokeWidth,
				isCompact,
		  );

	endingEdgeCache[hash] = endingEdge;

	return endingEdge;
}
