.gk-graph {
	--graph-row-height: 22px;
	--font-monospace: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
	--font-size: 62.5%;
	--fs-1: 1rem;
	--fs-2: 1.2rem;
	--fs-3: 1.4rem;
	--fs-4: 1.6rem;
	--fs-5: 1.8rem;
	--fs-6: 2rem;
	--fs-7: 2.4rem;
	--button-small-padding-y: 0.25rem;
	--button-small-padding-x: 0.5rem;
	--button-medium-padding-y: 0.5rem;
	--button-medium-padding-x: 0.75rem;
	--button-large-padding-y: 1rem;
	--button-large-padding-x: 1.25rem;
	--button-radius: 0.2rem;
	--input-radius: 3px;
	--about-modal-bg-color: #e2e4e9;
	--about-modal-width: 650px;
	--registration-modal-width: 400px;
	--file-history-header-height: 35px;
	--shop-modal-width: 1000px;
	--shop-modal-height: 90%;
	--scrollable-scrollbar-thickness: 14px;
	--mini-commit-col-left-width: 30px;
	--mini-commit-col-middle-padding: 6px;
	--mini-commit-col-right-width: 56px;
	--fuzzy-finder-repo-color: rgba(46, 206, 157, 0.1);
	--fuzzy-finder-checkout-color: rgba(6, 105, 247, 0.1);
	--fuzzy-finder-history-color: rgba(197, 23, 182, 0.1);
	--handle-width: 7px;
	--shadow-color: rgba(0, 0, 0, 0.4);
	--graph-red: #cd0101;
	--graph-orange: #f25d2e;
	--graph-yellow: #f2ca33;
	--graph-green: #7bd938;
	--graph-teal: #2ece9d;
	--graph-light-blue: #15a0bf;
	--graph-dark-blue: #0669f7;
	--graph-magenta: #8e00c2;
	--graph-purple: #c517b6;
	--graph-dark-purple: #d90171;
	--graph-red-f10: rgba(205, 1, 1, 0.1);
	--graph-orange-f10: rgba(242, 93, 46, 0.1);
	--graph-yellow-f10: rgba(242, 202, 51, 0.1);
	--graph-green-f10: rgba(123, 217, 56, 0.1);
	--graph-teal-f10: rgba(46, 206, 157, 0.1);
	--graph-light-blue-f10: rgba(21, 160, 191, 0.1);
	--graph-dark-blue-f10: rgba(6, 105, 247, 0.1);
	--graph-magenta-f10: rgba(142, 0, 194, 0.1);
	--graph-purple-f10: rgba(197, 23, 182, 0.1);
	--graph-dark-purple-f10: rgba(217, 1, 113, 0.1);
	--conflict-right: rgba(242, 202, 51, 0.25);
	--conflict-left: rgba(21, 160, 191, 0.25);
	--conflict-output: rgba(197, 23, 182, 0.25);
	--graph-red-f50: rgba(205, 1, 1, 0.5);
	--graph-orange-f50: rgba(242, 93, 46, 0.5);
	--graph-yellow-f50: rgba(242, 202, 51, 0.5);
	--graph-green-f50: rgba(123, 217, 56, 0.5);
	--graph-teal-f50: rgba(46, 206, 157, 0.5);
	--graph-light-blue-f50: rgba(21, 160, 191, 0.5);
	--graph-dark-blue-f50: rgba(6, 105, 247, 0.5);
	--graph-magenta-f50: rgba(142, 0, 194, 0.5);
	--graph-purple-f50: rgba(197, 23, 182, 0.5);
	--graph-dark-purple-f50: rgba(217, 1, 113, 0.5);
	--axo-orange: #e75225;
	--axo-ink: #141422;
	--axo-cream: #f9efc1;
	--axo-yellow: #d8c13a;
	--info-bar-height: 26px;
	--left-panel-header-height: 60px;
	--title-bar-height: 0px;
	--toolbar-height: 48px;
	--toolbar-border: 1px;
	--toolbar-item-height: calc(var(--toolbar-height, 0px) - var(--toolbar-border, 1px));
	--expand-detail-panel-transition: flex-grow 250ms ease-in-out;

	.app-bg0 {
		background-color: var(--app__bg0, #1c1e23);
	}
	.panel-bg0 {
		background-color: var(--panel__bg0, #272a31);
	}
	.font-monospace {
		font-family: var(--font-monospace, monospace);
	}
	.text-ellipsis {
		text-overflow: ellipsis;
		overflow-x: hidden;
		white-space: nowrap;
	}
	.text-selected {
		color: var(--text-selected, #ffffff);
	}
	.text-normal {
		color: var(--text-normal, rgba(255, 255, 255, 0.75));
	}
	.text-secondary {
		color: var(--text-secondary, rgba(255, 255, 255, 0.6));
	}
	.text-disabled {
		color: var(--text-disabled, rgba(255, 255, 255, 0.4));
	}
	.text-accent {
		color: var(--text-accent, #93a9ec);
	}
	.text-link {
		color: var(--link-color, #40c5ec);
	}
	.border {
		border: 1px solid var(--section-border, rgba(255, 255, 255, 0.08));
	}
	.border-top {
		border-top: 1px solid var(--section-border, rgba(255, 255, 255, 0.08));
	}
	.border-bottom {
		border-bottom: 1px solid var(--section-border, rgba(255, 255, 255, 0.08));
	}
	.border-left {
		border-left: 1px solid var(--section-border, rgba(255, 255, 255, 0.08));
	}
	.border-right {
		border-right: 1px solid var(--section-border, rgba(255, 255, 255, 0.08));
	}
	.fs-1 {
		font-size: var(--fs-1, 1rem);
	}
	.width-100-percent {
		width: 100%;
	}
	.height-100-percent {
		height: 100%;
	}
	.z1 {
		z-index: 1;
	}
	.z2 {
		z-index: 2;
	}
	.z3 {
		z-index: 3;
	}
	.z4 {
		z-index: 4;
	}
	.z5 {
		z-index: 5;
	}
	.z6 {
		z-index: 6;
	}
	.z7 {
		z-index: 7;
	}
	.z8 {
		z-index: 8;
	}
	.z9 {
		z-index: 9;
	}
	.z10 {
		z-index: 10;
	}
	.z11 {
		z-index: 11;
	}
	.pointer {
		cursor: pointer;
	}
}
