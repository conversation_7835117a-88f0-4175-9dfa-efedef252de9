import type { CommitType } from '../../../../domain/commit/CommitTypes';
import {
	buildStartingOrEndingEdgeHash,
	getLineElement,
	getSvgStyles,
	getXValueAtColumn,
} from '../../../../domain/edge/EdgeHelpers';
import {
	COMMIT_ZONE_EDGE_ARC_RADIUS,
	COMMIT_ZONE_MARGIN_BOTTOM,
	COMMIT_ZONE_ROW_HEIGHT,
} from '../../../../domain/graph/GraphConstants';
import type {
	ColumnColor,
	ColumnColorByColumn,
	EdgeCache,
	LineSvgProps,
	SvgElement,
	SvgStyles,
} from '../../../../domain/graph/GraphTypes';
import getArc from './Arc';

function getVerticalStartingEdge(
	color: ColumnColor,
	column: number,
	type: CommitType,
	columnWidth: number,
	gutterWidth: number,
	strokeWidth: number,
	isCompact?: boolean,
): SvgElement {
	const svgProps: LineSvgProps = getSvgStyles(type, color, strokeWidth, isCompact) as LineSvgProps;
	svgProps.x1 = getXValueAtColumn(column, columnWidth, gutterWidth, isCompact);
	svgProps.x2 = getXValueAtColumn(column, columnWidth, gutterWidth, isCompact);
	svgProps.y1 = COMMIT_ZONE_ROW_HEIGHT / 2;
	svgProps.y2 = COMMIT_ZONE_ROW_HEIGHT;
	return getLineElement(svgProps);
}

function getCurvedStartingEdge(
	color: ColumnColor,
	nodeColumn: number,
	edgeColumn: number,
	type: CommitType,
	columnWidth: number,
	gutterWidth: number,
	strokeWidth: number,
	isCompact?: boolean,
): SvgElement {
	const svgStyles: SvgStyles = getSvgStyles(type, color, strokeWidth, isCompact);
	const arcStartOffset: number = nodeColumn < edgeColumn ? -COMMIT_ZONE_EDGE_ARC_RADIUS : COMMIT_ZONE_EDGE_ARC_RADIUS;

	// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
	const svgProps1: LineSvgProps = { ...svgStyles } as LineSvgProps;
	svgProps1.x1 = getXValueAtColumn(edgeColumn, columnWidth, gutterWidth, isCompact);
	svgProps1.x2 = getXValueAtColumn(edgeColumn, columnWidth, gutterWidth, isCompact);
	svgProps1.y1 = COMMIT_ZONE_ROW_HEIGHT - COMMIT_ZONE_MARGIN_BOTTOM;
	svgProps1.y2 = COMMIT_ZONE_ROW_HEIGHT;

	// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
	const svgProps2: LineSvgProps = { ...svgStyles } as LineSvgProps;
	svgProps2.x1 = getXValueAtColumn(edgeColumn, columnWidth, gutterWidth, isCompact) + arcStartOffset;
	svgProps2.x2 = getXValueAtColumn(nodeColumn, columnWidth, gutterWidth, isCompact);
	svgProps2.y1 = COMMIT_ZONE_ROW_HEIGHT / 2;
	svgProps2.y2 = COMMIT_ZONE_ROW_HEIGHT / 2;

	return `<g>${getArc(
		color,
		nodeColumn < edgeColumn ? 180 : 270,
		nodeColumn < edgeColumn ? 270 : 0,
		type,
		getXValueAtColumn(edgeColumn, columnWidth, gutterWidth, isCompact) + arcStartOffset,
		COMMIT_ZONE_ROW_HEIGHT - COMMIT_ZONE_MARGIN_BOTTOM,
		strokeWidth,
		isCompact,
	)}${getLineElement(svgProps1)}${getLineElement(svgProps2)}</g>`;
}

function isStartingEdgeVertical(nodeColumn: number, edgeColumn: number): boolean {
	return nodeColumn === edgeColumn;
}

let startingEdgeCache: EdgeCache = {};

export function clearStartingEdgeCache() {
	startingEdgeCache = {};
}

export function getStartingEdge(
	edgeColumn: number,
	nodeColumn: number,
	type: CommitType,
	columnColorByColumn: ColumnColorByColumn,
	columnWidth: number,
	gutterWidth: number,
	strokeWidth: number,
	isCompact?: boolean,
): SvgElement {
	const hash: string = buildStartingOrEndingEdgeHash(edgeColumn, nodeColumn, type, isCompact);

	let startingEdge: SvgElement | undefined = startingEdgeCache[hash];
	if (startingEdge) {
		return startingEdge;
	}

	startingEdge = isStartingEdgeVertical(nodeColumn, edgeColumn)
		? getVerticalStartingEdge(
				columnColorByColumn[edgeColumn],
				nodeColumn,
				type,
				columnWidth,
				gutterWidth,
				strokeWidth,
				isCompact,
		  )
		: getCurvedStartingEdge(
				columnColorByColumn[edgeColumn],
				nodeColumn,
				edgeColumn,
				type,
				columnWidth,
				gutterWidth,
				strokeWidth,
				isCompact,
		  );

	startingEdgeCache[hash] = startingEdge;

	return startingEdge;
}
