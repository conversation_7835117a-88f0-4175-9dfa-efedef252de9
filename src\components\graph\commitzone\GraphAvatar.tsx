import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import { OverlayTrigger, Tooltip } from 'react-bootstrap';
import { getTextColor } from '../../../domain/color/ColorHelpers';
import { columnToColorPropName } from '../../../domain/cssvariable/CssVariableHelpers';
import type { CssVariables, Style } from '../../../domain/generic/GenericTypes';
import { COMMIT_ZONE_ROW_INNER_HEIGHT, DEFAULT_AVATAR_BACKGROUND_COLOR } from '../../../domain/graph/GraphConstants';
import { parseContext } from '../../../domain/graph/GraphHelpers';
import type { GraphItemContext } from '../../../domain/graph/GraphTypes';
import type { ComponentTooltip } from '../../../domain/ui/UiTypes';
import Avatar from '../shared/Avatar';

type Props = {
	authorInitials?: string;
	avatarUrl?: string | null;
	className?: string;
	column?: number;
	context?: GraphItemContext | null;
	cssVariables: CssVariables;
	style?: Style;
	fontSize: number;
	height?: number;
	minWidth?: number;
	size: number;
	title?: string;
	tooltip?: ComponentTooltip;
	top?: number;
	useAuthorInitialsForAvatars: boolean;
	width?: number;
};

type AvatarState = {
	tooltipText?: string;
};

export default class GraphAvatar extends React.Component<Props, AvatarState> {
	constructor(props: Props) {
		super(props);

		this.state = {
			tooltipText: '',
		};
	}

	// Some users behind firewalls are unable to use gravatar.com to get avatars
	// In this case, we'll render a backup avatar that consists of a color and the author's initials
	getDefaultAvatar: (height: number, width: number, top: number) => ReactElement<'div'> = (
		height: number,
		width: number,
		top: number,
	): ReactElement<'div'> => {
		const {
			authorInitials,
			column,
			context,
			cssVariables,
			style,
			fontSize,
			minWidth,
			size,
			title = '',
		} = this.props;

		const labelColor: string =
			column !== undefined
				? cssVariables[columnToColorPropName(column)] || DEFAULT_AVATAR_BACKGROUND_COLOR
				: DEFAULT_AVATAR_BACKGROUND_COLOR;

		const textColor: string = getTextColor(labelColor.trim());

		const mergedStyles = {
			alignItems: 'center',
			backgroundColor: labelColor,
			backgroundSize: size,
			color: textColor,
			display: 'flex',
			fontSize: `${fontSize}px`,
			fontWeight: 'bold',
			height: height,
			justifyContent: 'center',
			minWidth: minWidth,
			top: top,
			width: width,
			...style,
		};

		return (
			<div
				className={this.getAvatarClassName()}
				data-vscode-context={parseContext(context)}
				style={mergedStyles}
				title={title}
			>
				{authorInitials ? authorInitials.toUpperCase() : ''}
			</div>
		);
	};

	// this is necessary to allow for lazy tooltip text loading in performance critical areas
	ensureTooltipText: () => void = (): void => {
		const { tooltip } = this.props;
		this.setState({
			tooltipText: typeof tooltip === 'function' ? tooltip() : tooltip,
		});
	};

	getAvatarClassName(): string {
		return classnames('avatar', 'gravatar', 'rad2', this.props.className);
	}

	override render(): ReactElement<any> {
		const { tooltipText } = this.state;

		const {
			avatarUrl,
			context,
			style,
			height: currentHeight,
			minWidth,
			size,
			title = '',
			tooltip,
			top: currentTop,
			useAuthorInitialsForAvatars,
			width: currentWidth,
		} = this.props;

		const height: number = currentHeight || size;
		const width: number = currentWidth || size;
		const top: number = currentTop || COMMIT_ZONE_ROW_INNER_HEIGHT / 2 - height / 2;

		const avatarStyles = {
			top: top,
			minWidth: minWidth,
			...style,
		};

		const defaultAvatar = this.getDefaultAvatar(height, width, top);

		// For some reason, <Avatar> has to be in a containing component for the tooltip to show properly
		const avatar = useAuthorInitialsForAvatars ? (
			defaultAvatar
		) : (
			<span>
				<Avatar
					avatarClassName={this.getAvatarClassName()}
					avatarStyle={avatarStyles}
					context={context}
					height={height}
					hint={title}
					size={size}
					url={avatarUrl}
					width={width}
				>
					{this.getDefaultAvatar(height, width, top)}
				</Avatar>
			</span>
		);

		if (tooltip) {
			return (
				<OverlayTrigger
					delay={250}
					onEnter={this.ensureTooltipText}
					overlay={
						<Tooltip className="gk-graph" id="graph-gravatar-tooltip">
							{tooltipText}
						</Tooltip>
					}
					placement="top"
					trigger={['hover', 'focus']}
				>
					{avatar}
				</OverlayTrigger>
			);
		}

		return avatar;
	}
}
