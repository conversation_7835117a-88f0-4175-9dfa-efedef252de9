@import (reference) '../shared/columnColors';
// NOTE: The "base.jsonc" file used in for the themes system is derived from this .less file.
// If you modify something here keep in mind that you have to update that file as well.

.createGraphColorByColumnCssVariables(@column-number) when (@column-number > 0) {
	.getColumnColors(@column-number);

	.createGraphColorByColumnCssVariables((@column-number - 1));
}

.border-sides (@borderArg) {
	border-left: @borderArg;
	border-right: @borderArg;
}

.border-tb (@borderArg) {
	border-top: @borderArg;
	border-bottom: @borderArg;
}

.gk-graph {
	.createGraphColorByColumnCssVariables(@num-columns-supported);

	@ref-line-margin: -11;

	.inner-right-panel {
		background-color: var(--app__bg0);
		width: 100%;
	}

	/*
	Several values (like graph padding and column widths) are defined in GraphConstants.js and put directly on the
	component inline. This is because those values are used in calculations and the numbers should always come from a
	single source.
	*/

	.timeline-column-container {
		position: absolute;
		right: var(--scrollable-scrollbar-thickness);
		top: 0;
	}

	.react-virtualized-list {
		&.pad-for-horizontal-scrollbar {
			padding-bottom: var(--scrollable-scrollbar-thickness);
		}

		&.pad-for-vertical-scrollbar {
			padding-right: var(--scrollable-scrollbar-thickness);
		}

		&.timeline-column {
			overflow: visible !important;
		}
	}

	.graph-component {
		font-size: 14px;
	}

	.graph-container {
		outline: none;
		overflow-y: hidden;
		overflow-x: hidden;
		min-height: 100%;
		display: flex;
		flex-direction: row;
		background-color: var(--app__bg0);

		.color-red {
			color: var(--red, #d9413d);
		}
		.color-orange {
			color: var(--orange, #de9b43);
		}
		.color-blue {
			color: var(--blue, #4d88ff);
		}
		.color-green {
			color: var(--green, #5cb85c);
		}

		.graph-tree-resizable {
			.contents {
				overflow: hidden;
			}

			.resizable-handle.react-draggable.right {
				z-index: 5;
				width: 6px;
				right: 3px;
			}
		}

		.resizable .contents.resize-edge-right {
			right: 0;
			overflow: hidden;
		}

		.resizable-handle.horizontal {
			transition: 0s border-right;
			&:hover {
				border-right: 2px solid var(--selected-row);
				transition-delay: 0.05s; // briefly delay showing hover effect
			}
		}

		&.creating-ref {
			overflow-y: hidden;

			.graph-row-wrapper {
				opacity: 0.5;

				&.create-ref-node,
				&.create-ref-node-wip {
					opacity: 1;
				}
			}
		}

		.graph-adjust-commit-count {
			font-size: var(--fs-2);
			white-space: nowrap;
		}
	}

	.commit-popover {
		.createArrowColorBgClasses(@column-number) when (@column-number > 0) {
			.getColumnColors(@column-number);

			&.commit-popover-bg-color-@{column-number} {
				.arrow {
					border-right-color: @column-color;

					&:after {
						border-right-color: @column-color;
					}
				}

				border: 2px solid @column-color-f50;
				border-left: 4px solid @column-color;
			}
			.createArrowColorBgClasses((@column-number - 1));
		}
		.createArrowColorBgClasses(@num-columns-supported);

		&.popover {
			max-width: 500px;
		}

		border-radius: 0px;
		padding: 0px;

		.popover-content {
			padding: 0px;
			font-family: var(--font-default);
		}

		.commit-popover-details {
			transition: none;
			position: relative;
			display: block;
			width: 450px;
			font-size: 0.9em;
			padding: 10px;
			background-color: var(--panel__bg1);
		}

		.avatar {
			display: inline-block;
		}

		.commit-popover-author-details {
			font-weight: 300;
			margin-left: 10px;
			vertical-align: top;
			display: inline-block;
		}

		.commit-popover-ref-panel {
			height: 40px;
			width: 100%;
			padding: 10px;
			font-size: 0.9em;
			background-color: var(--panel__bg2);
		}
	}

	.graph-header {
		.droppable {
			background-color: var(--panel__bg0);

			.drop-left {
				border-left: 1px solid var(--drop-sort-border);
			}

			.drop-right {
				border-right: 1px solid var(--drop-sort-border);
			}
		}

		.columns-btn {
			padding: 0 2px 0 2px;
			border-radius: 0.25rem;
			margin-top: 0.25rem;

			&:not(.small-btn) {
				font-size: var(--fs-2);
			}

			&.small-btn {
				font-size: var(--fs-1);
				padding: 0;
			}

			&-icon {
				font-size: var(--fs-1);
				vertical-align: middle;
				cursor: pointer;
			}
		}

		.columns-filter {
			&.filtering .columns-filter-icon {
				color: var(--text-selected);
			}
		}

		.header-columns-icons {
			top: 0;
			position: absolute;
			right: 0;
			width: fit-content;
		}

		.button {
			cursor: pointer;
			font-size: var(--fs-2);
			justify-content: center;
			align-items: center;
			padding: 0 2px 0 2px;
			text-align: center;
			border-radius: 0.25rem;

			&.active {
				background-color: var(--color-button-background);
			}

			&:hover {
				background-color: var(--color-button-background);
			}
		}

		.spinner {
			position: absolute;
			right: 0;
		}
	}

	#opts-popover {
		display: block;
		position: absolute;
		background-color: var(--panel__bg0, #272a31);
		padding: 6px 6px 6px 6px;
		max-width: 300px;
		z-index: 2000;
		margin-top: 0;

		.popover-content {
			padding: 0;
		}

		ul {
			margin-top: 0;
			margin-bottom: 0px;
			padding-inline-start: 0;
		}

		ul > li {
			display: flex;
			align-items: center;
			font-size: var(--fs-2);
			font-family: var(--font-default);

			&:hover {
				cursor: pointer;
				background-color: var(--hover-row);
			}
		}
	}

	.graph-container {
		height: 100%;
		position: relative;

		.scrollbar-container {
			.scrollbar-outer.horizontal {
				z-index: 2;
			}
		}

		.createColorBgClasses(@column-number) when (@column-number > 0) {
			.getColumnColors(@column-number);

			.commit-popover-bg-color-@{column-number} {
				.arrow {
					border-right-color: @column-color-f10;

					&:after {
						border-right-color: @column-color-f10;
					}
				}
			}

			.column-@{column-number} {
				&.is-selected {
					.commit-bg-color {
						background-color: @column-color-f50;
						border-radius: 0;
					}
				}

				.color-strip {
					background-color: @column-color;
				}

				.commit-bg-color {
					background-color: @column-color-f10;
				}

				.ref-line {
					border-color: @column-color;
					opacity: 0.25;

					&.is-active {
						border-width: 2px;
						opacity: 1;
					}
				}
			}

			.createColorBgClasses((@column-number - 1));
		}
		.createColorBgClasses(@num-columns-supported);

		.commit-zone {
			transition:
				opacity 0.8s ease-out,
				opacity 0.3s ease,
				border 0.8s ease-out;
			width: 100%;
			position: relative;
			cursor: pointer;

			.ref-line {
				position: absolute;
				left: 0px;
				margin-top: ((@ref-line-margin * -1) + 0px);
			}

			&.merge-node .ref-line {
				margin-top: ((@ref-line-margin * -1) - 1px);
			}

			&.create-ref-node,
			&.create-ref-node-wip {
				color: #bababa;
			}

			&.stash-node {
				.message {
					font-style: italic;
					color: var(--text-secondary);
				}
			}
		}

		// Column-specific styling
		#commit-message-zone {
			color: var(--text-normal);
		}

		#commit-author-zone,
		#commit-changes-zone,
		#commit-date-time-zone,
		#commit-sha-zone {
			color: var(--text-disabled);
		}

		// PADDING
		.pt3 {
			padding-top: 3px;
		}
		.pb3 {
			padding-bottom: 3px;
		}

		.message-zone--body {
			padding-left: 8px;
			opacity: 0.6;
		}

		.time-line {
			border-top: 1px solid var(--section-border);
		}

		.changes-zone {
			display: inline-block;
			height: 100%;
			width: 100%;
			&.selected {
				.changes-bar {
					filter: none;
				}

				.changes-files {
					color: var(--text-selected-row);
				}
			}

			.changes-files {
				color: var(--text-disabled);
				display: inline-block;
			}

			.changes-bar {
				filter: opacity(0.7) saturate(0.6);

				.changes-sub-bar {
					height: var(--stats-bar-height);
					display: inline-block;
					min-width: 5px;

					&.added {
						background-color: var(--stats-added-color);
					}

					&.deleted {
						background-color: var(--stats-deleted-color);
					}

					&.files {
						background-color: var(--stats-files-color);
					}

					&:last-child {
						margin-left: 1px;
						border-radius: 0 var(--stats-bar-border-radius) var(--stats-bar-border-radius) 0;
					}
				}
			}
		}

		.createColorBorderClasses(@column-number) when (@column-number > 0) {
			.getColumnColors(@column-number);

			.column-@{column-number} {
				.node {
					border: 2px solid @column-color;

					&.merge-node {
						background-color: @column-color;
					}
				}

				.node.work-dir-changes,
				.node.unsupported-rebase-warning-node,
				.node.stash-node,
				.node.merge-conflict-node {
					border: 2px dotted @column-color;
				}

				div.node.stash-node {
					-webkit-border-radius: 0;
					border-radius: 0;
					color: @column-color;
					padding-left: 2px;
					padding-top: 1px;
					font-size: var(--fs-2, 1.2rem);

					&:before {
						font-family: inherit;
						content: inherit;
					}

					&.compact {
						border: 0px;
						background-image: linear-gradient(
							135deg,
							@column-color 33.33%,
							var(--panel__bg0) 33.33%,
							var(--panel__bg0) 50%,
							@column-color 50%,
							@column-color 83.33%,
							var(--panel__bg0) 83.33%,
							var(--panel__bg0) 100%
						);
						background-size: 4.24px 4.24px;
					}
				}
			}
			.createColorBorderClasses((@column-number - 1));
		}
		.createColorBorderClasses(@num-columns-supported);

		.node {
			border-radius: 50%;
			position: relative;
			background-color: var(--app__bg0);

			&.merge-node,
			&.unsupported-rebase-warning-node,
			&.merge-conflict-node {
				display: inline-block;
			}
		}

		.graph-zone-column.is-hovering .graph-row {
			background-color: var(--hover-row);
			outline: var(--hover-row-border);
		}

		.graph-zone-column {
			font-size: var(--fs-2);

			.time-line-message-container {
				.time-line-message {
					background-color: var(--panel__bg1);
					border-radius: 2px;
					color: var(--text-secondary);
					padding: 0px 6px 1px 6px;
					font-size: var(--fs-1);
					position: absolute;
					top: -8px;
					right: 0;
					white-space: nowrap;
				}
			}

			.graph-row {
				transition: color 0.2s ease-in;

				.graph-zone {
					margin-left: 8px;
					padding-right: 5px;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
					width: 100%;
					display: inline-block;
					color: inherit;

					&.has-work-dir {
						width: auto;
					}
				}

				&.dimmed-row {
					color: var(--text-dimmed);
					transition: color 0.5s ease-out 1s;

					.summary .tiny-files-readout > :not(.tiny-files-readout-text) {
						opacity: 0.3;
						transition: opacity 0.5s ease-out 1s;
					}
				}

				&.is-selected {
					background-color: var(--selected-row);
					color: var(--text-selected-row);
					outline: var(--selected-row-border);

					&.dimmed-row {
						color: var(--text-dimmed-selected);

						.summary .tiny-files-readout > :not(.tiny-files-readout-text) {
							opacity: 0.5;
						}
					}
				}

				&.unsupported-rebase-warning-node {
					background-color: var(--danger-row);
				}

				&.merge-conflict-node {
					background-color: var(--warning-row);
				}

				.work-dir-input {
					display: inline-block;
					max-width: calc(100% - 75px);
					min-width: 100px;

					input {
						height: 22px;
					}
				}

				input {
					margin-left: 3px;
					width: calc(100% - 3px);
					border: 0px;
					box-shadow: inset 0 0 0;
					letter-spacing: 0.02em;
					font-size: var(--fs-2);
					background-color: var(--input__bg);
					border: 1px solid var(--section-border);
					color: var(--text-normal);

					&:focus {
						border-color: var(--blue);
						box-shadow: 0 0 0 1px var(--blue);
					}
				}

				.text-width {
					visibility: hidden;
					position: absolute;
				}

				.summary {
					display: flex;
					align-items: center;
					margin-left: 15px;
					color: inherit;

					> span {
						display: flex;
						align-items: center;
						margin-right: 6px;
					}

					.tiny-files-readout > :not(.tiny-files-readout-text) {
						transition: opacity 0.2s ease-in;
					}

					.file-type-icon {
						font-size: var(--fs-3);
						vertical-align: middle;
						padding: 0px 3px 0px 0px;
						opacity: 0.87;
					}

					.added {
						color: var(--green);
					}

					.deleted {
						color: var(--red);
					}

					.modified {
						color: var(--orange);
					}
				}
			}
		}

		&.graph-highlighted {
			.graph-zone-column {
				.graph-row {
					transition: color 0.2s ease-out;

					&:not(.is-selected):not(.is-highlighted) {
						color: var(--text-dimmed);

						.summary .tiny-files-readout > :not(.tiny-files-readout-text) {
							opacity: 0.3;
						}
					}

					&.is-selected:not(.is-highlighted) {
						color: var(--text-dimmed-selected);

						.summary .tiny-files-readout > :not(.tiny-files-readout-text) {
							opacity: 0.5;
						}
					}
				}

				&.is-hovering {
					.graph-row {
						&:not(.is-selected):not(.is-highlighted) {
							color: var(--text-dimmed-selected);
							transition: color 0.1s ease-in-out;
						}
					}
				}
			}
		}

		.graph-scroll-markers {
			pointer-events: none;
			position: absolute;
			right: -0.5px;
			z-index: 1;
		}
	}
}
