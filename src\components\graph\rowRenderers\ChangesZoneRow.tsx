import type { <PERSON>ridCellProps, GridCellRenderer } from 'react-virtualized';
import type { ReactElement } from 'react';
import React from 'react';
import { mergeNodeType, workDirType } from '../../../domain/commit/CommitConstants';
import type { GraphZoneType } from '../../../domain/graph/GraphConstants';
import { changesZone } from '../../../domain/graph/GraphConstants';
import { GraphRowHelper } from '../../../domain/graph/GraphHelpers';
import type {
	CommonGraphRowDispatchProps,
	CommonGraphRowProps,
	GraphItemContext,
	ProcessedGraphRow,
	RowStats,
	RowStatsConstraints,
} from '../../../domain/graph/GraphTypes';
import type { GetRealKeyForCellFunc } from '../../../domain/reactvirtualized/ReactVirtualizedHelpers';
import ChangesBar from '../changeszone/ChangesBar';
import GenericGraphZone from '../common/GenericGraphZone';

interface Props extends CommonGraphRowProps, CommonGraphRowDispatchProps {
	rowStatsConstraints: RowStatsConstraints;
	rowsStats?: Record<string, RowStats>;
}

const makeChangesZoneRowRenderer = (props: Props, getKeyForCell: GetRealKeyForCellFunc): GridCellRenderer => {
	const {
		processedRows,
		rowStatsConstraints,
		getExternalIcon,
		highlightRowsOnRefHover,
		isInUnsupportedRebase,
		numGraphColumns,
		clearCurrentlyHoveredGraphCommit,
		currentlyHoveredCommitSha,
		dimMergeCommits,
		dimRowsOfSelectedCommit,
		onCommitContextMenu,
		onClickCommit,
		onDoubleClickCommit,
		rowsStats,
		setAsCurrentlyHoveredGraphCommit,
		suppressNonRefRowTooltips,
	} = props;

	const rowHelper: GraphRowHelper = new GraphRowHelper(props);

	const rowRenderer = ({ rowIndex: index, style }: GridCellProps): ReactElement<any> | undefined => {
		const key: string = getKeyForCell(index);

		const graphRow: ProcessedGraphRow = processedRows[index];
		const { sha, type, contexts } = graphRow;
		const context: GraphItemContext | undefined = contexts?.stats || undefined;
		const rowContext: GraphItemContext | undefined = contexts?.row || undefined;

		const stats = rowsStats?.[sha];
		const filesText: string = stats?.files ? `${stats.files} files changed` : '';
		const linesAddedText: string = stats?.additions ? `${stats.additions} lines added` : '';
		const linesDeletedText: string = stats?.deletions ? `${stats.deletions} lines deleted` : '';
		let statsText: string = [filesText, linesAddedText, linesDeletedText].filter(Boolean).join(', ');
		statsText = type !== workDirType ? statsText : '';
		const zoneType: GraphZoneType = changesZone;
		const showColorStrip = rowHelper.isColumnFollowingGraphColumn(zoneType);
		const isLastColumn = rowHelper.isLastColumn(zoneType);
		const zoneWidth = rowHelper.getZoneWidth(zoneType);
		const verticalScrollWidth: number = rowHelper.getVerticalScrollWidth(zoneType);

		return (
			<GenericGraphZone
				clearCurrentlyHoveredGraphCommit={clearCurrentlyHoveredGraphCommit}
				column={graphRow.column}
				columnForColoring={graphRow.columnForColoring}
				context={context}
				currentlyHoveredCommitSha={currentlyHoveredCommitSha}
				dimRowsOfSelectedCommit={dimRowsOfSelectedCommit}
				getExternalIcon={getExternalIcon}
				graphZoneType={zoneType}
				highlightRowsOnRefHover={highlightRowsOnRefHover}
				isDimmedMergeCommitRow={type === mergeNodeType && dimMergeCommits}
				isHighlighted={rowHelper.isHighlighted(index)}
				isHovering={rowHelper.isHovering(index)}
				isInUnsupportedRebase={isInUnsupportedRebase}
				isLastColumn={isLastColumn}
				isMissingHoveredRefGroup={rowHelper.isMissingHoveredRefGroup(index)}
				isSelected={rowHelper.isSelected(index)}
				key={key}
				numGraphColumns={numGraphColumns}
				onClickCommit={onClickCommit}
				onContextMenu={onCommitContextMenu}
				onDoubleClickCommit={onDoubleClickCommit}
				rowContext={rowContext}
				setAsCurrentlyHoveredGraphCommit={setAsCurrentlyHoveredGraphCommit}
				sha={sha}
				showColorStrip={showColorStrip}
				showTimeline={rowHelper.hasTimeline(index)}
				style={style}
				title={suppressNonRefRowTooltips ? undefined : statsText}
				type={type}
				verticalScrollWidth={verticalScrollWidth}
				zoneWidth={zoneWidth}
			>
				{stats && type !== workDirType ? (
					<ChangesBar
						constraints={rowStatsConstraints}
						getExternalIcon={getExternalIcon}
						isLastColumn={isLastColumn}
						isRowSelected={rowHelper.isSelected(index)}
						stats={stats}
						verticalScrollWidth={verticalScrollWidth}
						zoneWidth={zoneWidth}
					/>
				) : null}
			</GenericGraphZone>
		);
	};

	return rowRenderer;
};

export default makeChangesZoneRowRenderer;
