# This workflow will run tests using node and then publish a package to GitHub Packages when a release is created
# For more information see: https://help.github.com/actions/language-and-framework-guides/publishing-nodejs-packages

name: Publish

on:
    push:
        tags:
            - 'v*.*.*' # Push events to matching v*, i.e. v1.0, v20.15.10

jobs:
    publish:
        runs-on: ubuntu-latest
        steps:
            - name: Code Checkout
              uses: actions/checkout@v4
            - name: Node Setup
              uses: actions/setup-node@v4
              with:
                  node-version: '22'
                  registry-url: https://registry.npmjs.org/
            - name: Install with Lock Check
              run: yarn --frozen-lockfile
            - name: Parse semver string
              id: semver_parser
              uses: booxmedialtd/ws-action-parse-semver@v1.4.7
              with:
                  input_string: ${{ github.ref_name }}
                  version_extractor_regex: 'v(.*)$'
            - name: Publish Package as Public
              if: ${{ ! steps.semver_parser.outputs.prerelease }}
              run: yarn publish --access=public --non-interactive
              env:
                  NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN_GITKRAKEN}}
            - name: Publish Package as prerelease with tag
              if: ${{ steps.semver_parser.outputs.prerelease }}
              run: yarn publish --tag="${{ steps.semver_parser.outputs.prerelease }}" --access=public --non-interactive
              env:
                  NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN_GITKRAKEN}}
