import classnames from 'classnames';
import type { CSSProperties, ReactElement, ReactNode } from 'react';
import React from 'react';
import { parseContext } from '../../../domain/graph/GraphHelpers';
import type { GraphItemContext } from '../../../domain/graph/GraphTypes';

type DataURLBase64 = string;
type ImgCacheByUrl = { [url: string]: DataURLBase64 };

const imgCacheByUrl: ImgCacheByUrl = {};

const canvas: HTMLCanvasElement = document.createElement('canvas');
canvas.id = 'gk-graph-avatar-canvas';

type Props = {
	avatarClassName?: string;
	avatarStyle?: CSSProperties;
	children?: ReactNode;
	className?: string;
	context?: GraphItemContext | null;
	enableTransparentBackground?: boolean;
	style?: CSSProperties;
	height?: number;
	hint?: string;
	size: number;
	url?: string;
	width?: number;
};

type State = {
	dataURL?: DataURLBase64;
};

class Avatar extends React.Component<Props, State> {
	private image: HTMLImageElement | undefined;

	constructor(props: Props) {
		super(props);

		this.image = new Image();
		this.image.crossOrigin = 'anonymous';
		this.image.onload = () => this.refreshImage();

		this.state = {
			dataURL: undefined,
		};
	}

	static getDerivedStateFromProps(props: Props, state: State): State {
		return {
			dataURL: !props.url && state.dataURL ? undefined : state.dataURL,
		};
	}

	override componentDidMount(): void {
		const { url } = this.props;

		if (url) {
			this.startImgLoading(url);
		}
	}

	override componentDidUpdate(prevProps: Props): void {
		const { url } = this.props;

		if (url && prevProps.url !== url) {
			this.startImgLoading(url);
		}
	}

	override componentWillUnmount(): void {
		this.image = undefined;
	}

	private refreshImage(): void {
		if (!this.image) {
			return;
		}

		let dataURL: string | undefined;
		const loadComplete: boolean = this.image.complete;

		if (loadComplete) {
			const curHeight = this.image.height;
			const curWidth = this.image.width;

			const canvasCtx = canvas.getContext('2d');
			canvasCtx.canvas.width = curWidth;
			canvasCtx.canvas.height = curHeight;
			canvasCtx.drawImage(this.image, 0, 0, curWidth, curHeight);

			dataURL = canvas.toDataURL();
			imgCacheByUrl[this.image.src] = dataURL;
		}

		this.setState({
			dataURL: dataURL,
		});
	}

	startImgLoading(url: string): void {
		if (!this.image) {
			return;
		}

		const dataURL = imgCacheByUrl[url];
		if (dataURL) {
			this.setState({
				dataURL: dataURL,
			});

			return;
		}

		this.image.src = url;
	}

	override render(): ReactElement<any> {
		const {
			avatarClassName,
			avatarStyle,
			children,
			className,
			context,
			enableTransparentBackground,
			style,
			height,
			hint,
			size,
			width,
		} = this.props;

		const { dataURL } = this.state;

		const showDefaultIcon = !dataURL;
		const backgroundColor: string = enableTransparentBackground ? '' : 'var(--app__bg0, #1c1e23)';

		const styles: CSSProperties | undefined = showDefaultIcon
			? style
			: {
					...style,
					...avatarStyle,
					background: `url(${dataURL}) 0% 0% / ${size}px no-repeat ${backgroundColor}`,
					height: height || size,
					width: width || size,
			  };

		const classes: string = classnames(
			className,
			showDefaultIcon ? '' : avatarClassName,
			!showDefaultIcon && {
				avatar: true,
			},
		);

		const maybeDefaultIcon: ReactElement<any> | undefined = showDefaultIcon ? <>{children}</> : undefined;

		return (
			<div
				className={classes}
				data-test-class={dataURL ? 'avatar' : 'local-avatar'}
				data-vscode-context={parseContext(context)}
				style={styles}
				title={hint}
			>
				{maybeDefaultIcon}
			</div>
		);
	}
}

export default Avatar;
