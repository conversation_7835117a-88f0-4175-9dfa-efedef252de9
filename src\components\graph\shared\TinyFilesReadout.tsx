import type { ReactElement } from 'react';
import React from 'react';
import { types } from '../../../domain/diff/DiffConstants';
import type { DiffType } from '../../../domain/diff/DiffTypes';
import {
	TINY_FILES_READOUT_FONT_SIZE,
	TINY_FILES_READOUT_RIGHT_MARGIN,
	TINY_ICON_RIGHT_MARGIN,
	TINY_ICON_SIZE,
} from '../../../domain/graph/GraphConstants';
import type { GetExternalIcon } from '../../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';
import FileDiffTypeIcon from './FileDiffTypeIcon';

// The keys are set up as contiguous strings (as opposed to constructed) so that they can be found easily by text search
const { ADDED, DELETED, MODIFIED, RENAMED } = types;
const FULL_TEXT_TRANSLATION_KEYS = {
	[ADDED]: 'CommitDiffSection-NFilesAdded',
	[DELETED]: 'CommitDiffSection-NFilesDeleted',
	[MODIFIED]: 'CommitDiffSection-NFilesModified',
	[RENAMED]: 'CommitDiffSection-NFilesRenamed',
};

type Props = {
	count: number;
	diffType: DiffType;
	fileNodeListStyle?: boolean;
	getExternalIcon: GetExternalIcon;
	translate: TranslationFn;
};

function TinyFilesReadout({
	count,
	diffType,
	getExternalIcon,
	fileNodeListStyle,
	translate,
}: Props): ReactElement<'span'> {
	const text = fileNodeListStyle ? count : translate(FULL_TEXT_TRANSLATION_KEYS[diffType], count);

	let wrapperStyle;
	let iconStyle;
	if (fileNodeListStyle) {
		wrapperStyle = {
			fontSize: TINY_FILES_READOUT_FONT_SIZE,
			marginRight: TINY_FILES_READOUT_RIGHT_MARGIN,
		};

		iconStyle = {
			fontSize: TINY_ICON_SIZE,
			margin: `0px ${TINY_ICON_RIGHT_MARGIN}px 0px 0px`,
		};
	}

	return (
		<span className="tiny-files-readout" style={wrapperStyle}>
			<FileDiffTypeIcon
				fileDiffType={diffType}
				getExternalIcon={getExternalIcon}
				style={iconStyle}
				translate={translate}
			/>
			<span className="tiny-files-readout-text">{text}</span>
		</span>
	);
}

export default TinyFilesReadout;
