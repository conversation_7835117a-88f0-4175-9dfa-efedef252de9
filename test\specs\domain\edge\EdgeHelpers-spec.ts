import { expect } from 'chai';

import {
	commitNodeType,
	mergeConflictNodeType,
	mergeNodeType,
	stashNodeType,
	unsupportedRebaseWarningNodeType,
	workDirType,
} from '../../../../src/domain/commit/CommitConstants';

import * as EdgeHelpers from '../../../../src/domain/edge/EdgeHelpers';
import * as GraphHelpers from '../../../../src/domain/graph/GraphHelpers';
import {
	GraphZoneModeConstants,
	LineSvgProps,
	RowEdges,
	SvgElementName,
	SvgProps,
} from '../../../../src/domain/graph/GraphTypes';
import { CommitType } from '../../../../src/domain/commit/CommitTypes';

type SvgStylesMock = {
	fill?: string;
	shapeRendering?: string;
	strokeLinejoin?: string;
	strokeWidth?: number;
	strokeDasharray?: number;
	stroke?: string;
};

describe('Edge Helpers', function () {
	const mockGraphZoneModeConstants: GraphZoneModeConstants = GraphHelpers.getGraphZoneModeConstants();

	const mockSvgStyles: SvgStylesMock = {
		fill: 'none',
		shapeRendering: 'auto',
		strokeLinejoin: 'round',
		strokeWidth: mockGraphZoneModeConstants.COMMIT_ZONE_LINE_WIDTH,
		strokeDasharray: 2,
		stroke: '#480A69',
	};

	const mockLineSvgProps = {
		...mockSvgStyles,
		x1: 8,
		x2: 21,
		y1: 12,
		y2: 30,
	};

	const mockArcSvgProps = {
		...mockSvgStyles,
		d: '8_12_90_180',
	};

	const mockArcSvgPropsWithUndefinedProp = {
		...mockArcSvgProps,
	};
	delete mockArcSvgPropsWithUndefinedProp.strokeWidth;

	const mockArcSvgPropsWithUnknownProp = {
		...mockArcSvgProps,
		unknownProp1: 'test1',
	};

	const mockLineSvgPropsWithUndefinedProp = {
		...mockLineSvgProps,
	};
	delete mockLineSvgPropsWithUndefinedProp.shapeRendering;

	const mockLineSvgPropsWithUnknownProp = {
		...mockLineSvgProps,
		unknownProp2: 'test2',
	};

	describe('getXValueAtColumn', function () {
		const testCases: any[] = [];
		const initialValue = 17;
		const offset = 22;

		let result = initialValue;

		for (let i = 0; i < 20; i += 1) {
			result += offset;
			const desc = `For column ${i}, it should return value: ${result}`;
			testCases.push({ desc, column: i, result });
		}

		result = initialValue;
		for (let i = 0; i < 20; i += 1) {
			result += offset;
			const desc = `For column ${i}, it should return cached value: ${result}`;
			testCases.push({ desc, column: i, result });
		}

		testCases.forEach(testCase => {
			it(testCase.desc, function () {
				expect(
					EdgeHelpers.getXValueAtColumn(
						testCase.column,
						mockGraphZoneModeConstants.COMMIT_COLUMN_WIDTH,
						mockGraphZoneModeConstants.COMMIT_ZONE_GUTTER_WIDTH,
					),
				).to.equal(testCase.result);
			});
		});
	});

	describe('getSvgElement', function () {
		const testCases = [
			// eslint-disable-next-line max-len
			{
				svgElementName: 'path',
				svgProps: mockArcSvgProps,
				result: '<path d="8_12_90_180" fill="none" shape-rendering="auto" stroke-linejoin="round" stroke-width="2" stroke-dasharray="2" stroke="#480A69" />',
			},
			// eslint-disable-next-line max-len
			{
				svgElementName: 'path',
				svgProps: mockArcSvgPropsWithUndefinedProp,
				result: '<path d="8_12_90_180" fill="none" shape-rendering="auto" stroke-linejoin="round" stroke-dasharray="2" stroke="#480A69" />',
			},
			// eslint-disable-next-line max-len
			{
				svgElementName: 'path',
				svgProps: mockArcSvgPropsWithUnknownProp,
				result: '<path d="8_12_90_180" fill="none" shape-rendering="auto" stroke-linejoin="round" stroke-width="2" stroke-dasharray="2" stroke="#480A69" />',
			},
			// eslint-disable-next-line max-len
			{
				svgElementName: 'line',
				svgProps: mockLineSvgProps,
				result: '<line fill="none" shape-rendering="auto" stroke-linejoin="round" stroke-width="2" stroke-dasharray="2" stroke="#480A69" x1="8" x2="21" y1="12" y2="30" />',
			},
			// eslint-disable-next-line max-len
			{
				svgElementName: 'line',
				svgProps: mockLineSvgPropsWithUndefinedProp,
				result: '<line fill="none" stroke-linejoin="round" stroke-width="2" stroke-dasharray="2" stroke="#480A69" x1="8" x2="21" y1="12" y2="30" />',
			},
			// eslint-disable-next-line max-len
			{
				svgElementName: 'line',
				svgProps: mockLineSvgPropsWithUnknownProp,
				result: '<line fill="none" shape-rendering="auto" stroke-linejoin="round" stroke-width="2" stroke-dasharray="2" stroke="#480A69" x1="8" x2="21" y1="12" y2="30" />',
			},
		];

		testCases.forEach(testCase => {
			const { svgElementName, svgProps, result } = testCase;
			const input = `SVG name: '${svgElementName}' and props ${JSON.stringify(svgProps)}`;

			it(`Check ${input}`, function () {
				expect(EdgeHelpers.getSvgElement(svgElementName as SvgElementName, svgProps as SvgProps)).to.equal(
					result,
				);
			});
		});
	});

	describe('getLineElement', function () {
		const testCases = [
			// eslint-disable-next-line max-len
			{
				svgProps: mockLineSvgProps,
				result: '<line fill="none" shape-rendering="auto" stroke-linejoin="round" stroke-width="2" stroke-dasharray="2" stroke="#480A69" x1="8" x2="21" y1="12" y2="30" />',
			},
			// eslint-disable-next-line max-len
			{
				svgProps: mockLineSvgPropsWithUndefinedProp,
				result: '<line fill="none" stroke-linejoin="round" stroke-width="2" stroke-dasharray="2" stroke="#480A69" x1="8" x2="21" y1="12" y2="30" />',
			},
			// eslint-disable-next-line max-len
			{
				svgProps: mockLineSvgPropsWithUnknownProp,
				result: '<line fill="none" shape-rendering="auto" stroke-linejoin="round" stroke-width="2" stroke-dasharray="2" stroke="#480A69" x1="8" x2="21" y1="12" y2="30" />',
			},
		];

		testCases.forEach(testCase => {
			it(`Check ${JSON.stringify(testCase.svgProps)}`, function () {
				expect(EdgeHelpers.getLineElement(testCase.svgProps as LineSvgProps)).to.equal(testCase.result);
			});
		});
	});

	describe('getSvgStyles', function () {
		const mockSvgStylesWithoutPendingChanges = {
			fill: 'none',
			shapeRendering: 'auto',
			strokeLinejoin: 'round',
			strokeWidth: mockGraphZoneModeConstants.COMMIT_ZONE_LINE_WIDTH,
			strokeDasharray: 0,
			stroke: '#480A69',
		};

		const mockSvgStylesWithPendingChanges = {
			...mockSvgStylesWithoutPendingChanges,
			strokeDasharray: 2,
		};

		const testCases = [
			{ type: commitNodeType, color: '#480A69', result: mockSvgStylesWithoutPendingChanges },
			{ type: mergeNodeType, color: '#480A69', result: mockSvgStylesWithoutPendingChanges },
			{ type: stashNodeType, color: '#480A69', result: mockSvgStylesWithPendingChanges },
			{ type: mergeConflictNodeType, color: '#480A69', result: mockSvgStylesWithPendingChanges },
			{ type: unsupportedRebaseWarningNodeType, color: '#480A69', result: mockSvgStylesWithPendingChanges },
			{ type: workDirType, color: '#480A69', result: mockSvgStylesWithPendingChanges },
		];

		testCases.forEach(testCase => {
			it(`Check type '${testCase.type}' and color '${testCase.color}'`, function () {
				expect(
					EdgeHelpers.getSvgStyles(
						testCase.type as CommitType,
						testCase.color,
						mockGraphZoneModeConstants.COMMIT_ZONE_LINE_WIDTH,
					),
				).to.eql(testCase.result);
			});
		});
	});

	describe('buildStartingOrEndingEdgeHash', function () {
		const testCases = [
			{ edgeColumn: 1, nodeColumn: 2, type: undefined, result: '1_2_+' },
			{ edgeColumn: 3, nodeColumn: 4, type: commitNodeType, result: `3_4_${commitNodeType}` },
			{ edgeColumn: 5, nodeColumn: 6, type: mergeNodeType, result: `5_6_${mergeNodeType}` },
			{ edgeColumn: 7, nodeColumn: 8, type: stashNodeType, result: `7_8_${stashNodeType}` },
			{ edgeColumn: 9, nodeColumn: 10, type: mergeConflictNodeType, result: `9_10_${mergeConflictNodeType}` },
			// eslint-disable-next-line max-len
			{
				edgeColumn: 11,
				nodeColumn: 12,
				type: unsupportedRebaseWarningNodeType,
				result: `11_12_${unsupportedRebaseWarningNodeType}`,
			},
			{ edgeColumn: 13, nodeColumn: 14, type: workDirType, result: `13_14_${workDirType}` },
		];

		testCases.forEach(testCase => {
			const { edgeColumn, nodeColumn, type, result } = testCase;
			it(`Check edgeColumn ${edgeColumn}, nodeColumn ${nodeColumn} and type '${
				type || 'undefined'
			}'`, function () {
				expect(
					EdgeHelpers.buildStartingOrEndingEdgeHash(
						edgeColumn,
						nodeColumn,
						type as CommitType,
						mockGraphZoneModeConstants.IS_COMPACT,
					),
				).to.equal(result);
			});
		});
	});

	describe('buildPassThroughEdgeHash', function () {
		const testCases = [
			{ column: 0, type: undefined, result: '0_+' },
			{ column: 1, type: commitNodeType, result: `1_${commitNodeType}` },
			{ column: 2, type: mergeNodeType, result: `2_${mergeNodeType}` },
			{ column: 3, type: stashNodeType, result: `3_${stashNodeType}` },
			{ column: 4, type: mergeConflictNodeType, result: `4_${mergeConflictNodeType}` },
			{ column: 5, type: unsupportedRebaseWarningNodeType, result: `5_${unsupportedRebaseWarningNodeType}` },
			{ column: 6, type: workDirType, result: `6_${workDirType}` },
		];

		testCases.forEach(testCase => {
			const { column, type, result } = testCase;
			it(`For edgeColumn ${column} and type '${type || 'undefined'}'; it should return '${result}'`, function () {
				expect(EdgeHelpers.buildPassThroughEdgeHash(column, type as CommitType)).to.equal(testCase.result);
			});
		});
	});

	describe('buildEdgeHash', function () {
		const mockEdgeCommit = {
			parentSha: '6960f1bc9a2418fd6e8d97983b160ad404274631',
			type: commitNodeType,
		};

		const mockEdgeMerge = {
			parentSha: '6960f1bc9a2418fd6e8d97983b160ad404274631',
			type: mergeNodeType,
		};

		const mockEdgeStash = {
			parentSha: '6960f1bc9a2418fd6e8d97983b160ad404274631',
			type: stashNodeType,
		};

		const testCases = [
			{
				desc: 'Check one columns without edges',
				edges: {
					'0': {},
				},
				edgeColumnMax: 1,
				nodeColumn: 0,
				result: '_0_0_+_0_0_+_0_+_1_0_+_1_0_+_1_+',
			},
			{
				desc: 'Check one column ending, passThrough and starting commit edges types',
				edges: {
					'0': {
						ending: mockEdgeCommit,
						passThrough: mockEdgeCommit,
						starting: mockEdgeCommit,
					},
				},
				edgeColumnMax: 1,
				nodeColumn: 1,
				result: '_0_1_commit-node_0_1_commit-node_0_commit-node_1_1_+_1_1_+_1_+',
			},
			{
				desc: 'Check one column ending and starting commit edges types',
				edges: {
					'0': {
						ending: mockEdgeCommit,
						starting: mockEdgeCommit,
					},
				},
				edgeColumnMax: 1,
				nodeColumn: 1,
				result: '_0_1_commit-node_0_1_commit-node_0_+_1_1_+_1_1_+_1_+',
			},
			{
				desc: 'Check one column ending commit edges types',
				edges: {
					'0': {
						ending: mockEdgeCommit,
					},
				},
				edgeColumnMax: 1,
				nodeColumn: 1,
				result: '_0_1_+_0_1_commit-node_0_+_1_1_+_1_1_+_1_+',
			},
			{
				desc: 'Check one column ending, passThrough and starting merge edges types',
				edges: {
					'0': {
						ending: mockEdgeMerge,
						passThrough: mockEdgeMerge,
						starting: mockEdgeMerge,
					},
				},
				edgeColumnMax: 1,
				nodeColumn: 1,
				result: '_0_1_merge-node_0_1_merge-node_0_merge-node_1_1_+_1_1_+_1_+',
			},
			{
				desc: 'Check one column ending, passThrough and starting stash edges types',
				edges: {
					'0': {
						ending: mockEdgeStash,
						passThrough: mockEdgeStash,
						starting: mockEdgeStash,
					},
				},
				edgeColumnMax: 1,
				nodeColumn: 1,
				result: '_0_1_stash-node_0_1_stash-node_0_stash-node_1_1_+_1_1_+_1_+',
			},
			{
				desc: 'Check one column ending, passThrough and starting stash, merge and commit edges types',
				edges: {
					'0': {
						ending: mockEdgeStash,
						passThrough: mockEdgeMerge,
						starting: mockEdgeCommit,
					},
				},
				edgeColumnMax: 1,
				nodeColumn: 1,
				result: '_0_1_commit-node_0_1_stash-node_0_merge-node_1_1_+_1_1_+_1_+',
			},
		];

		testCases.forEach(testCase => {
			it(testCase.desc, function () {
				const { edges, edgeColumnMax, nodeColumn } = testCase;
				expect(
					EdgeHelpers.buildEdgeHash(
						edges as RowEdges,
						edgeColumnMax,
						nodeColumn,
						mockGraphZoneModeConstants.IS_COMPACT,
					),
				).to.equal(testCase.result);
			});
		});
	});
});
