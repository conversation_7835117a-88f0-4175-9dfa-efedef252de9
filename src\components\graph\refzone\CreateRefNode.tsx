import type { ReactElement } from 'react';
import React from 'react';
import { FormControl, FormGroup } from 'react-bootstrap';
// import onClickOutside from 'react-onclickoutside';
import type { Style } from '../../../domain/generic/GenericTypes';
import type {
	FormatRefShorthand,
	IsRefShorthandValid,
	OnRefCreate,
	OnRefCreateCancel,
	OnRefCreateContextMenu,
	OnRefShorthandChange,
} from '../../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';
import * as RefConstants from '../../../domain/ref/RefConstants';
import type { CreateRefFormData, RefShorthand } from '../../../domain/ref/RefTypes';
import type { OnKeyDown } from '../../../domain/ui/UiTypes';

type CreateRefNodeProps = {
	createRefFormData: CreateRefFormData;
	formatRefShorthand: FormatRefShorthand;
	isRefShorthandValid: IsRefShorthandValid;
	onCancel: OnRefCreateCancel;
	onContextMenu: OnRefCreateContextMenu;
	onRefCreate: OnRefCreate;
	onRefShorthandChange?: OnRefShorthandChange;
	refZoneWidth: number;
	style?: Style | null;
	translate: TranslationFn;
};

type CreateRefNodeState = {
	currentShorthand: RefShorthand;
	initShorthand: RefShorthand;
};

class CreateRefNode extends React.PureComponent<CreateRefNodeProps, CreateRefNodeState> {
	handleClickOutside(): void {
		const { sha, shorthand, type, data } = this.props.createRefFormData;
		this.props.onCancel(shorthand, sha, type, data);
	}

	constructor(props: CreateRefNodeProps) {
		super(props);

		const { createRefFormData, formatRefShorthand } = this.props;
		const { sha, shorthand, type, data } = createRefFormData;

		this.state = {
			currentShorthand: formatRefShorthand(shorthand, sha, type, data),
			initShorthand: shorthand,
		};
	}

	static getDerivedStateFromProps(props: CreateRefNodeProps, state: CreateRefNodeState): CreateRefNodeState | null {
		const { createRefFormData, formatRefShorthand } = props;
		const { currentShorthand, initShorthand } = state;

		const { sha, shorthand, type, data } = createRefFormData;
		const hasInitShorthandChanged: boolean = shorthand !== initShorthand;

		if (hasInitShorthandChanged && formatRefShorthand(shorthand, sha, type, data) !== currentShorthand) {
			return {
				currentShorthand: formatRefShorthand(shorthand, sha, type, data),
				initShorthand: shorthand,
			};
		} else if (hasInitShorthandChanged) {
			return {
				currentShorthand: currentShorthand,
				initShorthand: shorthand,
			};
		}

		return null;
	}

	onInputChange(shorthand?: RefShorthand): void {
		let formattedShorthand: RefShorthand = shorthand || '';

		if (shorthand) {
			const { sha, type, data } = this.props.createRefFormData;
			formattedShorthand = this.props.formatRefShorthand(formattedShorthand, sha, type, data);
		}

		this.setState({ currentShorthand: formattedShorthand });

		this.props.onRefShorthandChange?.(formattedShorthand);
	}

	override render(): ReactElement<'div'> {
		const {
			createRefFormData,
			isRefShorthandValid,
			onCancel,
			onContextMenu,
			onRefCreate,
			refZoneWidth,
			style,
			translate,
		} = this.props;

		const { currentShorthand } = this.state;

		const { data, sha, type } = createRefFormData;
		const isTag: boolean = type === RefConstants.refTypes.TAG;
		const placeholder: string = isTag ? translate('RefZone-EnterTagName') : translate('RefZone-EnterBranchName');

		const onKeyDown: OnKeyDown = (e: React.KeyboardEvent<any>) => {
			e.stopPropagation();

			if (e.key === 'Escape') {
				onCancel(currentShorthand, sha, type, data);
				return;
			}

			if (sha && e.key === 'Enter') {
				onRefCreate(currentShorthand, sha, type, data);
			}
		};

		return (
			<div
				className="create-ref"
				data-testid="create-ref"
				style={{ ...style, minWidth: refZoneWidth, width: refZoneWidth }}
			>
				<FormGroup>
					<FormControl
						autoFocus
						data-testid="create-ref-form-control"
						isInvalid={
							currentShorthand.length > 0 && !isRefShorthandValid(currentShorthand, sha, type, data)
						}
						onChange={e => this.onInputChange((e.target as any)?.value)}
						onContextMenu={e => onContextMenu(e, currentShorthand, sha, type, data)}
						onKeyDown={onKeyDown}
						placeholder={placeholder}
						type="text"
						value={currentShorthand}
					/>
				</FormGroup>
			</div>
		);
	}
}

// TODO: onclickoutside for gk client
export default CreateRefNode;
