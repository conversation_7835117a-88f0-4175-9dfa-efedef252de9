const customDateTimeFormatParserRegex =
	/(?<literal>\[.*?\])|(?<year>YYYY|YY)|(?<month>M{1,4})|(?<day>Do|DD?)|(?<weekday>d{2,4})|(?<hour>HH?|hh?)|(?<minute>mm?)|(?<second>ss?)|(?<fractionalSecond>SSS)|(?<dayPeriod>A|a)|(?<timeZoneName>ZZ?)/g;
const dateTimeFormatRegex = /(?<dateStyle>full|long|medium|short)(?:\+(?<timeStyle>full|long|medium|short))?/;

type DateStyle = 'full' | 'long' | 'medium' | 'short';
type TimeStyle = 'full' | 'long' | 'medium' | 'short';
export type DateTimeFormat = DateStyle | `${DateStyle}+${TimeStyle}`;

const dateTimeFormatCache = new Map<string | undefined, Intl.DateTimeFormat>();
let defaultLocales: string[] | undefined;

export function getDateTimeFormatOptionsFromFormatString(
	format: DateTimeFormat | string | undefined,
): Intl.DateTimeFormatOptions {
	if (format == null) return { localeMatcher: 'best fit', dateStyle: 'full', timeStyle: 'short' };

	const match = dateTimeFormatRegex.exec(format);
	if (match?.groups != null) {
		const { dateStyle, timeStyle } = match.groups;
		return {
			localeMatcher: 'best fit',
			dateStyle: (dateStyle as DateStyle) || 'full',
			timeStyle: (timeStyle as DateStyle) || undefined,
		};
	}

	const options: Intl.DateTimeFormatOptions = { localeMatcher: 'best fit' };

	for (const { groups } of format.matchAll(customDateTimeFormatParserRegex)) {
		if (groups == null) continue;

		for (const key in groups) {
			const value = groups[key];

			if (value == null) continue;

			switch (key) {
				case 'year':
					options.year = value.length === 4 ? 'numeric' : '2-digit';
					break;
				case 'month':
					switch (value.length) {
						case 4:
							options.month = 'long';
							break;
						case 3:
							options.month = 'short';
							break;
						case 2:
							options.month = '2-digit';
							break;
						case 1:
							options.month = 'numeric';
							break;
					}

					break;
				case 'day':
					if (value === 'DD') {
						options.day = '2-digit';
					} else {
						options.day = 'numeric';
					}

					break;
				case 'weekday':
					switch (value.length) {
						case 4:
							options.weekday = 'long';
							break;
						case 3:
							options.weekday = 'short';
							break;
						case 2:
							options.weekday = 'narrow';
							break;
					}

					break;
				case 'hour':
					options.hour = value.length === 2 ? '2-digit' : 'numeric';
					options.hour12 = value === 'hh' || value === 'h';
					break;
				case 'minute':
					options.minute = value.length === 2 ? '2-digit' : 'numeric';
					break;
				case 'second':
					options.second = value.length === 2 ? '2-digit' : 'numeric';
					break;
				case 'fractionalSecond':
					// TODO: review following line. Added cast to "any" as it seems that "fractionalSecondDigits"
					// property does not exists in options object.
					(options as any).fractionalSecondDigits = 3;
					break;
				case 'dayPeriod':
					options.dayPeriod = 'narrow';
					options.hour12 = true;
					options.hourCycle = 'h12';
					break;
				case 'timeZoneName':
					options.timeZoneName = value.length === 2 ? 'long' : 'short';
					break;
			}
		}
	}

	return options;
}

const ordinals = ['th', 'st', 'nd', 'rd'];
export function formatWithOrdinal(n: number): string {
	const v = n % 100;
	return `${n}${ordinals[(v - 20) % 10] ?? ordinals[v] ?? ordinals[0]}`;
}

export function formatDate(
	date: Date | number,
	format: 'full' | 'long' | 'medium' | 'short' | string | null | undefined,
	locale?: string,
	cache = true,
): string {
	format = format ?? undefined;

	const key = `${locale ?? ''}:${format}`;

	let formatter = dateTimeFormatCache.get(key);
	if (formatter == null) {
		const options = getDateTimeFormatOptionsFromFormatString(format);

		let locales;
		if (locale == null) {
			locales = defaultLocales;
		} else if (locale === 'system') {
			locales = undefined;
		} else {
			locales = [locale];
		}

		formatter = new Intl.DateTimeFormat(locales, options);
		if (cache) {
			dateTimeFormatCache.set(key, formatter);
		}
	}

	if (format == null || dateTimeFormatRegex.test(format)) {
		return formatter.format(date);
	}

	function getTimeFormatter(timeFormat: TimeStyle) {
		const timeKey = `${locale ?? ''}:time:${timeFormat}`;

		let timeFormatter = dateTimeFormatCache.get(timeKey);
		if (timeFormatter == null) {
			const options: Intl.DateTimeFormatOptions = { localeMatcher: 'best fit', timeStyle: timeFormat };

			let locales;
			if (locale == null) {
				locales = defaultLocales;
			} else if (locale === 'system') {
				locales = undefined;
			} else {
				locales = [locale];
			}

			timeFormatter = new Intl.DateTimeFormat(locales, options);
			if (cache) {
				dateTimeFormatCache.set(timeKey, timeFormatter);
			}
		}

		return timeFormatter;
	}

	const parts = formatter.formatToParts(date);
	return format.replace(
		customDateTimeFormatParserRegex,
		(
			_match,
			literal,
			_year,
			_month,
			_day,
			_weekday,
			_hour,
			_minute,
			_second,
			_fractionalSecond,
			_dayPeriod,
			_timeZoneName,
			_offset,
			_s,
			groups,
		) => {
			if (literal != null) return (literal as string).substring(1, literal.length - 1);

			for (const currentKey in groups) {
				const value = groups[currentKey];

				if (value == null) continue;

				const part: Intl.DateTimeFormatPart = parts.find((p: Intl.DateTimeFormatPart) => p.type === currentKey);

				if (value === 'Do' && part?.type === 'day') {
					return formatWithOrdinal(Number(part.value));
				}

				if (value === 'a' && part?.type === 'dayPeriod') {
					// For some reason the Intl.DateTimeFormat doesn't honor the `dayPeriod` value
					// and always returns the long version, so use the "short" timeStyle instead
					const dayPeriod = getTimeFormatter('short')
						.formatToParts(date)
						.find((p: Intl.DateTimeFormatPart) => p.type === 'dayPeriod');
					return ` ${(dayPeriod ?? part)?.value ?? ''}`;
				}

				return part?.value ?? '';
			}

			return '';
		},
	);
}
