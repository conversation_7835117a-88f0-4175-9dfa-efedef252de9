import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import { REF_ZONE_TEXT_HEIGHT } from '../../../domain/graph/GraphConstants';

export type RefNameProps = {
	className?: string;
	name: string;
};

function RefName({ className, name }: RefNameProps): ReactElement<'span'> {
	return (
		<span className={classnames('ref-name', 'truncate', className)} style={{ height: REF_ZONE_TEXT_HEIGHT }}>
			{name}
		</span>
	);
}

export default RefName;
