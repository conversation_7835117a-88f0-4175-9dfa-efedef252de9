import type { <PERSON>ridCellP<PERSON>, GridCell<PERSON><PERSON><PERSON> } from 'react-virtualized';
import type { ReactElement } from 'react';
import React from 'react';
import type { GraphZoneType } from '../../../domain/graph/GraphConstants';
import { commitZone } from '../../../domain/graph/GraphConstants';
import { GraphRowHelper } from '../../../domain/graph/GraphHelpers';
import type {
	AvatarUrlByEmail,
	ColumnColorByColumn,
	CommonGraphRowDispatchProps,
	CommonGraphRowProps,
	GetMissingAvatar,
	GraphItemContext,
	GraphZoneModeConstants,
	NodeOffsetByColumn,
	NodeOpacityByColumn,
	ProcessedGraphRow,
} from '../../../domain/graph/GraphTypes';
import type { GetRealKeyForCellFunc } from '../../../domain/reactvirtualized/ReactVirtualizedHelpers';
import CommitZone from '../commitzone/CommitZone';

interface OwnProps {
	avatarUrlByEmail: AvatarUrlByEmail;
	graphZoneModeConstants: GraphZoneModeConstants;
	height: number;
	onMissingAvatar: GetMissingAvatar;
	columnColorByColumn: ColumnColorByColumn;
	leftGutterBoxShadowAlpha: number;
	leftGutterWidth: number;
	nodeOffsetByColumn: NodeOffsetByColumn;
	nodeOpacityByColumn: NodeOpacityByColumn;
	rightGutterBoxShadowAlpha: number;
	rightGutterWidth: number;
	scrollLeft: number;
	shouldShowRefLine: boolean;
	suppressNonRefRowTooltips?: boolean;
}

interface Props extends CommonGraphRowProps, CommonGraphRowDispatchProps, OwnProps {}

function makeCommitZoneRowRenderer(props: Props, getKeyForCell: GetRealKeyForCellFunc): GridCellRenderer {
	const {
		avatarUrlByEmail,
		onMissingAvatar,
		clearCurrentlyHoveredGraphCommit,
		columnColorByColumn,
		cssVariables,
		currentlyHoveredCommitSha,
		processedRows,
		getExternalIcon,
		graphZoneModeConstants,
		isInUnsupportedRebase,
		leftGutterBoxShadowAlpha,
		leftGutterWidth,
		onCommitContextMenu,
		onClickCommit,
		onDoubleClickCommit,
		nodeOffsetByColumn,
		nodeOpacityByColumn,
		numGraphColumns,
		rightGutterBoxShadowAlpha,
		rightGutterWidth,
		setAsCurrentlyHoveredGraphCommit,
		scrollLeft,
		shouldShowRefLine,
		suppressNonRefRowTooltips,
		translate,
		useAuthorInitialsForAvatars,
	} = props;

	const rowHelper: GraphRowHelper = new GraphRowHelper(props);

	const renderer = ({ rowIndex: index, style }: GridCellProps): ReactElement<any> | null => {
		const key: string = getKeyForCell(index);

		const graphRow: ProcessedGraphRow = processedRows[index];
		const zoneType: GraphZoneType = commitZone;
		const commitZoneWidth: number = rowHelper.getZoneWidth(zoneType);
		const { email, author, contexts, message, sha, type } = graphRow;
		const column: number = graphRow.column;
		const zoneContext: GraphItemContext | undefined = contexts?.graph || undefined;
		const avatarContext: GraphItemContext | undefined = contexts?.avatar || undefined;
		const rowContext: GraphItemContext | undefined = contexts?.row || undefined;
		let avatarUrl: string | undefined;
		if (!useAuthorInitialsForAvatars) {
			avatarUrl = avatarUrlByEmail[email];
			if (!avatarUrl) {
				onMissingAvatar(email, sha);
			}
		}

		const isLastColumn = rowHelper.isLastColumn(zoneType);

		return (
			<CommitZone
				authorEmail={email}
				authorName={author}
				avatarContext={avatarContext}
				avatarUrl={avatarUrl}
				clearCurrentlyHoveredGraphCommit={clearCurrentlyHoveredGraphCommit}
				column={column}
				columnColorByColumn={columnColorByColumn}
				columnForColoring={graphRow.columnForColoring}
				commitZoneWidth={commitZoneWidth}
				cssVariables={cssVariables}
				currentlyHoveredCommitSha={currentlyHoveredCommitSha}
				edgeColumnMax={graphRow.edgeColumnMaxes}
				edges={graphRow.edges}
				getExternalIcon={getExternalIcon}
				graphZoneModeConstants={graphZoneModeConstants}
				hasRefs={graphRow.hasRefs || false}
				hasTimeline={rowHelper.hasTimeline(index)}
				isActiveSha={rowHelper.rowContainsCurrentHeadRef(graphRow)}
				isInUnsupportedRebase={isInUnsupportedRebase}
				isLastColumn={isLastColumn}
				isSelected={rowHelper.isSelected(index)}
				key={key}
				leftGutterBoxShadowAlpha={leftGutterBoxShadowAlpha}
				leftGutterWidth={leftGutterWidth}
				message={message}
				nodeOffset={nodeOffsetByColumn[column]}
				nodeOpacity={nodeOpacityByColumn[column]}
				numGraphColumns={numGraphColumns}
				onClickCommit={onClickCommit}
				onContextMenu={onCommitContextMenu}
				onDoubleClickCommit={onDoubleClickCommit}
				rightGutterBoxShadowAlpha={rightGutterBoxShadowAlpha}
				rightGutterWidth={rightGutterWidth}
				rowContext={rowContext}
				scrollLeft={scrollLeft}
				setAsCurrentlyHoveredGraphCommit={setAsCurrentlyHoveredGraphCommit}
				sha={sha}
				shouldShowRefLine={shouldShowRefLine}
				style={style}
				suppressNonRefRowTooltips={suppressNonRefRowTooltips}
				translate={translate}
				type={type}
				useAuthorInitialsForAvatars={useAuthorInitialsForAvatars}
				zoneContext={zoneContext}
			/>
		);
	};

	return renderer;
}

export default makeCommitZoneRowRenderer;
