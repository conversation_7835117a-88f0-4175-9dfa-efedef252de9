import { expect } from 'chai';

import * as LanguageHelpers from '../../../../src/domain/language/LanguageHelpers';

describe('Language Helpers', function () {
	describe('formatString', function () {
		const testCases = [
			{ str: 'test without params', args: [], result: 'test without params' },
			{ str: 'test', args: ['parameter'], result: 'test' },
			{ str: 'test with {0} parameter', args: ['1'], result: 'test with 1 parameter' },
			{ str: 'test with {0} and {1}', args: ['param1', 'param2'], result: 'test with param1 and param2' },
			// eslint-disable-next-line max-len
			{
				str: 'test with {0}, {1} and {2}',
				args: ['param1', 'param2', 'param3'],
				result: 'test with param1, param2 and param3',
			},
		];

		testCases.forEach(testCase => {
			const { str, args, result } = testCase;
			const argsStr = (args || []).toString();

			it(`Check input '${str}', with args [${argsStr}] should return '${result}'`, function () {
				expect(LanguageHelpers.formatString(str, args)).to.equal(result);
			});
		});
	});
});
