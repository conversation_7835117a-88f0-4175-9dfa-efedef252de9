import classnames from 'classnames';
import type { ReactElement, ReactNode, RefObject } from 'react';
import React from 'react';
import type { OnMouseEnter, OnMouseLeave } from '../../../domain/ui/UiTypes';

type RefPopoverStyle = {
	top: number;
};

type Props = {
	children?: ReactNode;
	column: number;
	numGraphColumns: number;
	onMouseEnter?: OnMouseEnter;
	onMouseLeave?: OnMouseLeave;
	style?: RefPopoverStyle;
	ref?: RefObject<HTMLDivElement>;
};

function RefPopover({
	style,
	children,
	column,
	numGraphColumns,
	onMouseEnter,
	onMouseLeave,
	ref,
	...props
}: Props): ReactElement<'div'> {
	return (
		<div
			{...props}
			className={classnames('gk-graph', 'ref-zone', `column-${(column % numGraphColumns) + 1}`)}
			ref={ref}
		>
			<ul
				onMouseEnter={onMouseEnter}
				onMouseLeave={onMouseLeave}
				style={{
					...style,
					listStyle: 'none',
					padding: 0,
					zIndex: 1,
				}}
			>
				{children}
			</ul>
		</div>
	);
}

export default RefPopover;
