import type { ReactElement } from 'react';
import React from 'react';
import ReactDOM from 'react-dom';
import type { Style } from '../../../domain/generic/GenericTypes';
import type { GraphZoneType } from '../../../domain/graph/GraphConstants';
import { graphZoneMetaData } from '../../../domain/graph/GraphConstants';
import type { ChildrenElements } from '../../../domain/ui/UiTypes';

type Props = {
	children: ChildrenElements;
	className?: string;
	height: number;
	heightAdjustment: number;
	graphZoneType: GraphZoneType;
	scrollTop?: number;
	style: Style;
};

function EntireRowSpan({
	children,
	className,
	height,
	heightAdjustment,
	graphZoneType,
	scrollTop,
	style,
}: Props): ReactElement<'div'> {
	// If we are rendering this node, than the zone exists.
	const zoneListId: string = graphZoneMetaData[graphZoneType].listId;
	const graphZone: HTMLElement | null = document.getElementById(zoneListId);

	const scrollHeight: number = graphZone?.scrollHeight || 0;

	let top: number = height - 28;
	if (scrollTop !== undefined) {
		top = Number(style.top) - scrollTop; // this is the intended way to calculate the position
		if (graphZone && scrollHeight - height - scrollTop < 0) {
			// The bad magic 5 lives behind this check
			top = height - heightAdjustment - 28; // this is when the bad magic 5 attacks
		}
	}

	const positionStyle: Style = {
		top: top,
		zIndex: 2,
	};

	const childrenContainer = (
		<span className="absolute" style={positionStyle}>
			{children}
		</span>
	);

	return (
		<div className={className} style={style}>
			{graphZone
				? ReactDOM.createPortal(
						childrenContainer,
						// If we are rendering this node, then the graph-container exists.
						document.getElementById('graph-container'),
				  )
				: childrenContainer}
		</div>
	);
}

export default EntireRowSpan;
