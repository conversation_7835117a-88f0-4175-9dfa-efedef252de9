import type { <PERSON><PERSON><PERSON>, GridCellProps, GridCellRange<PERSON>enderer, ScrollParams } from 'react-virtualized';
import { Grid } from 'react-virtualized';
import classnames from 'classnames';
import type { ReactElement, ReactNode } from 'react';
import React from 'react';
import type { GraphMarkerType, GraphZone, GraphZoneType } from '../../domain/graph/GraphConstants';
import { commitZone, GRAPH_ROW_HEIGHT, refZone, timelineMsgRowRenderId } from '../../domain/graph/GraphConstants';
import {
	getAdjustedRowCount,
	getContentZoneHeight,
	getContentZoneWidth,
	getGraphZoneFromGraphZones,
	getGraphZoneWidthConstraintsFromGraphZones,
	getMustShowMoreCommitsPanel,
	hasScrolledToBottom,
	isLastColumn,
} from '../../domain/graph/GraphHelpers';
import type {
	ColumnContentGenerator,
	GetExternalIcon,
	GraphMarkerColors,
	GraphMarkerRowIndices,
	OnGraphZoneResize,
	OnGraphZoneResizeEnd,
	OnGraphZoneResizeFromPropChange,
	OnScrollForZone,
	RowRenderersByIds,
	RowRenderType,
} from '../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../domain/language/LanguageTypes';
import type { GetRealKeyForCellFunc } from '../../domain/reactvirtualized/ReactVirtualizedHelpers';
import type { OnMouseEnter } from '../../domain/ui/UiTypes';
import Resizable from '../resizable/Resizable';
import ScrollbarContainer from './common/ScrollbarContainer';
import GraphScrollMarker from './GraphScrollMarker';
import CommitsInfoRow from './rowRenderers/CommitsInfoRow';

type GraphColumnProps = {
	branchUpstreamRowIndices: number[];
	rowCount: number;
	columnIndex: number;
	customFooterRow?: ReactElement<any>;
	enableResizer?: boolean;
	enabledScrollMarkerTypes: GraphMarkerType[];
	getExternalIcon: GetExternalIcon;
	getKeyForCell: GetRealKeyForCellFunc;
	graphHeight: number;
	graphWidth: number;
	graphZoneType: GraphZoneType;
	graphZones: GraphZone[];
	hasMoreCommits: boolean;
	horizontalScrollHeight: number;
	isLoadingRows: boolean;
	markerColors: GraphMarkerColors;
	markerRowIndices: GraphMarkerRowIndices;
	makeColumnContents?: ColumnContentGenerator;
	onResize?: OnGraphZoneResize;
	onResizeEnd?: OnGraphZoneResizeEnd;
	onResizeFromPropChange?: OnGraphZoneResizeFromPropChange;
	onScroll: OnScrollForZone;
	onScrollToRowCausedUpdate?: OnScrollForZone;
	onZoneEnter: OnMouseEnter;
	cellRenderersByIds: RowRenderersByIds;
	scrollToAlignment?: Alignment;
	scrollToIndex?: number;
	scrollLeft: number;
	scrollTop: number;
	smartCellRangeRenderer: GridCellRangeRenderer;
	translate: TranslationFn;
	verticalScrollWidth: number;
};

export function GraphColumn({
	branchUpstreamRowIndices,
	rowCount,
	cellRenderersByIds,
	columnIndex,
	customFooterRow,
	enabledScrollMarkerTypes,
	enableResizer,
	getExternalIcon,
	getKeyForCell,
	graphHeight,
	graphWidth,
	graphZoneType,
	graphZones,
	hasMoreCommits,
	horizontalScrollHeight,
	isLoadingRows,
	markerColors,
	markerRowIndices,
	onResize,
	onResizeEnd,
	onResizeFromPropChange,
	onScroll,
	onScrollToRowCausedUpdate,
	onZoneEnter,
	scrollLeft,
	scrollToAlignment,
	scrollToIndex,
	scrollTop,
	smartCellRangeRenderer,
	translate,
	verticalScrollWidth,
}: GraphColumnProps): ReactElement<any> | null {
	const graphZone: GraphZone | undefined = getGraphZoneFromGraphZones(graphZoneType, graphZones);
	if (!graphZone) {
		return null;
	}

	const graphZoneElement: HTMLElement | null = document.getElementById(graphZone.listId);
	const isLastZone: boolean = isLastColumn(graphZoneType, graphZones);

	const mustShowMoreCommitsPanel = getMustShowMoreCommitsPanel(rowCount, hasMoreCommits, isLoadingRows);
	const adjustedRowCount: number = getAdjustedRowCount(rowCount, hasMoreCommits, isLoadingRows);

	const contentZoneWidth: number = getContentZoneWidth(graphZone, rowCount);
	const contentZoneHeight: number = getContentZoneHeight(rowCount, hasMoreCommits, isLoadingRows);

	const hasVerticalScroll = verticalScrollWidth > 0;
	const isScrollMarkerEnabled = enabledScrollMarkerTypes.length > 0;

	const currentClassName: string = classnames(
		'graph-panel',
		'react-virtualized-list',
		'pad-for-horizontal-scrollbar',
		graphZoneType === refZone ? 'z3' : null,
		{
			'pad-for-vertical-scrollbar': hasVerticalScroll,
		},
	);

	const containerStyle =
		graphZoneType === commitZone
			? {
					...(!isLastZone && {
						position: 'static',
					}) /* NOTE this makes the grid unusable with CellMeasurer */,
					...(isLastZone && {
						width: graphZone.currentWidth > contentZoneWidth ? graphZone.currentWidth : contentZoneWidth,
						maxWidth: 'none',
					}),
			  }
			: undefined;

	const onCustomScroll = (params: ScrollParams, prevScrollTop: number) => {
		const newScrollTop: number = params.scrollTop;

		// Case for "onWheel" event when graph column does not have a vertical scrollbar
		if (newScrollTop > prevScrollTop && graphZoneElement && hasScrolledToBottom(graphZoneElement)) {
			return;
		}

		onScroll(graphZoneType, params, graphWidth, graphHeight, hasMoreCommits);
	};

	const onTimelineColumnWheel = (event: React.WheelEvent, prevScrollTop: number) => {
		let newScrollTop: number = prevScrollTop + event.deltaY;
		newScrollTop = newScrollTop >= 0 ? newScrollTop : 0;

		// Case for "onWheel" event when graph column does not have a vertical scrollbar
		if (newScrollTop > prevScrollTop && graphZoneElement && hasScrolledToBottom(graphZoneElement)) {
			return;
		}

		const params: ScrollParams = {
			clientHeight: contentZoneHeight,
			clientWidth: contentZoneWidth,
			scrollHeight: horizontalScrollHeight,
			scrollLeft: 0,
			scrollTop: newScrollTop >= 0 ? newScrollTop : 0,
			scrollWidth: verticalScrollWidth,
		};

		onScroll(graphZoneType, params, graphWidth, graphHeight, hasMoreCommits);
	};

	const decorateRow = (row: ReactNode, cellRenderType: RowRenderType, params: GridCellProps): ReactNode => {
		return (
			<span
				data-column-name={cellRenderType}
				data-row-idx={params.rowIndex}
				key={`gk-row-${params.key}`}
				style={{ top: params.style.top }}
			>
				{row}
			</span>
		);
	};

	const graphColumnCellRenderer = (cellRenderType: RowRenderType, params: GridCellProps): ReactNode => {
		if (params.rowIndex > rowCount - 1) {
			if (mustShowMoreCommitsPanel && columnIndex === 0) {
				return (
					<CommitsInfoRow
						currentCommits={rowCount - 1}
						customFooterRow={customFooterRow}
						getExternalIcon={getExternalIcon}
						graphZoneType={graphZoneType}
						hasMoreCommits={hasMoreCommits}
						height={graphHeight}
						horizontalScrollHeight={horizontalScrollHeight}
						isLoadingRows={isLoadingRows}
						key={getKeyForCell(params.rowIndex)}
						scrollTop={scrollTop}
						style={params.style}
						translate={translate}
					/>
				);
			}

			return undefined;
		}

		const newParams: GridCellProps = { ...params, style: { ...params.style } };

		if (timelineMsgRowRenderId === cellRenderType) {
			// Need to adjust the top property when overflow CSS value is 'visible'.
			// Otherwise, top property will be incorrect when we scroll the grid.
			// This adjustment code is the same as in "EntireRowSpan" component.
			newParams.style.top = Number(params.style.top) - scrollTop;
		}

		const currentCellRenderer = cellRenderersByIds[cellRenderType];
		return currentCellRenderer ? decorateRow(currentCellRenderer(newParams), cellRenderType, params) : undefined;
	};

	const getInternalGrid = (
		cellRenderType: RowRenderType,
		id: string,
		className: string,
		colContainerStyle: object,
		colStyle: object,
		width: number,
		height: number,
		columnWidth: number,
	): ReactNode => (
		<Grid
			autoContainerWidth
			cellRangeRenderer={smartCellRangeRenderer}
			cellRenderer={(params: GridCellProps) => graphColumnCellRenderer(cellRenderType, params)}
			className={className}
			columnCount={1}
			columnWidth={columnWidth}
			containerStyle={colContainerStyle}
			height={height}
			id={id}
			isScrolling={false}
			/*
			NOTE on the isScrolling prop above:
			react-virtualized Grid will not call the cellRenderer while scrolling, which caused gross
			throttle behavior when updating scrollLeft on the CommitZone column. To force the cells to
			rerender even while scrolling, we force isScrolling to false.
			This has the downside of calling the cellRenderer for each cell even when throttling is
			desirable (i.e. while scrolling only vertically).
			Seemingly the only way to improve performance on this, without breaking the column,
			is to define our own cellRangeRenderer (which has not yet been explored).
		*/
			key={id}
			onScrollToTargetCausedUpdate={
				onScrollToRowCausedUpdate
					? (s: ScrollParams) =>
							onScrollToRowCausedUpdate(graphZoneType, s, graphWidth, graphHeight, hasMoreCommits)
					: undefined
			}
			overscanRowCount={0}
			rowCount={adjustedRowCount}
			rowHeight={GRAPH_ROW_HEIGHT}
			scrollLeft={scrollLeft}
			scrollToAlignment={scrollToAlignment}
			scrollTop={scrollTop}
			scrollToRow={scrollToIndex}
			style={colStyle}
			tabIndex={null}
			width={width}
		/>
	);

	const DefaultColumn: ReactNode | undefined = getInternalGrid(
		graphZone.type, // Graph zone type
		graphZone.listId, // Graph zone list id
		currentClassName, // Current className
		containerStyle, // Container style
		{ position: 'absolute' }, // Column style
		graphZone.currentWidth, // width
		graphHeight, // height
		contentZoneWidth, // columnWidth
	);

	const TimelineColumn: ReactNode | undefined = isLastZone ? (
		<div className="timeline-column-container" onWheelCapture={e => onTimelineColumnWheel(e, scrollTop)}>
			{getInternalGrid(
				timelineMsgRowRenderId, // Graph zone type
				'timeline-column', // Graph zone list id
				'graph-panel react-virtualized-list timeline-column', // Current className
				{ overflow: 'visible', zIndex: 3, maxHeight: contentZoneHeight }, // Container style
				{ position: 'absolute', right: 0, zIndex: 3, maxHeight: graphHeight }, // Column style
				1, // width
				graphHeight, // height
				1, // columnWidth
			)}
		</div>
	) : undefined;

	const DefaultColumnWithScrollBars = DefaultColumn && (
		<>
			{TimelineColumn}
			{isScrollMarkerEnabled && isLastZone ? (
				<GraphScrollMarker
					branchUpstreamRowIndices={branchUpstreamRowIndices}
					enabledScrollMarkerTypes={enabledScrollMarkerTypes}
					markerColors={markerColors}
					markerRowIndices={markerRowIndices}
					scrollWidth={verticalScrollWidth}
					totalRows={adjustedRowCount}
					viewportHeight={graphHeight < contentZoneHeight ? graphHeight : contentZoneHeight}
					viewportWidth={graphZone.currentWidth}
				/>
			) : null}
			<ScrollbarContainer
				contentHeight={contentZoneHeight}
				contentWidth={contentZoneWidth}
				displayHorizontalScrollbar={graphZone.type === commitZone}
				displayVerticalScrollbar={isLastZone}
				forceOnVerticalWheel={!isLastZone}
				forceVerticalScrollbar={isLastZone && isScrollMarkerEnabled}
				height={graphHeight}
				onScroll={params => onCustomScroll(params, scrollTop)}
				scrollLeft={scrollLeft}
				scrollTop={scrollTop}
				width={graphZone.currentWidth}
			>
				{DefaultColumn}
			</ScrollbarContainer>
		</>
	);

	const Column: ReactElement<'div'> = <div onMouseEnter={onZoneEnter}>{DefaultColumnWithScrollBars}</div>;

	return isLastZone ? (
		Column
	) : (
		<Resizable
			enable={enableResizer}
			height={graphHeight}
			id={`${graphZoneType}Column`}
			key={`${graphZoneType}Column`}
			onResize={onResize ? dimensions => onResize(graphZone, dimensions) : undefined}
			onResizeEnd={onResizeEnd ? dimensions => onResizeEnd(graphZone, dimensions) : undefined}
			onResizeFromPropChange={
				onResizeFromPropChange ? dimensions => onResizeFromPropChange(graphZone, dimensions) : undefined
			}
			resizeContentClassName="z1"
			resizeEdge="right"
			resizeHandleClassName="z1"
			// translate={translate}
			width={graphZone.currentWidth}
			widthConstraints={getGraphZoneWidthConstraintsFromGraphZones(graphZoneType, graphZones, graphWidth)}
		>
			{Column}
		</Resizable>
	);
}
