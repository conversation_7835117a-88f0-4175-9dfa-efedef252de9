import { expect } from 'chai';

import * as CssVariableHelpers from '../../../../src/domain/cssvariable/CssVariableHelpers';
import { DEFAULT_CSS_VARIABLES } from '../../../../src/domain/cssvariable/CssVariableConstants';

// Note: tests are running via Node runtime, meaning there are no browser APIs (like CSS used
// in "validateAndMergeCssVariablesWithDefaults"). So, I had to mock it in that way.
(global as any).CSS = {
	supports: () => true,
	escape: () => '',
};

describe('Css Variable Helpers', function () {
	describe('columnToColorPropName', function () {
		it('Should return column property by column number', function () {
			expect(CssVariableHelpers.columnToColorPropName(3)).to.equal('--column-3-color');
		});
	});

	describe('validateAndMergeCssVariablesWithDefaults', function () {
		const cssCustomVarsMock = {
			'--terminal__repo-name-color': 'red',
		};

		const cssCustomVarsResult = {
			...DEFAULT_CSS_VARIABLES,
			...cssCustomVarsMock,
		};

		const testCases = [
			{
				desc: 'Validating/merging default CSS vars',
				cssVars: DEFAULT_CSS_VARIABLES,
				result: DEFAULT_CSS_VARIABLES,
			},
			{ desc: 'Validating/merging custom CSS vars', cssVars: cssCustomVarsMock, result: cssCustomVarsResult },
		];

		testCases.forEach(testCase => {
			it(testCase.desc, function () {
				expect(CssVariableHelpers.validateAndMergeCssVariablesWithDefaults(testCase.cssVars)).to.eql(
					testCase.result,
				);
			});
		});
	});

	describe('getStyleStringFromCssVariables', function () {
		let styleStringMock = '.gk-graph {\n';
		styleStringMock += '--graph-row-height: 22px;\n';
		styleStringMock += "--font-default: 'Open Sans', Arial, sans-serif !important;\n";
		// eslint-disable-next-line max-len
		styleStringMock +=
			"--font-monospace: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;\n";
		styleStringMock += '--font-size: 62.5%;\n';
		styleStringMock += '--fs-1: 1rem;\n';
		styleStringMock += '--fs-2: 1.2rem;\n';
		styleStringMock += '--fs-3: 1.4rem;\n';
		styleStringMock += '--fs-4: 1.6rem;\n';
		styleStringMock += '--fs-5: 1.8rem;\n';
		styleStringMock += '--fs-6: 2rem;\n';
		styleStringMock += '--fs-7: 2.4rem;\n';
		styleStringMock += '--button-xs-padding-y: .15rem;\n';
		styleStringMock += '--button-xs-padding-x: .25rem;\n';
		styleStringMock += '--button-small-padding-y: .25rem;\n';
		styleStringMock += '--button-small-padding-x: .5rem;\n';
		styleStringMock += '--button-medium-padding-y: .5rem;\n';
		styleStringMock += '--button-medium-padding-x: .75rem;\n';
		styleStringMock += '--button-large-padding-y: 1rem;\n';
		styleStringMock += '--button-large-padding-x: 1.25rem;\n';
		styleStringMock += '--button-radius: .2rem;\n';
		styleStringMock += '--input-radius: 3px;\n';
		styleStringMock += '--about-modal-bg-color: #e2e4e9;\n';
		styleStringMock += '--about-modal-width: 650px;\n';
		styleStringMock += '--label__purple-color-f25: rgba(197,23,182,0.25);\n';
		styleStringMock += '--registration-modal-width: 400px;\n';
		styleStringMock += '--file-history-header-height: 35px;\n';
		styleStringMock += '--shop-modal-width: 95vw;\n';
		styleStringMock += '--shop-modal-height: 95vh;\n';
		styleStringMock += '--scrollable-scrollbar-thickness: 14px;\n';
		styleStringMock += '--mini-commit-col-left-width: 30px;\n';
		styleStringMock += '--mini-commit-col-middle-padding: 6px;\n';
		styleStringMock += '--mini-commit-col-right-width: 56px;\n';
		styleStringMock += '--fuzzy-finder-repo-color: rgba(46,206,157,0.1);\n';
		styleStringMock += '--fuzzy-finder-checkout-color: rgba(6,105,247,0.1);\n';
		styleStringMock += '--fuzzy-finder-history-color: rgba(197,23,182,0.1);\n';
		styleStringMock += '--handle-width: 7px;\n';
		styleStringMock += '--shadow-color: rgba(0,0,0,0.4);\n';
		styleStringMock += '--graph-color-5-f10: rgba(205,1,1,0.1);\n';
		styleStringMock += '--graph-color-6-f10: rgba(242,93,46,0.1);\n';
		styleStringMock += '--graph-color-7-f10: rgba(242,202,51,0.1);\n';
		styleStringMock += '--graph-color-8-f10: rgba(123,217,56,0.1);\n';
		styleStringMock += '--graph-color-9-f10: rgba(46,206,157,0.1);\n';
		styleStringMock += '--graph-color-0-f10: rgba(21,160,191,0.1);\n';
		styleStringMock += '--graph-color-1-f10: rgba(6,105,247,0.1);\n';
		styleStringMock += '--graph-color-2-f10: rgba(142,0,194,0.1);\n';
		styleStringMock += '--graph-color-3-f10: rgba(197,23,182,0.1);\n';
		styleStringMock += '--graph-color-4-f10: rgba(217,1,113,0.1);\n';
		styleStringMock += '--graph-color-5-f50: rgba(205,1,1,0.5);\n';
		styleStringMock += '--graph-color-6-f50: rgba(242,93,46,0.5);\n';
		styleStringMock += '--graph-color-7-f50: rgba(242,202,51,0.5);\n';
		styleStringMock += '--graph-color-8-f50: rgba(123,217,56,0.5);\n';
		styleStringMock += '--graph-color-9-f50: rgba(46,206,157,0.5);\n';
		styleStringMock += '--graph-color-0-f50: rgba(21,160,191,0.5);\n';
		styleStringMock += '--graph-color-1-f50: rgba(6,105,247,0.5);\n';
		styleStringMock += '--graph-color-2-f50: rgba(142,0,194,0.5);\n';
		styleStringMock += '--graph-color-3-f50: rgba(197,23,182,0.5);\n';
		styleStringMock += '--graph-color-4-f50: rgba(217,1,113,0.5);\n';
		styleStringMock += '--axo-orange: #e75225;\n';
		styleStringMock += '--axo-ink: #141422;\n';
		styleStringMock += '--axo-cream: #f9efc1;\n';
		styleStringMock += '--axo-yellow: #d8c13a;\n';
		styleStringMock += '--info-bar-height: 26px;\n';
		styleStringMock += '--left-panel-header-height: 60px;\n';
		styleStringMock += '--toolbar-height: 48px;\n';
		styleStringMock += '--toolbar-border: 1px;\n';
		styleStringMock += '--toolbar-item-height: calc(var(--toolbar-height, 0px) - var(--toolbar-border, 1px));\n';
		styleStringMock += '--expand-detail-panel-transition: flex-grow 250ms ease-in-out;\n';
		styleStringMock += '--conflict-left-color: rgba(21,160,191,0.25);\n';
		styleStringMock += '--conflict-right-color: rgba(242,202,51,0.25);\n';
		styleStringMock += '--conflict-output-color: rgba(197,23,182,0.25);\n';
		styleStringMock += '--graph-color-5-bg50: #751012;\n';
		styleStringMock += '--graph-color-6-bg50: #873e29;\n';
		styleStringMock += '--graph-color-7-bg50: #87742b;\n';
		styleStringMock += '--graph-color-8-bg50: #4c7c2e;\n';
		styleStringMock += '--graph-color-9-bg50: #257660;\n';
		styleStringMock += '--graph-color-0-bg50: #195f71;\n';
		styleStringMock += '--graph-color-1-bg50: #11448d;\n';
		styleStringMock += '--graph-color-2-bg50: #550f73;\n';
		styleStringMock += '--graph-color-3-bg50: #711b6d;\n';
		styleStringMock += '--graph-color-4-bg50: #7b104a;\n';
		styleStringMock += '--graph-color-5-bg45: #6c1114;\n';
		styleStringMock += '--graph-color-6-bg45: #7c3a28;\n';
		styleStringMock += '--graph-color-7-bg45: #7c6b2a;\n';
		styleStringMock += '--graph-color-8-bg45: #47722c;\n';
		styleStringMock += '--graph-color-9-bg45: #246d5a;\n';
		styleStringMock += '--graph-color-0-bg45: #195969;\n';
		styleStringMock += '--graph-color-1-bg45: #124082;\n';
		styleStringMock += '--graph-color-2-bg45: #4f116b;\n';
		styleStringMock += '--graph-color-3-bg45: #681b65;\n';
		styleStringMock += '--graph-color-4-bg45: #711146;\n';
		styleStringMock += '--graph-color-5-bg25: #48171b;\n';
		styleStringMock += '--graph-color-6-bg25: #522e26;\n';
		styleStringMock += '--graph-color-7-bg25: #524927;\n';
		styleStringMock += '--graph-color-8-bg25: #344d28;\n';
		styleStringMock += '--graph-color-9-bg25: #214a42;\n';
		styleStringMock += '--graph-color-0-bg25: #1a3f4a;\n';
		styleStringMock += '--graph-color-1-bg25: #173158;\n';
		styleStringMock += '--graph-color-2-bg25: #39174b;\n';
		styleStringMock += '--graph-color-3-bg25: #461c48;\n';
		styleStringMock += '--graph-color-4-bg25: #4b1737;\n';
		styleStringMock += '--graph-color-5-bg15: #371a1e;\n';
		styleStringMock += '--graph-color-6-bg15: #3c2725;\n';
		styleStringMock += '--graph-color-7-bg15: #3c3825;\n';
		styleStringMock += '--graph-color-8-bg15: #2a3a26;\n';
		styleStringMock += '--graph-color-9-bg15: #1f3835;\n';
		styleStringMock += '--graph-color-0-bg15: #1b323a;\n';
		styleStringMock += '--graph-color-1-bg15: #192943;\n';
		styleStringMock += '--graph-color-2-bg15: #2d1a3b;\n';
		styleStringMock += '--graph-color-3-bg15: #351d39;\n';
		styleStringMock += '--graph-color-4-bg15: #381a2f;\n';
		styleStringMock += '--semi-bold: 500;\n';
		styleStringMock += '--bold: 600;\n';
		styleStringMock += '--num-columns-supported: 10;\n';
		styleStringMock += '--column-0-color: #15a0bf;\n';
		styleStringMock += '--column-1-color: #0669f7;\n';
		styleStringMock += '--column-2-color: #8e00c2;\n';
		styleStringMock += '--column-3-color: #c517b6;\n';
		styleStringMock += '--column-4-color: #d90171;\n';
		styleStringMock += '--column-5-color: #cd0101;\n';
		styleStringMock += '--column-6-color: #f25d2e;\n';
		styleStringMock += '--column-7-color: #f2ca33;\n';
		styleStringMock += '--column-8-color: #7bd938;\n';
		styleStringMock += '--column-9-color: #2ece9d;\n';
		styleStringMock += '--red: #D9413D;\n';
		styleStringMock += '--orange: #DE9B43;\n';
		styleStringMock += '--yellow: #ECB91C;\n';
		styleStringMock += '--green: #5CB85C;\n';
		styleStringMock += '--blue: #4D88FF;\n';
		styleStringMock += '--ltblue: #5bc0de;\n';
		styleStringMock += '--app__bg0: #1C1E23;\n';
		styleStringMock += '--toolbar__bg0: rgb(42,45,52);\n';
		styleStringMock += '--toolbar__bg1: rgb(51,55,63);\n';
		styleStringMock += '--toolbar__bg2: rgb(65,70,80);\n';
		styleStringMock += '--panel__bg0: rgb(39,42,49);\n';
		styleStringMock += '--panel__bg1: rgb(50,54,63);\n';
		styleStringMock += '--panel__bg2: rgb(61,66,77);\n';
		styleStringMock += '--input__bg: rgba(0,0,0,0.2);\n';
		styleStringMock += '--input-bg-warn-color: rgba(222,155,67,0.6);\n';
		styleStringMock += '--panel-border: rgb(19,20,24);\n';
		styleStringMock += '--section-border: rgba(255,255,255,0.08);\n';
		styleStringMock += '--subtle-border: rgba(255,255,255,0.04);\n';
		styleStringMock += '--modal-overlay-color: rgba(0,0,0,.5);\n';
		styleStringMock += '--graph-color-0: #15a0bf;\n';
		styleStringMock += '--graph-color-1: #0669f7;\n';
		styleStringMock += '--graph-color-2: #8e00c2;\n';
		styleStringMock += '--graph-color-3: #c517b6;\n';
		styleStringMock += '--graph-color-4: #d90171;\n';
		styleStringMock += '--graph-color-5: #cd0101;\n';
		styleStringMock += '--graph-color-6: #f25d2e;\n';
		styleStringMock += '--graph-color-7: #f2ca33;\n';
		styleStringMock += '--graph-color-8: #7bd938;\n';
		styleStringMock += '--graph-color-9: #2ece9d;\n';
		styleStringMock += '--text-selected: #FFFFFF;\n';
		styleStringMock += '--text-selected-row: #FFFFFF;\n';
		styleStringMock += '--text-dimmed: rgba(255,255,255,0.2);\n';
		styleStringMock += '--text-dimmed-selected: rgba(255,255,255,0.5);\n';
		styleStringMock += '--text-normal: rgba(255,255,255,0.75);\n';
		styleStringMock += '--text-secondary: rgba(255,255,255,0.6);\n';
		styleStringMock += '--text-disabled: rgba(255,255,255,0.4);\n';
		styleStringMock += '--text-accent: #93A9EC;\n';
		styleStringMock += '--text-inverse: #222222;\n';
		styleStringMock += '--text-bright: #FFFFFF;\n';
		styleStringMock += '--btn-text: rgba(255,255,255,0.75);\n';
		styleStringMock += '--btn-text-hover: #FFFFFF;\n';
		styleStringMock += '--default-border: rgba(255,255,255,0.75);\n';
		styleStringMock += '--default-bg: transparent;\n';
		styleStringMock += '--default-hover: transparent;\n';
		styleStringMock += '--default-border-hover: #FFFFFF;\n';
		styleStringMock += '--primary-border: #4D88FF;\n';
		styleStringMock += '--primary-bg: rgba(77,136,255,0.2);\n';
		styleStringMock += '--primary-hover: rgba(77,136,255,0.6);\n';
		styleStringMock += '--success-border: #5CB85C;\n';
		styleStringMock += '--success-bg: rgba(92,184,92,0.2);\n';
		styleStringMock += '--success-hover: rgba(92,184,92,0.6);\n';
		styleStringMock += '--warning-border: #DE9B43;\n';
		styleStringMock += '--warning-bg: rgba(222,155,67,0.2);\n';
		styleStringMock += '--warning-hover: rgba(222,155,67,0.6);\n';
		styleStringMock += '--danger-border: #D9413D;\n';
		styleStringMock += '--danger-bg: rgba(217,65,61,0.2);\n';
		styleStringMock += '--danger-hover: rgba(217,65,61,0.6);\n';
		styleStringMock += '--hover-row: rgba(77,136,255,0.1);\n';
		styleStringMock += '--hover-row-border: none;\n';
		styleStringMock += '--danger-row: rgba(217,65,61,0.6);\n';
		styleStringMock += '--selected-row: rgba(77,136,255,0.2);\n';
		styleStringMock += '--selected-row-border: none;\n';
		styleStringMock += '--warning-row: rgba(222,155,67,0.6);\n';
		styleStringMock += '--droppable: rgba(236,185,28,0.3);\n';
		styleStringMock += '--drop-target: rgba(92,184,92,0.5);\n';
		styleStringMock += '--input--disabled: rgba(0,0,0,0.1);\n';
		styleStringMock += '--link-color: #40c5ec;\n';
		styleStringMock += '--link-color-bright: #40c5ec;\n';
		styleStringMock += '--form-control-focus: #4D88FF;\n';
		styleStringMock += '--scroll-thumb-border: #1C1E23;\n';
		styleStringMock += '--scroll-thumb-bg: rgb(65,70,80);\n';
		styleStringMock += '--scroll-thumb-bg-light: rgb(111,119,136);\n';
		styleStringMock += '--wip-status: rgba(77,136,255,0.4);\n';
		styleStringMock += '--card__bg: rgb(61,66,77);\n';
		styleStringMock += '--card-shadow: rgba(0,0,0,.2);\n';
		styleStringMock += '--statusbar__warning-bg: #87742b;\n';
		styleStringMock += '--label__yellow-color: #f2ca33;\n';
		styleStringMock += '--label__light-blue-color: #15a0bf;\n';
		styleStringMock += '--label__purple-color: #c517b6;\n';
		styleStringMock += '--filtering: rgba(77,136,255,0.5);\n';
		styleStringMock += '--soloing: rgba(222,155,67,0.5);\n';
		styleStringMock += '--checked-out: rgba(92,184,92,0.3);\n';
		styleStringMock += '--soloed: rgba(222,155,67,0.3);\n';
		styleStringMock += '--filter-match: rgba(77,136,255,0.5);\n';
		styleStringMock += '--clone__progress: rgba(77,136,255,0.7);\n';
		styleStringMock += '--toolbar__prompt: rgba(77,136,255,0.2);\n';
		styleStringMock += '--verified: rgba(92,184,92,0.3);\n';
		styleStringMock += '--unverified: rgba(255,255,255,0.1);\n';
		styleStringMock += '--drop-sort-border: #5CB85C;\n';
		styleStringMock += '--terminal__repo-name-color: turquoise;\n';
		styleStringMock += '--terminal__repo-branch-color: violet;\n';
		styleStringMock += '--terminal__repo-tag-color: lightcoral;\n';
		styleStringMock += '--terminal__repo-upstream-color: yellowgreen;\n';
		styleStringMock += '--terminal__background: #000000;\n';
		styleStringMock += '--terminal__cursor: #FFFFFF;\n';
		styleStringMock += '--terminal__cursorAccent: #000000;\n';
		styleStringMock += '--terminal__foreground: #FFFFFF;\n';
		styleStringMock += '--terminal__selection: #304676;\n';
		styleStringMock += '--terminal__black: #000000;\n';
		styleStringMock += '--terminal__red: #F24A4A;\n';
		styleStringMock += '--terminal__green: #0DBC79;\n';
		styleStringMock += '--terminal__yellow: #E5E510;\n';
		styleStringMock += '--terminal__blue: #4A98EE;\n';
		styleStringMock += '--terminal__magenta: #E063E0;\n';
		styleStringMock += '--terminal__cyan: #11A8CD;\n';
		styleStringMock += '--terminal__white: #E5E5E5;\n';
		styleStringMock += '--terminal__brightBlack: #686868;\n';
		styleStringMock += '--terminal__brightRed: #FF5656;\n';
		styleStringMock += '--terminal__brightGreen: #23D18B;\n';
		styleStringMock += '--terminal__brightYellow: #F5F543;\n';
		styleStringMock += '--terminal__brightBlue: #51A4FF;\n';
		styleStringMock += '--terminal__brightMagenta: #D670D6;\n';
		styleStringMock += '--terminal__brightCyan: #29B8DB;\n';
		styleStringMock += '--terminal__brightWhite: #E5E5E5;\n';
		styleStringMock += '--code-bg: #1C1E23;\n';
		styleStringMock += '--code-foreground: rgba(255,255,255,0.75);\n';
		styleStringMock += '--code-blame-color-0: #15a0bf;\n';
		styleStringMock += '--code-blame-color-1: #0669f7;\n';
		styleStringMock += '--code-blame-color-2: #8e00c2;\n';
		styleStringMock += '--code-blame-color-3: #c517b6;\n';
		styleStringMock += '--code-blame-color-4: #d90171;\n';
		styleStringMock += '--code-blame-color-5: #cd0101;\n';
		styleStringMock += '--code-blame-color-6: #f25d2e;\n';
		styleStringMock += '--code-blame-color-7: #f2ca33;\n';
		styleStringMock += '--code-blame-color-8: #7bd938;\n';
		styleStringMock += '--code-blame-color-9: #2ece9d;\n';
		styleStringMock += '--added-line: rgba(92,184,92,0.2);\n';
		styleStringMock += '--deleted-line: rgba(217,65,61,0.2);\n';
		styleStringMock += '--modified-line: rgba(0,0,0,0.25);\n';
		styleStringMock += '--conflict-info-color: #15a0bf;\n';
		styleStringMock += '--conflict-left-border-color: #15a0bf;\n';
		styleStringMock += '--conflict-right-border-color: #f2ca33;\n';
		styleStringMock += '--conflict-output-border-color: #c517b6;\n';
		styleStringMock += '--scroll-marker-head-color: #16c216;\n';
		styleStringMock += '--scroll-marker-highlights-color: #e6e62b;\n';
		styleStringMock += '--scroll-marker-local-branches-color: #ffa500;\n';
		styleStringMock += '--scroll-marker-pull-requests-color: #ff8f18;\n';
		styleStringMock += '--scroll-marker-remote-branches-color: #ffd68b;\n';
		styleStringMock += '--scroll-marker-selection-color: #0821ff;\n';
		styleStringMock += '--scroll-marker-stashes-color: #fb94ff;\n';
		styleStringMock += '--scroll-marker-tags-color: #affcff;\n';
		styleStringMock += '--scroll-marker-upstream-color: #9bff9b;\n';
		styleStringMock += '--stats-added-color: #347d39;\n';
		styleStringMock += '--stats-deleted-color: #c93c37;\n';
		styleStringMock += '--stats-files-color: #e2c08d;\n';
		styleStringMock += '--stats-bar-border-radius: 3px;\n';
		styleStringMock += '--stats-bar-height: 40%;\n';
		styleStringMock += '}\n';

		const customCssVarsMock = {
			...DEFAULT_CSS_VARIABLES,
			'--deleted-line': '#2ece9d',
			'--text-normal': '#124082',
		};

		const customStyleStringMock = styleStringMock
			.replace('--deleted-line: rgba(217,65,61,0.2);', '--deleted-line: #2ece9d;')
			.replace('--text-normal: rgba(255,255,255,0.75);', '--text-normal: #124082;');

		const testCases = [
			{
				desc: 'Check generated style using default CSS vars',
				cssVars: DEFAULT_CSS_VARIABLES,
				result: styleStringMock,
			},
			{
				desc: 'Check generated style using custom CSS vars',
				cssVars: customCssVarsMock,
				result: customStyleStringMock,
			},
		];

		testCases.forEach(testCase => {
			it(testCase.desc, function () {
				expect(CssVariableHelpers.getStyleStringFromCssVariables(testCase.cssVars)).to.equal(testCase.result);
			});
		});
	});
});
