@import (reference) '../shared/columnColors';
@import (reference) 'graphPanel';

.gk-graph {
	.ref-zone,
	&.ref-zone {
		@ref-line-margin: 11px;

		&.commit-node.has-active .ref-name {
			font-weight: 500;
		}

		&.dim-ref {
			&:hover {
				opacity: 0.65;
			}
			opacity: 0.5;
		}

		.createColorBorderClasses(@column-number) when (@column-number > 0) {
			.getColumnColors(@column-number);

			&.column-@{column-number} {
				.ref-node {
					user-select: none;

					.ref-pull-request {
						cursor: pointer;
					}

					.ref-upstream {
						cursor: pointer;
						display: flex;
						flex-direction: row;
						align-items: center;
						justify-content: center;
						font-size: 11px;
						height: 16px;
						border-radius: 6px;
						border-width: 0;
						background-color: @column-color-bg45;
						padding: 5px 5px 6px 3px;
						margin-left: 0.5rem;
					}

					.ref-upstream-behind {
						margin-left: 0.5rem;
					}

					.ref-upstream-ahead {
						margin-left: 0.5rem;
					}

					&:hover,
					&:active,
					&.is-active,
					&.hovering {
						.ref-upstream {
							background-color: @column-color-bg25;
						}
					}

					background-color: @column-color-bg25;
					color: var(--text-normal);
					border-radius: 2px;

					&:hover {
						background-color: @column-color-bg45;
					}

					&:active {
						background-color: @column-color-bg50;
						// We don't show the grabbing hand while dragging because
						// it looks strange when you double click to checkout a branch.
						// cursor: -webkit-grabbing;
					}

					&.has-active {
						font-weight: 500;
					}

					&.dim-ref {
						&:hover {
							opacity: 0.65;
						}
						opacity: 0.5;
					}

					&.is-active {
						color: var(--text-selected);
						background-color: @column-color-bg50;
					}

					&.hovering {
						box-shadow: 0 0 2px 2px var(--drop-target);
						background-color: @column-color-bg45;
					}

					&.submodule-parent {
						border-radius: 10px;
						padding: 3px 5px;
						cursor: default;
						margin-top: -1px;
						color: var(--text-selected);
						background-color: @column-color-bg50;
						.border-sides(1px solid @column-color);
						.border-tb(1px solid @column-color);
					}

					.button {
						cursor: pointer;
						padding: 0;
						margin: 0 0 0 3px;
					}

					.ref-icons {
						&.is-left-position {
							margin-right: 0.5rem;
						}

						> *:not(:last-child) {
							margin-right: 0.5rem;
						}
					}

					.ref-icon-current {
						margin-right: 0.6rem;
					}
				}

				.overflow-count {
					background-color: @column-color-bg25;
					color: var(--text-normal);
				}

				.ref-line {
					border-color: @column-color;
					opacity: 0.25;
					margin-left: 3px;

					&.is-active {
						border-width: 2px;
						opacity: 1;
					}
				}

				&.work-dir-changes,
				&.merge-conflict-node {
					.ref-node {
						color: var(--text-secondary);
						.ref-name {
							font-weight: 400;
							text-shadow: none;
						}
					}
				}
			}
			.createColorBorderClasses((@column-number - 1));
		}
		.createColorBorderClasses(@num-columns-supported);

		.ref-line {
			margin-top: @ref-line-margin;
			margin-left: 2px;
		}

		&.merge-node .ref-line {
			margin-top: (@ref-line-margin - 1px);
		}

		&.merge-conflict-node .ref-line,
		&.truncate.merge-conflict-node .ref-line {
			margin-top: (@ref-line-margin - 1px);
			position: absolute;
		}

		.ref-node-wrapper {
			&:hover {
				z-index: 2;
			}

			> span {
				font-size: var(--fs-2);
				color: white;
				vertical-align: middle;
				z-index: 5;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;

				&.truncate {
					z-index: 4;

					&.is-dummy {
						z-index: 3;
					}
				}
			}
		}

		.overflow-count {
			font-size: var(--fs-2);
			padding: 3px;
			text-align: center;
			vertical-align: middle;
			z-index: 2;
			border-radius: 2px;
		}

		.ref-avatar {
			.avatar {
				box-sizing: content-box;
			}
		}

		.form-group {
			margin-right: 5px;
		}

		input {
			height: inherit;
			line-height: initial;
			padding: 1px;
			margin-left: 3px;
		}
	}

	.create-ref {
		input {
			background-color: var(--input__bg);
			color: var(--text-normal);
			height: 28px;
			font-size: var(--fs-2);
			font-weight: 400;
		}

		.has-success input:focus {
			box-shadow: 0 0 0 1px var(--blue);
		}

		.has-error input:focus {
			border-color: var(--red);
			box-shadow: 0 0 0 1px var(--red);
		}

		span {
			font-size: var(--fs-4);
			color: #eb1f27;
		}
	}
}
