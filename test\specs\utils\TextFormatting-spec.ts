import { expect } from 'chai';

import * as TextFormatting from '../../../src/utils/TextFormatting';

describe('Text formatting', function () {
	describe('getInitialsFromName', function () {
		const testCases = [
			{ name: '<PERSON>', result: 'J<PERSON>' },
			{ name: 'JOHN SMITH', result: 'J<PERSON>' },
			{ name: 'jOhN sMItH', result: 'JS' },
			{ name: 'JOHNSMITH', result: 'J' },
			{ name: '<PERSON>', result: 'CW' },
			{ name: '<PERSON><PERSON><PERSON>', result: 'D' },
			{ name: 'miller', result: 'M' },
			{ name: "<PERSON><PERSON><PERSON>", result: 'O' },
			{ name: '', result: '?' },
		];

		testCases.forEach(testCase => {
			it(`For name '${testCase.name}', it should return '${testCase.result}'`, function () {
				expect(TextFormatting.getInitialsFromName(testCase.name)).to.equal(testCase.result);
			});
		});
	});

	describe('getNameWithEmail', function () {
		const testCases = [
			{ name: '<PERSON>', email: '<PERSON><PERSON>@gitkraken.com', result: '<PERSON> <<PERSON>.<PERSON>@gitkraken.com>' },
			{ name: '<PERSON>', result: '<PERSON>' },
			{ email: '<PERSON>.<EMAIL>', result: '<PERSON>.<EMAIL>' },
			{ result: '' },
		];

		testCases.forEach(testCase => {
			it(`For name ${testCase.name || 'not provided'} and email ${
				testCase.email || 'not provided'
			}, it should return '${testCase.result}'`, function () {
				expect(TextFormatting.getNameWithEmail(testCase.name, testCase.email)).to.equal(testCase.result);
			});
		});
	});
});
