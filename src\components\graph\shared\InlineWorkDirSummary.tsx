import type { ReactElement } from 'react';
import React from 'react';
import * as DiffConstants from '../../../domain/diff/DiffConstants';
import type { DiffCountStats } from '../../../domain/diff/DiffTypes';
import { INLINE_SUMMARY_MARGIN_LEFT } from '../../../domain/graph/GraphConstants';
import type { GetExternalIcon } from '../../../domain/graph/GraphTypes';
import type { TranslationFn } from '../../../domain/language/LanguageTypes';
import TinyFilesReadout from './TinyFilesReadout';

type Props = {
	diffStats?: DiffCountStats;
	fileNodeListStyle?: boolean;
	getExternalIcon: GetExternalIcon;
	translate: TranslationFn;
};

function InlineWorkDirSummary({
	diffStats,
	fileNodeListStyle,
	getExternalIcon,
	translate,
}: Props): ReactElement<'span'> {
	const { types: diffTypes } = DiffConstants;

	if (!diffStats) {
		return <span />;
	}

	const hasDiffSomeValues: boolean = Object.values(diffStats).some((value: any) => Boolean(value));

	if (!hasDiffSomeValues) {
		return <span />;
	}

	const maybeModifiedSummary = diffStats.modified ? (
		<TinyFilesReadout
			count={diffStats.modified}
			diffType={diffTypes.MODIFIED}
			fileNodeListStyle
			getExternalIcon={getExternalIcon}
			translate={translate}
		/>
	) : null;

	const maybeAddedSummary = diffStats.added ? (
		<TinyFilesReadout
			count={diffStats.added}
			diffType={diffTypes.ADDED}
			fileNodeListStyle
			getExternalIcon={getExternalIcon}
			translate={translate}
		/>
	) : null;

	const maybeDeletedSummary = diffStats.deleted ? (
		<TinyFilesReadout
			count={diffStats.deleted}
			diffType={diffTypes.DELETED}
			fileNodeListStyle
			getExternalIcon={getExternalIcon}
			translate={translate}
		/>
	) : null;

	const maybeRenamedSummary = diffStats.renamed ? (
		<TinyFilesReadout
			count={diffStats.renamed}
			diffType={diffTypes.RENAMED}
			fileNodeListStyle
			getExternalIcon={getExternalIcon}
			translate={translate}
		/>
	) : null;

	const maybeSummaryStyle = fileNodeListStyle ? { marginLeft: INLINE_SUMMARY_MARGIN_LEFT } : null;

	return (
		<span className="summary" style={maybeSummaryStyle}>
			{maybeModifiedSummary}
			{maybeAddedSummary}
			{maybeDeletedSummary}
			{maybeRenamedSummary}
		</span>
	);
}

export default InlineWorkDirSummary;
