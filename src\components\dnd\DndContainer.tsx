import classNames from 'classnames';
import type { <PERSON><PERSON><PERSON>O<PERSON><PERSON>, Drake } from 'dragula';
import type { ReactElement, ReactNode } from 'react';
import React from 'react';
import Dragula from 'react-dragula';

// External types

export type DndDirection = 'vertical' | 'horizontal';
export type DndIsDraggable = (source: ReactElement<any>) => boolean;
export type DndIsDroppable = (source: ReactElement<any>, target: ReactElement<any>) => boolean;
export type DndOnDrop = (source: ReactElement<any>, target: ReactElement<any> | null) => void;

export type DndContainerProps = {
	children?: ReactNode;
	className?: string;
	isDroppable?: DndIsDroppable;
	direction?: DndDirection;
	onDrop?: DndOnDrop;
	isDraggable?: DndIsDraggable;
	mirrorContainer?: Element | undefined;
};

// Internal types / constants

type DraggulaOnDrop = (el: Element, target: Element, source: Element, sibling: Element) => void;
type DraggulaIsContainer = (el?: Element) => boolean;
type DraggulaMoves = (el?: Element, container?: Element, handle?: Element, sibling?: Element) => boolean;
type DraggulaInvalid = (el?: Element, target?: Element) => boolean;
type DraggulaAccepts = (el?: Element, target?: Element, source?: Element, sibling?: Element) => boolean;

type ChildReactElementsById = { [id: string]: ReactElement<any> };

const DND_CONTAINER_CLASS = 'dnd-container';

class DndContainer extends React.PureComponent<DndContainerProps> {
	dndDrake?: Drake;
	childReactElementsById: ChildReactElementsById = {};

	constructor(props: DndContainerProps) {
		super(props);
		this.loadChildrenReactElements(this.props.children);
	}

	override componentDidMount(): void {
		// See doc to track more events here: https://github.com/bevacqua/dragula#drakeon-events
		if (this.dndDrake) {
			this.dndDrake.on('drop', this.onDrop);
		}
	}

	override componentDidUpdate(nextProps: DndContainerProps): void {
		if (this.props.children !== nextProps.children) {
			this.loadChildrenReactElements(nextProps.children);
		}
	}

	override componentWillUnmount(): void {
		if (this.dndDrake) {
			this.dndDrake.destroy();
		}
	}

	onDrop: DraggulaOnDrop = (el: Element, target?: Element, source?: Element, sibling?: Element): void => {
		if (this.props.onDrop && el.id) {
			const sanitizedId: string = this.sanitizeId(el.id);

			const sanitizedTargetId: string | null = sibling?.id ? this.sanitizeId(sibling.id) : null;

			const element: ReactElement<any> | undefined = this.childReactElementsById[sanitizedId];
			const elementTarget: ReactElement<any> | null = sanitizedTargetId
				? this.childReactElementsById[sanitizedTargetId]
				: null;

			if (element) {
				this.props.onDrop(element, elementTarget);
			}
		}
	};

	isContainerCallback: DraggulaIsContainer = (el?: Element): boolean => {
		return Boolean(el?.classList.contains(DND_CONTAINER_CLASS));
	};

	movesCallback: DraggulaMoves = (el?: Element): boolean => {
		return Boolean(el?.id) && this.isDraggable(el.id);
	};

	// Note: the sibling can be null, which would mean that the element would be placed as the last
	// element in the container. See for more details: https://github.com/bevacqua/dragula#optionsaccepts
	acceptsCallback: DraggulaAccepts = (
		el?: Element,
		target?: Element,
		source?: Element,
		sibling?: Element,
	): boolean => {
		return Boolean(el?.id) && this.isDroppable(el.id, sibling?.id);
	};

	invalidCallBack: DraggulaInvalid = (): boolean => {
		return false; // don't prevent any drags from initiating by default
	};

	dndDecorator: (instance: HTMLDivElement | null) => void = (
		componentBackingInstance: HTMLDivElement | null,
	): void => {
		if (componentBackingInstance) {
			// See doc here: https://github.com/bevacqua/dragula
			const options: DragulaOptions = {
				isContainer: this.isContainerCallback,
				mirrorContainer: this.props.mirrorContainer,
				moves: this.movesCallback,
				accepts: this.acceptsCallback,
				invalid: this.invalidCallBack,
				direction: this.props.direction || 'horizontal',
				copy: false,
				copySortSource: false,
				revertOnSpill: false,
				removeOnSpill: false,
				ignoreInputTextSelection: true,
			};

			this.dndDrake = Dragula([componentBackingInstance], options);
		}
	};

	loadChildrenReactElements(children?: ReactNode): void {
		this.childReactElementsById = {};
		if (children) {
			if (children instanceof Array) {
				children.forEach((element: any) => {
					if (React.isValidElement(element)) {
						this.addChildReactElement(element);
					}
				});
			} else if (React.isValidElement(children)) {
				this.addChildReactElement(children);
			}
		}
	}

	addChildReactElement(element: ReactElement<any>): void {
		const id: string | undefined = element.props.id;
		if (id) {
			this.childReactElementsById[this.sanitizeId(id)] = element;
		}
	}

	sanitizeId(id: string): string {
		return id.trim().toLowerCase().replace(/-/g, '').replace(/_/g, '');
	}

	isDraggable(id: string): boolean {
		if (this.props.isDraggable) {
			const sanitizedId: string = this.sanitizeId(id);
			const element: ReactElement<any> | undefined = this.childReactElementsById[sanitizedId];
			return Boolean(element) && this.props.isDraggable(element);
		}

		return false;
	}

	isDroppable(id: string, targetId?: string): boolean {
		if (this.props.isDroppable) {
			const sanitizedId: string = this.sanitizeId(id);
			const sanitizedTargetId: string | null = targetId ? this.sanitizeId(targetId) : null;

			const element: ReactElement<any> | undefined = this.childReactElementsById[sanitizedId];
			const elementTarget: ReactElement<any> | null = sanitizedTargetId
				? this.childReactElementsById[sanitizedTargetId]
				: null;

			if (element) {
				return this.props.isDroppable(element, elementTarget);
			}
		}

		return false;
	}

	override render(): ReactElement<any> {
		const { children, className } = this.props;
		const classes: string = classNames(DND_CONTAINER_CLASS, className);

		return (
			<div className={classes} ref={this.dndDecorator}>
				{children}
			</div>
		);
	}
}

export default DndContainer;
