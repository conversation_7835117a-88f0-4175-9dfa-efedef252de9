import type {
	commitNodeType,
	mergeConflictNodeType,
	mergeNodeType,
	stashNodeType,
	unsupportedRebaseWarningNodeType,
	workDirType,
} from './CommitConstants';

export type Sha = string;
export type ShortSha = string;

export type WorkDirType = typeof mergeConflictNodeType | typeof unsupportedRebaseWarningNodeType | typeof workDirType;

export type CommitType = typeof commitNodeType | typeof mergeNodeType | typeof stashNodeType | WorkDirType;

export type Author = {
	name: string;
	email: string;
};
