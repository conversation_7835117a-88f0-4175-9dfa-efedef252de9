import classnames from 'classnames';
import type { ReactElement } from 'react';
import React from 'react';
import { commitNodeType, mergeConflictNodeType, mergeNodeType } from '../../../domain/commit/CommitConstants';
import type { CommitType } from '../../../domain/commit/CommitTypes';
import type { GraphZoneModeConstants } from '../../../domain/graph/GraphTypes';

interface RefLineCommonProps {
	hasRefs: boolean;
	isActiveSha: boolean;
	type: CommitType;
}

interface RefLineProps extends RefLineCommonProps {
	left: number;
	width: string | number;
}

interface RefLineForColumnProps extends RefLineCommonProps {
	graphZoneModeConstants: GraphZoneModeConstants;
	nodeOffset: number;
}

function typeCanHaveRefLine(type: CommitType): boolean {
	return [commitNodeType, mergeNodeType, mergeConflictNodeType].includes(type);
}

function RefLine({ hasRefs, isActiveSha, left, type, width }: RefLineProps): ReactElement<any> | null {
	const className = classnames('ref-line', 'z4', { 'is-active': isActiveSha });

	return hasRefs && typeCanHaveRefLine(type) ? (
		<hr className={className} style={{ left: left, width: width }} />
	) : null;
}

export default RefLine;

export function RefLineForCommit({
	graphZoneModeConstants: { RADIUS_DIFF_MERGE_NODE_COMMIT_NODE },
	hasRefs,
	isActiveSha,
	nodeOffset,
	type,
}: RefLineForColumnProps): ReactElement<typeof RefLine> {
	return (
		<RefLine
			hasRefs={hasRefs}
			isActiveSha={isActiveSha}
			left={0}
			type={type}
			width={nodeOffset + (type === mergeNodeType ? RADIUS_DIFF_MERGE_NODE_COMMIT_NODE : 0)}
		/>
	);
}
