export interface IEvent {
	name: string;
	content: any;
}

export interface ISubscription {
	unsubscribe(): void;
}

export type EventSubscriptionCallback = (event: IEvent) => void;

export interface IEventService {
	broadcast(event: IEvent): void;
	subscribe(eventName: string, callback: EventSubscriptionCallback): ISubscription;
	unsubscribe(subscription: ISubscription): void;
}

class Observer {
	name: string;
	notify: EventSubscriptionCallback;

	constructor(name: string, callback: EventSubscriptionCallback) {
		this.name = name;
		this.notify = callback;
	}
}

type UnsubscribeCallback = (observer: Observer) => void;

class Subscription implements ISubscription {
	private observer: Observer;
	private unsubscribeCallback: UnsubscribeCallback;

	constructor(observer: Observer, unsubscribeCallback: UnsubscribeCallback) {
		this.observer = observer;
		this.unsubscribeCallback = unsubscribeCallback;
	}

	unsubscribe(): void {
		this.unsubscribeCallback(this.observer);
	}
}

class EventService implements IEventService {
	private observers: Set<Observer>;

	constructor() {
		this.observers = new Set();
	}

	broadcast(event: IEvent): void {
		if (this.observers) {
			this.observers.forEach((observer: Observer) => {
				if (event.name === observer.name) {
					observer.notify(event);
				}
			});
		}
	}

	subscribe(eventName: string, callback: EventSubscriptionCallback): ISubscription {
		const observer = new Observer(eventName, callback);
		this.observers.add(observer);

		return new Subscription(observer, observer => {
			this.observers.delete(observer);
		});
	}

	unsubscribe(subscription?: ISubscription): void {
		if (subscription) {
			subscription.unsubscribe();
		}
	}
}

export const eventService: IEventService = new EventService();
