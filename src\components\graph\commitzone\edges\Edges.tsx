import type { ReactElement } from 'react';
import React from 'react';
import type { ColumnColorByColumn, GraphZoneModeConstants, RowEdges } from '../../../../domain/graph/GraphTypes';
import { getEdgeNodes } from './EdgeNodes';

type EdgeProps = {
	columnColorByColumn: ColumnColorByColumn;
	edgeColumnMax: number;
	edges: RowEdges;
	graphZoneModeConstants: GraphZoneModeConstants;
	nodeColumn: number;
};

class Edges extends React.Component<EdgeProps> {
	override render(): ReactElement<'div'> {
		const {
			columnColorByColumn,
			edgeColumnMax,
			edges,
			nodeColumn,
			graphZoneModeConstants: {
				COMMIT_COLUMN_WIDTH,
				COMMIT_ZONE_GUTTER_WIDTH,
				COMMIT_ZONE_LINE_WIDTH,
				IS_COMPACT,
			},
		} = this.props;

		const edgeNodes = getEdgeNodes(
			edges,
			edgeColumnMax,
			nodeColumn,
			columnColorByColumn,
			COMMIT_COLUMN_WIDTH,
			COMMIT_ZONE_GUTTER_WIDTH,
			COMMIT_ZONE_LINE_WIDTH,
			IS_COMPACT,
		);

		return <div className="absolute top-0 right-0 bottom-0 left-0 z2" style={{ background: edgeNodes }} />;
	}
}

export default Edges;
