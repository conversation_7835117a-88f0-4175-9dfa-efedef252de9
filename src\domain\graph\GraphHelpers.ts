import type { Alignment } from 'react-virtualized';
import { clamp } from '../../utils/MathUtils';
import {
	commitNodeType,
	mergeConflictNodeType,
	mergeNodeType,
	stashNodeType,
	unsupportedRebaseWarningNodeType,
	workDirType,
} from '../commit/CommitConstants';
import type { CommitType, Sha } from '../commit/CommitTypes';
import { validateAndMergeCssVariablesWithDefaults } from '../cssvariable/CssVariableHelpers';
import type { CssVariables } from '../generic/GenericTypes';
import type { GetRealKeyForCellFunc } from '../reactvirtualized/ReactVirtualizedHelpers';
import { refTypes } from '../ref/RefConstants';
import type { GraphRefType } from '../ref/RefTypes';
import type { WorkDirStats } from '../workdir/WorkDirTypes';
import type { GraphMarkerType, GraphZone, GraphZoneType, TimelineEntriesByPeriod } from './GraphConstants';
import {
	changesZone,
	COMMIT_ZONE_ROW_INNER_HEIGHT,
	commitAuthorZone,
	commitDateTimeZone,
	commitMessageZone,
	commitShaZone,
	commitZone,
	GRAPH_ROW_HEIGHT,
	GraphColumnMode,
	graphZoneMetaData,
	lookbackLimitByPeriod,
	refZone,
	SCROLL_TO_ALIGNMENT_AUTO,
	SCROLL_TO_ALIGNMENT_CENTER,
} from './GraphConstants';
import type {
	BaseMetadata,
	ExternalIconKeys,
	GetMissingRefMetadata,
	GraphColumnWidthConstraints,
	GraphItemContext,
	GraphRef,
	GraphRefGroup,
	GraphRow,
	GraphZoneModeConstants,
	Head,
	HighlightedShas,
	ProcessedGraphRow,
	ProcessedGraphRowBySha,
	Ref,
	RefMetadata,
	RefMetadataById,
	RefMetadataType,
	Remote,
	RowStatsConstraints,
	Tag,
} from './GraphTypes';
import { allMetadataTypes } from './GraphTypes';
import { ProcessedGraphRowObj } from './row/ProcessedGraphRowObj';

export type DebouncableFn = (...args: any) => void;
export type DebouncedFn = (...args: any) => void;
export const debounceFrame = (func: DebouncableFn): DebouncedFn => {
	let timer: number;
	return (...args: any) => {
		if (timer) cancelAnimationFrame(timer);
		timer = requestAnimationFrame(() => {
			func(...args);
		});
	};
};

export type ThrottleableFn = (...args: any) => void;
export type ThrottledFn = (...args: any) => void;
export const throttle = (func: ThrottleableFn, time: number, initial?: number): ThrottledFn => {
	let throttled = false;
	let callTime: number | undefined;
	let now: number;
	let delay = time;
	let lastCatchTimeout: NodeJS.Timeout | null;
	return (...args: any) => {
		if (initial !== undefined) {
			now = new Date().getTime();
			if (!callTime || now - callTime > delay + initial) {
				delay = initial;
			} else {
				delay = time;
			}
		}

		if (lastCatchTimeout) {
			clearTimeout(lastCatchTimeout);
			lastCatchTimeout = null;
		}

		if (!throttled) {
			func(...args);
			callTime = new Date().getTime();
			throttled = true;
			setTimeout(() => {
				throttled = false;
			}, delay);
		} else {
			lastCatchTimeout = setTimeout(() => {
				func(...args);
			}, delay);
		}
	};
};

export function hasScrolledToBottom(htmlElement: HTMLElement): boolean {
	return Math.abs(htmlElement.scrollHeight - htmlElement.scrollTop - htmlElement.clientHeight) < 1;
}

export function hasScrolledToRight(htmlElement: HTMLElement): boolean {
	return Math.abs(htmlElement.scrollWidth - htmlElement.scrollLeft - htmlElement.clientWidth) < 1;
}

export function parseContext(context?: GraphItemContext | null): string | null {
	if (!context) {
		return null;
	}

	return typeof context === 'string' ? context : JSON.stringify(context);
}

export function getRowStatsConstraints(stats: number[]): RowStatsConstraints {
	const { length } = stats;
	if (length === 0) {
		return { min: 0, max: 0 };
	}

	if (length === 1) {
		return { min: 0, max: stats[0] };
	}

	// Sort the stats based on their magnitude (combined value of additions and deletions) if the option was set
	stats.sort((a, b) => a - b);

	const min = stats[0];
	const q1 = stats[Math.floor(length * 0.25)];
	const median = stats[Math.floor(length * 0.5)];
	const q3 = stats[Math.floor(length * 0.75)];
	const max = stats[length - 1];
	const iqr = q3 - q1;

	return {
		min: Math.max(min, median - 1.5 * iqr),
		max: Math.min(max, median + 1.5 * iqr),
	};
}

export function getOrAskForRefMetadata(
	ref: GraphRef,
	refMetadata: RefMetadataById | undefined,
	onMissingRefMetadata: GetMissingRefMetadata,
	refMetadataType?: RefMetadataType,
): RefMetadata | BaseMetadata | BaseMetadata[] | null | undefined {
	const { id } = ref;
	// Skip this ref and dont ask for anything if we are given a null along the path to our target
	if (
		!id ||
		// eslint-disable-next-line @typescript-eslint/prefer-optional-chain
		refMetadata === null ||
		refMetadata?.[id] === null ||
		(refMetadataType && refMetadata?.[id]?.[refMetadataType] === null)
	) {
		return null;
	}

	// If refMetadata exists but our id is not in it, ask for everything to be fetched
	if (!refMetadata || !(id in refMetadata)) {
		onMissingRefMetadata(id, allMetadataTypes);
		return null;
	}

	// If our entry exists but we don't have any metadata of the target type, ask for it to be fetched
	if (refMetadataType && !refMetadata[id]?.[refMetadataType]) {
		onMissingRefMetadata(id, [refMetadataType]);
		return null;
	}

	return refMetadataType ? refMetadata?.[id]?.[refMetadataType] : refMetadata?.[id];
}

export function getTimelineEntriesByPeriod(): TimelineEntriesByPeriod {
	const now = new Date();
	const timeLineEntriesByPeriod: TimelineEntriesByPeriod = {
		hour: [],
		day: [],
		week: [],
		month: [],
		year: [],
	};

	const msToHour = 1000 * 60 * 60;
	const msToDay = msToHour * 24;
	const msToWeek = msToDay * 7;

	let i: number;
	for (i = 1; i <= lookbackLimitByPeriod.hour; i += 1) {
		timeLineEntriesByPeriod.hour.push({
			date: now.getTime() - i * msToHour,
			label: i === 1 ? 'Timeline-1HourAgo' : 'Timeline-NHoursAgo',
			timeUnit: 'hour',
			value: i,
		});
	}

	for (i = 1; i <= lookbackLimitByPeriod.day; i += 1) {
		timeLineEntriesByPeriod.day.push({
			date: now.getTime() - i * msToDay,
			label: i === 1 ? 'Timeline-Yesterday' : 'Timeline-NDaysAgo',
			timeUnit: 'day',
			value: i,
		});
	}

	for (i = 1; i <= lookbackLimitByPeriod.week; i += 1) {
		timeLineEntriesByPeriod.week.push({
			date: now.getTime() - i * msToWeek,
			label: i === 1 ? 'Timeline-1WeekAgo' : 'Timeline-NWeeksAgo',
			timeUnit: 'week',
			value: i,
		});
	}

	const currentMonthDate = new Date();
	currentMonthDate.setHours(0, 0, 0, 0);
	for (i = 1; i <= lookbackLimitByPeriod.month; i += 1) {
		currentMonthDate.setMonth(currentMonthDate.getMonth() - 1);
		currentMonthDate.setHours(0, 0, 0, 0);
		timeLineEntriesByPeriod.month.push({
			date: currentMonthDate.getTime(),
			label: i === 1 ? 'Timeline-1MonthAgo' : 'Timeline-NMonthsAgo',
			timeUnit: 'month',
			value: i,
		});
	}

	const currentYearDate = new Date();
	currentYearDate.setHours(0, 0, 0, 0);
	for (i = 1; i <= lookbackLimitByPeriod.year; i += 1) {
		currentYearDate.setFullYear(currentYearDate.getFullYear() - 1);
		currentYearDate.setHours(0, 0, 0, 0);
		let label: string;
		switch (i) {
			case 1:
				label = 'Timeline-1YearAgo';
				break;
			case lookbackLimitByPeriod.year:
				label = 'Timeline-NPlusYearsAgo';
				break;
			default:
				label = 'Timeline-NYearsAgo';
				break;
		}

		timeLineEntriesByPeriod.year.push({
			date: currentYearDate.getTime(),
			label: label,
			timeUnit: 'year',
			value: i,
		});
	}

	return timeLineEntriesByPeriod;
}

export function getRefIdByBaseRef(refType: GraphRefType, ref: Ref): string {
	if (ref.id) {
		return ref.id;
	}

	const refOwner: string = (ref as any).owner || '';
	if (refType === refTypes.REMOTE) {
		return `${refType}/${refOwner}/${ref.name}`;
	}

	return `${refType}/${ref.name}`;
}

export function getRefIdByGraphRef(ref: GraphRef): string {
	return getRefIdByBaseRef(ref.refType, ref);
}

export function getLastShrinkableGraphZone(activeGraphZones: GraphZone[], stoppingIndex = 0): GraphZone | undefined {
	for (let i = activeGraphZones.length - 1; i >= stoppingIndex; i -= 1) {
		const graphZone = activeGraphZones[i];
		if (graphZone.currentWidth > graphZone.minimumWidth) {
			return graphZone;
		}
	}

	return undefined;
}

export function getFirstExpandableGraphZone(activeGraphZones: GraphZone[], startingIndex = 0): GraphZone | undefined {
	if (!activeGraphZones.length) {
		return undefined;
	}

	for (let i = startingIndex; i < activeGraphZones.length; i += 1) {
		const graphZone = activeGraphZones[i];
		const maxExpandableWidth = graphZone.maximumWidth
			? Math.min(graphZone.maximumWidth, graphZone.preferredWidth || Infinity)
			: graphZone.preferredWidth;
		if (maxExpandableWidth && graphZone.currentWidth < maxExpandableWidth) {
			return graphZone;
		}
	}

	return activeGraphZones[activeGraphZones.length - 1];
}

export function getScrollThickness(cssVariables: CssVariables, mergeDefaultCssVariables: boolean = false): number {
	const cssVarKey = '--scrollable-scrollbar-thickness';
	const cssVars =
		mergeDefaultCssVariables || !cssVariables[cssVarKey]
			? validateAndMergeCssVariablesWithDefaults(cssVariables)
			: cssVariables;
	const scrollbarThickness = cssVars[cssVarKey] ? parseInt(cssVars[cssVarKey], 10) : 0;
	return Number.isNaN(scrollbarThickness) ? 0 : scrollbarThickness;
}

export function getZoneWidthsTotal(activeGraphZones: GraphZone[], zoneTypeToExclude?: GraphZoneType): number {
	let total = 0;
	for (const graphZone of activeGraphZones) {
		if (!zoneTypeToExclude || graphZone.type !== zoneTypeToExclude) {
			total += graphZone.currentWidth;
		}
	}

	return total;
}

export function getFollowingZoneMinWidthsTotal(activeGraphZones: GraphZone[], startingIndex = 0): number {
	let total = 0;
	for (let i = startingIndex; i < activeGraphZones.length; i += 1) {
		total += activeGraphZones[i].minimumWidth;
	}

	return total;
}

export function getPrecedingZoneCurrentWidthsTotal(
	activeGraphZones: GraphZone[],
	endingIndex: number = activeGraphZones.length - 1,
): number {
	let total = 0;
	for (let i = 0; i <= endingIndex; i += 1) {
		total += activeGraphZones[i].currentWidth;
	}

	return total;
}

export const isLastColumn = (graphZoneType: GraphZoneType, activeGraphZones: GraphZone[]): boolean => {
	return graphZoneType === activeGraphZones[activeGraphZones.length - 1].type;
};

export function getMustShowMoreCommitsPanel(
	rowCount: number,
	hasMoreCommits: boolean,
	isLoadingRows: boolean,
): boolean {
	return hasMoreCommits || isLoadingRows || rowCount === 0;
}

export function getAdjustedRowCount(rowCount: number, hasMoreCommits: boolean, isLoadingRows: boolean): number {
	const mustShowMoreCommitsPanel = getMustShowMoreCommitsPanel(rowCount, hasMoreCommits, isLoadingRows);
	return rowCount + (mustShowMoreCommitsPanel ? 1 : 0);
}

export function getContentZoneHeight(rowCount: number, hasMoreCommits: boolean, isLoadingRows: boolean): number {
	const adjustedRowCount: number = getAdjustedRowCount(rowCount, hasMoreCommits, isLoadingRows);
	return GRAPH_ROW_HEIGHT * adjustedRowCount;
}

export function getContentZoneWidth(activeGraphZone: GraphZone, rowCount: number): number {
	return activeGraphZone.type === commitZone && rowCount > 0
		? activeGraphZone.contentWidth || 0
		: activeGraphZone.currentWidth;
}

export function hasHorizontalScroll(activeGraphZone: GraphZone, rowCount: number): boolean {
	return activeGraphZone.currentWidth < getContentZoneWidth(activeGraphZone, rowCount);
}

export function hasVerticalScroll(
	activeGraphZone: GraphZone,
	activeGraphZones: GraphZone[],
	hasMoreCommits: boolean,
	isLoadingRows: boolean,
	graphHeight: number,
	rowCount: number,
	isScrollMarkerEnabled: boolean,
): boolean {
	const contentZoneHeight = getContentZoneHeight(rowCount, hasMoreCommits, isLoadingRows);
	const isLastZone = isLastColumn(activeGraphZone.type, activeGraphZones);

	return isLastZone && (graphHeight < contentZoneHeight || isScrollMarkerEnabled);
}

export function getHorizontalScrollHeight(
	graphZoneType: GraphZoneType,
	activeGraphZones: GraphZone[],
	graphRows: GraphRow[],
	cssVariables: CssVariables,
	mergeDefaultCssVariables?: boolean,
): number {
	const graphZone: GraphZone | undefined = getGraphZoneFromGraphZones(graphZoneType, activeGraphZones);

	if (!graphZone) {
		return 0;
	}

	const hasHScroll = hasHorizontalScroll(graphZone, graphRows.length);

	return hasHScroll ? getScrollThickness(cssVariables, mergeDefaultCssVariables) : 0;
}

export function getVerticalScrollWidth(
	graphZoneType: GraphZoneType,
	activeGraphZones: GraphZone[],
	hasMoreCommits: boolean,
	isLoadingRows: boolean,
	graphHeight: number,
	graphRows: GraphRow[],
	isScrollMarkerEnabled: boolean,
	cssVariables: CssVariables,
	mergeDefaultCssVariables?: boolean,
): number {
	const graphZone: GraphZone | undefined = getGraphZoneFromGraphZones(graphZoneType, activeGraphZones);

	if (!graphZone) {
		return 0;
	}

	const hasVScroll = hasVerticalScroll(
		graphZone,
		activeGraphZones,
		hasMoreCommits,
		isLoadingRows,
		graphHeight,
		graphRows.length,
		isScrollMarkerEnabled,
	);

	return hasVScroll ? getScrollThickness(cssVariables, mergeDefaultCssVariables) : 0;
}

export const isColumnFollowingGraphColumn = (graphZoneType: GraphZoneType, activeGraphZones: GraphZone[]): boolean => {
	const zoneIndex = getGraphZoneIndexFromGraphZones(graphZoneType, activeGraphZones);
	const commitZoneIndex = getGraphZoneIndexFromGraphZones(commitZone, activeGraphZones);
	return zoneIndex !== -1 && commitZoneIndex !== -1 && zoneIndex - commitZoneIndex === 1;
};

export function getClampedZoneWidth(graphZone: GraphZone, activeGraphZones: GraphZone[], width: number): number {
	return clamp(
		width,
		graphZone.minimumWidth || 0,
		graphZone.maximumWidth && !isLastColumn(graphZone.type, activeGraphZones)
			? graphZone.maximumWidth || 0
			: Number.MAX_VALUE,
	);
}

export function getGraphZoneFromGraphZones(
	graphZoneType: GraphZoneType,
	activeGraphZones: GraphZone[],
): GraphZone | undefined {
	return activeGraphZones.find(({ type }: GraphZone) => type === graphZoneType);
}

export function shouldUseColumnHeaderIcons(graphZone?: GraphZone): boolean {
	return graphZone ? graphZone.currentWidth <= graphZone.showIconWidth : false;
}

export function getGraphZoneIndexFromGraphZones(graphZoneType: GraphZoneType, activeGraphZones: GraphZone[]): number {
	return activeGraphZones.findIndex(({ type }: GraphZone) => type === graphZoneType);
}

export function getGraphZoneWidthConstraintsFromGraphZones(
	graphZoneType: GraphZoneType,
	activeGraphZones: GraphZone[],
	graphWidth: number,
): GraphColumnWidthConstraints {
	let min = 0;
	let max = 0;

	// We cannot expand a graph zone beyond the total width of the graph, which would happen after all
	// zones to its right shrank to their minimum width and all zones to its left keep their current width.
	const zoneIndex: number = getGraphZoneIndexFromGraphZones(graphZoneType, activeGraphZones);

	if (zoneIndex > -1) {
		const followingZoneMinWidthsTotal: number = getFollowingZoneMinWidthsTotal(activeGraphZones, zoneIndex + 1);
		const precedingZoneCurrentWidthsTotal: number = getPrecedingZoneCurrentWidthsTotal(
			activeGraphZones,
			zoneIndex - 1,
		);
		const zoneMaximumWidth: number = activeGraphZones[zoneIndex].maximumWidth || Number.MAX_VALUE;
		const zoneConstrainedMaximumWidth: number = Math.min(
			zoneMaximumWidth,
			graphWidth - followingZoneMinWidthsTotal - precedingZoneCurrentWidthsTotal,
		);

		min = activeGraphZones[zoneIndex].minimumWidth;
		max = zoneConstrainedMaximumWidth;
	}

	return {
		min: min,
		max: max,
	};
}

export function getGraphZoneModeConstants(graphZoneMode?: GraphColumnMode): GraphZoneModeConstants {
	const isCompact = !graphZoneMode ? false : graphZoneMode === GraphColumnMode.Compact;
	const COMMIT_ZONE_LINE_WIDTH = isCompact ? 1 : 2;
	const COMMIT_ZONE_GUTTER_WIDTH = isCompact ? 10 : 28;
	const COMMIT_ZONE_PADDING_LEFT = isCompact ? 1 : 3;
	const COMMIT_ZONE_PADDING_RIGHT = isCompact ? 1 : 3;
	const COMMIT_COLUMN_WIDTH = isCompact ? 10 : 22;
	const COMMIT_NODE_DIAMETER = isCompact ? COMMIT_COLUMN_WIDTH : COMMIT_ZONE_ROW_INNER_HEIGHT;
	const COMMIT_MERGE_NODE_DIAMETER = isCompact ? COMMIT_COLUMN_WIDTH : COMMIT_COLUMN_WIDTH / 2 + 1;
	const COMMIT_ZONE_SHOW_ICON_WIDTH = isCompact ? 45 : 56;

	return {
		COMMIT_ZONE_LINE_WIDTH: COMMIT_ZONE_LINE_WIDTH,
		COMMIT_ZONE_GUTTER_WIDTH: COMMIT_ZONE_GUTTER_WIDTH,
		COMMIT_ZONE_PADDING_LEFT: COMMIT_ZONE_PADDING_LEFT,
		COMMIT_ZONE_PADDING_RIGHT: COMMIT_ZONE_PADDING_RIGHT,
		COMMIT_COLUMN_WIDTH: COMMIT_COLUMN_WIDTH,
		COMMIT_NODE_DIAMETER: COMMIT_NODE_DIAMETER,
		COMMIT_MERGE_NODE_DIAMETER: COMMIT_MERGE_NODE_DIAMETER,
		COMMIT_NODE_TOP_OFFSET: 0.5 * COMMIT_ZONE_ROW_INNER_HEIGHT - 0.5 * COMMIT_NODE_DIAMETER,
		COMMIT_MERGE_NODE_LEFT_OFFSET: COMMIT_COLUMN_WIDTH / 2 - COMMIT_MERGE_NODE_DIAMETER / 2,
		COMMIT_MERGE_NODE_TOP_OFFSET: 0.5 * COMMIT_ZONE_ROW_INNER_HEIGHT - 0.5 * COMMIT_MERGE_NODE_DIAMETER,
		COMMIT_ZONE_AVATAR_DIAMETER: COMMIT_NODE_DIAMETER - COMMIT_ZONE_LINE_WIDTH * 2,
		COMMIT_ZONE_VIEWPORT_WIDTH_MIN:
			COMMIT_NODE_DIAMETER + COMMIT_ZONE_PADDING_LEFT + COMMIT_ZONE_PADDING_RIGHT + COMMIT_ZONE_GUTTER_WIDTH,
		COMMIT_ZONE_SHOW_ICON_WIDTH: COMMIT_ZONE_SHOW_ICON_WIDTH,
		IS_COMPACT: isCompact,
		RADIUS_DIFF_MERGE_NODE_COMMIT_NODE: COMMIT_NODE_DIAMETER / 2 - COMMIT_MERGE_NODE_DIAMETER / 2,
	};
}

export const makeGetKeyForCell =
	(rows: ProcessedGraphRow[]): GetRealKeyForCellFunc =>
	(rowIndex: number): string =>
		rowIndex === rows.length ? 'SHOW_MORE_COMMITS' : rows[rowIndex].sha;

// TODO: Refactor and unify code of "getCurrentWidthByZone", "getCommitZoneWidth",
// "graphHelper.getZoneWidth and "graphHelper.getZoneWidthWithVerticalScrollbar"
// to avoid mistakes when changing the code in the future.
export function getZoneWidthWithVerticalScrollbar(graphZoneType: GraphZoneType, activeGraphZones: GraphZone[]): number {
	return (
		getGraphZoneFromGraphZones(graphZoneType, activeGraphZones)?.currentWidth ||
		graphZoneMetaData[graphZoneType].minimumWidth
	);
}

export function rowContainsCurrentHeadRef(row?: ProcessedGraphRow | GraphRow): boolean {
	if (row instanceof ProcessedGraphRowObj) {
		return Boolean(row.refsData?.activeGraphRef);
	}

	// Case of GraphRow (this only happens when processing rows at first time)
	return row?.heads ? row.heads.some((head: Head) => isHeadActive(head)) : false;
}

export function getHeadRefShaFromGraphRows(graphRows: GraphRow[]): Sha | undefined {
	const currentHeadRefRow: GraphRow | undefined = graphRows.find(rowContainsCurrentHeadRef);
	return currentHeadRefRow?.sha || undefined;
}

export function isWorkDirNodeType(nodeType: CommitType): boolean {
	return [mergeConflictNodeType, unsupportedRebaseWarningNodeType, workDirType].includes(nodeType);
}

export function isCommitNodeType(nodeType: CommitType): boolean {
	return [commitNodeType, mergeNodeType, stashNodeType].includes(nodeType);
}

export function workDirStatsHaveChanges(workDirStats?: WorkDirStats): boolean {
	if (!workDirStats) {
		return false;
	}

	return Object.values(workDirStats).some((stat: any) => stat > 0);
}

export function hasSingleSelectedSha(selectedShas: Set<Sha>): boolean {
	return selectedShas.size === 1;
}

export function isSingleSelected(selectedShas: Set<Sha>, sha: Sha): boolean {
	return selectedShas.has(sha) && hasSingleSelectedSha(selectedShas);
}

export function getScrollToAlignment(
	rowHeight: number,
	clientHeight: number,
	scrollTop: number,
	scrollToIndex: number,
): Alignment {
	if (scrollToIndex < 0) {
		return SCROLL_TO_ALIGNMENT_AUTO;
	}

	const viewBuffer: number = rowHeight + 2;
	const topViewBuffer: number = scrollTop - viewBuffer;
	const bottomViewBuffer: number = scrollTop + clientHeight + viewBuffer;

	const value: number = scrollToIndex * rowHeight;
	if (value >= topViewBuffer && value < bottomViewBuffer) {
		return SCROLL_TO_ALIGNMENT_AUTO;
	}

	return SCROLL_TO_ALIGNMENT_CENTER;
}

export function getGraphZoneIconByType(zoneType: GraphZoneType): ExternalIconKeys | undefined {
	switch (zoneType) {
		case commitZone:
			return 'graph';
		case refZone:
			return 'branch';
		case commitMessageZone:
			return 'message';
		case commitAuthorZone:
			return 'author';
		case commitDateTimeZone:
			return 'datetime';
		case commitShaZone:
			return 'commit';
		case changesZone:
			return 'changes';
		default:
			return undefined;
	}
}

export function isHeadActive(head: Head): boolean {
	return head.isCurrentHead;
}

export function isGraphRefActive(ref: GraphRef): boolean {
	return ref.refType === refTypes.HEAD && isHeadActive(ref as Head);
}

export interface GraphRowHelperProps {
	currentlyHoveredCommitSha?: Sha;
	cssVariables: CssVariables;
	dimRowsOfSelectedCommit: boolean;
	enabledScrollMarkerTypes: GraphMarkerType[];
	height: number;
	graphZones: GraphZone[];
	hasMoreCommits: boolean;
	isLoadingRows: boolean;
	highlightedShas?: HighlightedShas;
	hoveredRefGroup?: GraphRefGroup;
	selectedShas: Set<Sha>;
	processedRows: ProcessedGraphRow[];
	processedGraphRowBySha: ProcessedGraphRowBySha;
	alwaysShowTimelines?: boolean;
}

export class GraphRowHelper {
	props: GraphRowHelperProps;

	constructor(props: GraphRowHelperProps) {
		this.props = props;
	}

	getActiveGraphZone(graphZoneType: GraphZoneType): GraphZone | undefined {
		const graphZone: GraphZone | undefined = getGraphZoneFromGraphZones(graphZoneType, this.props.graphZones);
		return !graphZone || graphZone.isHidden ? undefined : graphZone;
	}

	getGraphRefGroupsByNameForRow(rowIndex: number): GraphRefGroup[] {
		const row: ProcessedGraphRow | undefined = this.getRow(rowIndex);
		if (!row) {
			return [];
		}

		return row.refsData?.orderedRefGroups || [];
	}

	getRow(rowIndex: number): ProcessedGraphRow | undefined {
		if (rowIndex < 0 || rowIndex > this.props.processedRows.length - 1) {
			return undefined;
		}

		return this.props.processedRows[rowIndex];
	}

	// TODO: Refactor and unify code of "getCurrentWidthByZone", "getCommitZoneWidth",
	// "graphHelper.getZoneWidth and "graphHelper.getZoneWidthWithVerticalScrollbar"
	// to avoid mistakes when changing the code in the future.
	getZoneWidth(graphZoneType: GraphZoneType, withoutVerticalScrollWidth: boolean = true): number {
		const verticalScrollWidth = withoutVerticalScrollWidth ? this.getVerticalScrollWidth(graphZoneType) : 0;
		return getZoneWidthWithVerticalScrollbar(graphZoneType, this.props.graphZones) - verticalScrollWidth;
	}

	isDateTimeZoneActive(): boolean {
		return Boolean(this.getActiveGraphZone(commitDateTimeZone));
	}

	shouldShowTimelines(): boolean {
		return this.props.alwaysShowTimelines || !this.isDateTimeZoneActive();
	}

	isFirstRow(rowIndex: number): boolean {
		return rowIndex === 0;
	}

	isColumnFollowingGraphColumn(graphZoneType: GraphZoneType): boolean {
		return isColumnFollowingGraphColumn(graphZoneType, this.props.graphZones);
	}

	isHighlighted(rowIndex: number): boolean {
		if (this.props.processedRows[rowIndex] && this.props.highlightedShas) {
			const sha: Sha = this.props.processedRows[rowIndex]?.sha;
			return this.props.highlightedShas[sha] || false;
		}

		return false;
	}

	isHovering(rowIndex: number): boolean {
		if (this.props.processedRows[rowIndex]) {
			return this.props.currentlyHoveredCommitSha === this.props.processedRows[rowIndex]?.sha;
		}

		return false;
	}

	isLastColumn(graphZoneType: GraphZoneType): boolean {
		return isLastColumn(graphZoneType, this.props.graphZones);
	}

	getSelectedRefGroupByName(): GraphRefGroup[] {
		if (hasSingleSelectedSha(this.props.selectedShas)) {
			const selectedSha = first(this.props.selectedShas);
			if (selectedSha) {
				const selectedRow: ProcessedGraphRow | undefined = this.props.processedGraphRowBySha[selectedSha];
				if (selectedRow) {
					return selectedRow.refsData?.orderedRefGroups || [];
				}
			}
		}

		return [];
	}

	getHorizontalScrollHeight(graphZoneType: GraphZoneType): number {
		return getHorizontalScrollHeight(
			graphZoneType,
			this.props.graphZones,
			this.props.processedRows,
			this.props.cssVariables,
		);
	}

	getVerticalScrollWidth(graphZoneType: GraphZoneType): number {
		return getVerticalScrollWidth(
			graphZoneType,
			this.props.graphZones,
			this.props.hasMoreCommits,
			this.props.isLoadingRows,
			this.props.height,
			this.props.processedRows,
			this.props.enabledScrollMarkerTypes.length > 0,
			this.props.cssVariables,
		);
	}

	shouldUseColumnHeaderIcons(graphZoneType: GraphZoneType): boolean {
		const graphZone: GraphZone | undefined = getGraphZoneFromGraphZones(graphZoneType, this.props.graphZones);
		return graphZone ? shouldUseColumnHeaderIcons(graphZone) : false;
	}

	// The result of this function will determine if a commit row should be grayed out or not when a ref group is hovered.
	isMissingHoveredRefGroup(rowIndex: number): boolean {
		if (this.props.highlightedShas) {
			return false;
		}

		const row: ProcessedGraphRow | undefined = this.getRow(rowIndex);
		if (!row) {
			return false;
		}

		let refGroup: GraphRefGroup = [];
		if (!this.props.dimRowsOfSelectedCommit) {
			const { hoveredRefGroup } = this.props;
			if (!hoveredRefGroup?.length) {
				return false;
			}

			refGroup = hoveredRefGroup;
		} else {
			const refGroupByName: GraphRefGroup[] = this.getSelectedRefGroupByName();
			if (!refGroupByName.length) {
				return false;
			}

			refGroup = refGroupByName[0];
		}

		const { name: refGroupName, refType: refGroupType } = refGroup[0];

		const isTagRefGroup: boolean = refGroupType === refTypes.TAG;
		const rowHasOnlyTags: boolean = row.tags.length > 0 && !row.heads.length && !row.remotes.length;

		// NOTE: highlighting of associated commits of a tag works differently. Only the first commit associated to that
		// hovered tag should be highlighted but if its a branch, all associated commits must be highlighted including
		// the commits with tags. Fixes issue #152.
		const { heads, tags, remotes } = !row.hasRefs || (rowHasOnlyTags && !isTagRefGroup) ? row.childRefs : row;

		if (!heads.length && !remotes.length && !tags.length) {
			return true;
		}

		const groupContainsHead = (head: Head): boolean =>
			head.name === refGroupName && refGroup.some((ref: GraphRef) => ref.refType === refTypes.HEAD);
		const groupContainsTag = (tag: Tag): boolean =>
			tag.name === refGroupName && refGroup.some((ref: GraphRef) => ref.refType === refTypes.TAG);
		const groupContainsRemote = (remote: Remote): boolean =>
			remote.name === refGroupName &&
			refGroup.some((ref: GraphRef) => ref.refType === refTypes.REMOTE && (ref as Remote).owner === remote.owner);

		return !heads.some(groupContainsHead) && !remotes.some(groupContainsRemote) && !tags.some(groupContainsTag);
	}

	isSelected(rowIndex: number): boolean {
		const sha: Sha | undefined = this.getRow(rowIndex)?.sha;
		return Boolean(sha) && this.props.selectedShas.has(sha);
	}

	isSingleSelected(rowIndex: number): boolean {
		const sha: Sha | undefined = this.getRow(rowIndex)?.sha;
		return Boolean(sha) && isSingleSelected(this.props.selectedShas, sha);
	}

	hasTimeline(rowIndex: number): boolean {
		return (
			this.shouldShowTimelines() &&
			Boolean(this.props.processedRows[rowIndex]?.timeLineEntry) &&
			!this.isFirstRow(rowIndex)
		);
	}

	rowContainsCurrentHeadRef(row?: ProcessedGraphRow): boolean {
		return rowContainsCurrentHeadRef(row);
	}
}

export function first<T>(source: Iterable<T> | IterableIterator<T>): T | undefined {
	return source[Symbol.iterator]().next().value as T | undefined;
}

export function last<T>(source: Iterable<T>): T | undefined {
	let item: T | undefined;
	for (item of source) {
		/* noop */
	}
	return item;
}
