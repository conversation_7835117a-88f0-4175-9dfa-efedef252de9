export function getInitialsFromName(name: string): string {
	const trimmedName = (name || '').trim().toUpperCase();
	if (trimmedName.length === 0) {
		return '?';
	}

	const nameParts = trimmedName.split(' ');
	if (nameParts.length === 1) {
		return nameParts[0][0];
	}

	if (nameParts.length > 1) {
		return nameParts[0][0] + nameParts[nameParts.length - 1][0];
	}

	return '?';
}

export function getNameWithEmail(name?: string, email?: string): string {
	if (name && email) {
		return `${name} <${email}>`;
	}

	if (name) {
		return name;
	}

	if (email) {
		return email;
	}

	return '';
}
